<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13" name="Get Request Number And CBE">
        <lastModified>1691916816430</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.3c9f6174-0db5-4c59-ab58-18db07b2992b</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>d592743a-313f-41a9-b862-4ca21110b0f2</guid>
        <versionId>154abbc4-880d-4892-bb07-4e009700c72b</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:6ad7eb4224455a46:11f23e39:189eae0bd83:22dd" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.0c5ec9f9-d1a5-42d1-a388-e1781993b6c0"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"ee1d24e7-d4fb-40ba-a36b-a5a575bf0f11"},{"incoming":["7e06c907-d202-439a-89f4-f4bcbeb94ede"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":870,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61c7"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"f18ae69f-33af-41c1-991d-b6b425d02bed"},{"targetRef":"3c9f6174-0db5-4c59-ab58-18db07b2992b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get CBE Sanctions","declaredType":"sequenceFlow","id":"2027.0c5ec9f9-d1a5-42d1-a388-e1781993b6c0","sourceRef":"ee1d24e7-d4fb-40ba-a36b-a5a575bf0f11"},{"startQuantity":1,"outgoing":["ef996237-8120-4633-bb95-541f6ca332c3"],"incoming":["2027.0c5ec9f9-d1a5-42d1-a388-e1781993b6c0"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":153,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Get CBE Sanctions","dataInputAssociation":[{"targetRef":"2055.b7ca2766-fba7-4fca-87a1-873a04b2291c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.idcRequest.customerInformation.CIFNumber"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"3c9f6174-0db5-4c59-ab58-18db07b2992b","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.result"]}}],"sourceRef":["2055.e55e3035-c733-4b86-8251-e07e25aafcb2"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.2bfbf2b5-2baf-4417-8c69-b2980470a0c7"]}],"calledElement":"1.cc39766e-740a-4aae-91d4-983d3f72c00c"},{"targetRef":"ffa9f72d-5c08-46d5-9396-5cf5dfeffa6c","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61bd"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Generate BPM Request Number","declaredType":"sequenceFlow","id":"ef996237-8120-4633-bb95-541f6ca332c3","sourceRef":"3c9f6174-0db5-4c59-ab58-18db07b2992b"},{"startQuantity":1,"outgoing":["329ab46a-79d0-4900-b285-65d68e511b11"],"incoming":["ef996237-8120-4633-bb95-541f6ca332c3"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":308,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Generate BPM Request Number","dataInputAssociation":[{"targetRef":"2055.37b01447-781c-4780-8505-1b49814ab727","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.idcRequest.appInfo.branch.value"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"ffa9f72d-5c08-46d5-9396-5cf5dfeffa6c","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.BPM_Request_Number"]}}],"sourceRef":["2055.268b31c4-3c29-4092-8b24-cae3868d3d09"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.07e6c003-f08c-491a-8499-6d67cbb376ff"]}],"calledElement":"1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c"},{"targetRef":"ff10e7b9-1834-4a93-8790-d6ce7a03b91d","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61b4"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Script Task","declaredType":"sequenceFlow","id":"329ab46a-79d0-4900-b285-65d68e511b11","sourceRef":"ffa9f72d-5c08-46d5-9396-5cf5dfeffa6c"},{"startQuantity":1,"outgoing":["7e06c907-d202-439a-89f4-f4bcbeb94ede"],"incoming":["bf0a541e-40c3-47e0-8d38-ad452279ce7f"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":715,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Create Folder Structure","dataInputAssociation":[{"targetRef":"2055.879cf3c6-dd65-419d-a83f-0676faf89583","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.fullPath"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"57d7e5d5-0317-4e0c-84f4-59ab3ecc0bf8","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.folderID"]}}],"sourceRef":["2055.66e460ea-ed37-4b3f-be42-b56dd24f5205"]}],"calledElement":"1.76e93686-1290-441b-87b6-9e3624b6ce34"},{"targetRef":"f18ae69f-33af-41c1-991d-b6b425d02bed","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61aa"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Get Customer Accounts2","declaredType":"sequenceFlow","id":"7e06c907-d202-439a-89f4-f4bcbeb94ede","sourceRef":"57d7e5d5-0317-4e0c-84f4-59ab3ecc0bf8"},{"startQuantity":1,"outgoing":["270408cf-aaff-4637-b47f-686d14a5db4a"],"incoming":["329ab46a-79d0-4900-b285-65d68e511b11"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":466,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Script Task","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"ff10e7b9-1834-4a93-8790-d6ce7a03b91d","scriptFormat":"text\/x-javascript","script":{"content":["if (tw.local.idcRequest.IDCRequestNature.englishdescription == \"New Request\") {\r\n\ttw.local.fullPath = tw.local.idcRequest.customerInformation.CIFNumber+\"\/DC Inward\/\"+tw.local.BPM_Request_Number+\" - \"+tw.local.idcRequest.IDCRequestType.englishdescription+\"\/Issuance\";\r\n\ttw.local.parentPath = tw.env.FILENET_ROOT_PATH+\"\/\"+tw.local.idcRequest.customerInformation.CIFNumber+\"\/DC Inward\/\"+tw.local.BPM_Request_Number+\" - \"+tw.local.idcRequest.IDCRequestType.englishdescription;\r\n\r\n}else if (tw.local.idcRequest.IDCRequestType.englishdescription == \"IDC Payment\") {\r\n\ttw.local.fullPath = tw.local.idcRequest.customerInformation.CIFNumber+\"\/DC Inward\/\"+tw.local.parentIDC.appInfo.instanceID+\" - \"+tw.local.parentIDC.IDCRequestType.englishdescription+\"\/Liquidation \"+tw.local.BPM_Request_Number;\r\n\ttw.local.parentPath = tw.env.FILENET_ROOT_PATH+\"\/\"+tw.local.idcRequest.customerInformation.CIFNumber+\"\/DC Inward\/\"+tw.local.parentIDC.appInfo.instanceID+\" - \"+tw.local.parentIDC.IDCRequestType.englishdescription;\r\n\r\n}\r\nelse{\r\n\ttw.local.fullPath = tw.local.idcRequest.customerInformation.CIFNumber+\"\/DC Inward\/\"+tw.local.parentIDC.appInfo.instanceID+\" - \"+tw.local.parentIDC.IDCRequestType.englishdescription+\"\/Amendment \"+tw.local.BPM_Request_Number;\r\n\ttw.local.parentPath = tw.env.FILENET_ROOT_PATH+\"\/\"+tw.local.idcRequest.customerInformation.CIFNumber+\"\/DC Inward\/\"+tw.local.parentIDC.appInfo.instanceID+\" - \"+tw.local.parentIDC.IDCRequestType.englishdescription;\r\n\r\n}"]}},{"targetRef":"0aa8ffda-e271-4d2d-8fb8-38ae0fe2c6a4","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Create CIF Folder","declaredType":"sequenceFlow","id":"270408cf-aaff-4637-b47f-686d14a5db4a","sourceRef":"ff10e7b9-1834-4a93-8790-d6ce7a03b91d"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.ede591cb-07bc-4bcb-83f1-369309fcf75b"},{"startQuantity":1,"outgoing":["bf0a541e-40c3-47e0-8d38-ad452279ce7f"],"incoming":["270408cf-aaff-4637-b47f-686d14a5db4a"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":593,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Create CIF Folder","dataInputAssociation":[{"targetRef":"2055.71adeef9-e810-4f45-8463-2e3b6390ee1f","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.idcRequest.customerInformation.CIFNumber"]}}]},{"targetRef":"2055.41a04eae-a175-4a73-8cb0-9cb848cba2aa","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.idcRequest.customerInformation.customerName"]}}]},{"targetRef":"2055.561bfa1f-a99e-4008-8288-671ae7cfca79","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.idcRequest.appInfo.branch.value"]}}]},{"targetRef":"2055.736854dc-22ba-47b3-802a-e3a3b05ad425","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.idcRequest.customerInformation.customerType"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"0aa8ffda-e271-4d2d-8fb8-38ae0fe2c6a4","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.2ecc7f8b-c57d-4be3-8108-0e1522ee9e0d"]}],"calledElement":"1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058"},{"targetRef":"57d7e5d5-0317-4e0c-84f4-59ab3ecc0bf8","extensionElements":{"endStateId":["guid:83e1efe624431d49:-7084ad56:189daab98ba:-26b6"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Create Folder Structure","declaredType":"sequenceFlow","id":"bf0a541e-40c3-47e0-8d38-ad452279ce7f","sourceRef":"0aa8ffda-e271-4d2d-8fb8-38ae0fe2c6a4"}],"laneSet":[{"id":"d41a5d60-be29-47db-92c5-96e915bb1385","lane":[{"flowNodeRef":["ee1d24e7-d4fb-40ba-a36b-a5a575bf0f11","f18ae69f-33af-41c1-991d-b6b425d02bed","3c9f6174-0db5-4c59-ab58-18db07b2992b","ffa9f72d-5c08-46d5-9396-5cf5dfeffa6c","57d7e5d5-0317-4e0c-84f4-59ab3ecc0bf8","ff10e7b9-1834-4a93-8790-d6ce7a03b91d","0aa8ffda-e271-4d2d-8fb8-38ae0fe2c6a4"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"77e9d8c2-1d99-4e46-9b50-5c42b8b95623","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get Request Number And CBE","declaredType":"process","id":"1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"result","isCollection":false,"id":"2055.f4a7754b-fcbe-4f1f-aaaf-65abfffad2aa"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"BPM_Request_Number","isCollection":false,"id":"2055.d22b4a18-865c-4b84-b879-26ee9e3f322f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"fullPath","isCollection":false,"id":"2055.b304617e-98f3-4070-b457-59e470497a2f"},{"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"folderID","isCollection":false,"id":"2055.77c3cc2f-ce0f-473c-8551-a2f1093abb8f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentPath","isCollection":false,"id":"2055.31e2d85c-18d8-4756-82c5-efad7879177e"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.4ae2afa0-cdd1-4d71-869e-7ea34e15f8aa"}],"inputSet":[{"dataInputRefs":["2055.9eabaff4-3e88-47e4-bbbe-5cc34cf37cb2","2055.6e39d6c4-152e-4681-9ccb-b3d2ea326441"]}],"outputSet":[{"dataOutputRefs":["2055.f4a7754b-fcbe-4f1f-aaaf-65abfffad2aa","2055.d22b4a18-865c-4b84-b879-26ee9e3f322f","2055.b304617e-98f3-4070-b457-59e470497a2f","2055.77c3cc2f-ce0f-473c-8551-a2f1093abb8f","2055.31e2d85c-18d8-4756-82c5-efad7879177e","2055.4ae2afa0-cdd1-4d71-869e-7ea34e15f8aa"]}],"dataInput":[{"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.9eabaff4-3e88-47e4-bbbe-5cc34cf37cb2"},{"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"parentIDC","isCollection":false,"id":"2055.6e39d6c4-152e-4681-9ccb-b3d2ea326441"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.9eabaff4-3e88-47e4-bbbe-5cc34cf37cb2</processParameterId>
            <processId>1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>941557b6-89c2-4775-bd08-d497f9516256</guid>
            <versionId>4825ac5b-16ba-47ed-aa3b-9a27791bb28e</versionId>
        </processParameter>
        <processParameter name="parentIDC">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.6e39d6c4-152e-4681-9ccb-b3d2ea326441</processParameterId>
            <processId>1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>c932d757-b387-4084-8986-33a7460c51bf</guid>
            <versionId>2cb57988-c9e5-4096-895d-cf0eb641887a</versionId>
        </processParameter>
        <processParameter name="result">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.f4a7754b-fcbe-4f1f-aaaf-65abfffad2aa</processParameterId>
            <processId>1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>5ca6d631-d210-472e-ba96-06b81881839d</guid>
            <versionId>a7d955a2-eb35-4af1-b50b-404dbac4909e</versionId>
        </processParameter>
        <processParameter name="BPM_Request_Number">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d22b4a18-865c-4b84-b879-26ee9e3f322f</processParameterId>
            <processId>1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>8d30e47f-91b0-48a0-9ba1-4800c99aaf5b</guid>
            <versionId>21c1e463-2af7-49bd-a9ab-946a56dca104</versionId>
        </processParameter>
        <processParameter name="fullPath">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b304617e-98f3-4070-b457-59e470497a2f</processParameterId>
            <processId>1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>975241b0-9bc8-4ae0-a4e6-dfcbf8ed6e14</guid>
            <versionId>2e92085b-8556-4df5-8fd4-0be2cc2a361a</versionId>
        </processParameter>
        <processParameter name="folderID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.77c3cc2f-ce0f-473c-8551-a2f1093abb8f</processParameterId>
            <processId>1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>c845a019-0859-49af-909c-08147efcb6e9</guid>
            <versionId>70d1fab7-7c12-42c8-973b-80b55bf78e51</versionId>
        </processParameter>
        <processParameter name="parentPath">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.31e2d85c-18d8-4756-82c5-efad7879177e</processParameterId>
            <processId>1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>7</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>e1eef97c-69d8-4ab1-a534-958b5cdea3d0</guid>
            <versionId>30aebc4d-9c9a-4926-a2e2-9a0af0ec7638</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.4ae2afa0-cdd1-4d71-869e-7ea34e15f8aa</processParameterId>
            <processId>1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>8</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>4a65467c-a11f-4264-b381-245d915eca12</guid>
            <versionId>cba87e7f-36ea-458a-a381-af28491c41a9</versionId>
        </processParameter>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ede591cb-07bc-4bcb-83f1-369309fcf75b</processVariableId>
            <description isNull="true" />
            <processId>1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>878c8598-34d3-43cc-9d73-8b33308f77a5</guid>
            <versionId>2b84f0f4-f590-4481-a779-2a06b9ceec16</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3c9f6174-0db5-4c59-ab58-18db07b2992b</processItemId>
            <processId>1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13</processId>
            <name>Get CBE Sanctions</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.9631343e-79bc-4c2a-b160-97c607624c8e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61c8</guid>
            <versionId>20192713-75a7-421d-a538-fbe2640988f9</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="153" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.9631343e-79bc-4c2a-b160-97c607624c8e</subProcessId>
                <attachedProcessRef>/1.cc39766e-740a-4aae-91d4-983d3f72c00c</attachedProcessRef>
                <guid>a0a7b8ea-83d2-48c7-8ed1-4d825c9299e1</guid>
                <versionId>2c90328d-d450-4bfe-8190-d1c6a21c1455</versionId>
                <parameterMapping name="cif">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.46206df4-66df-4541-ab34-4b125df2c196</parameterMappingId>
                    <processParameterId>2055.b7ca2766-fba7-4fca-87a1-873a04b2291c</processParameterId>
                    <parameterMappingParentId>3012.9631343e-79bc-4c2a-b160-97c607624c8e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.idcRequest.customerInformation.CIFNumber</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>d151253c-210a-451b-a94d-4a740c88ac61</guid>
                    <versionId>491279f0-e3d5-4eb9-88ca-e0f2c8894b47</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="result">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.0d77c628-8faf-4417-814b-fd2c5d69fe74</parameterMappingId>
                    <processParameterId>2055.e55e3035-c733-4b86-8251-e07e25aafcb2</processParameterId>
                    <parameterMappingParentId>3012.9631343e-79bc-4c2a-b160-97c607624c8e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.result</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>4c65e689-4bd3-4552-9f9c-060fcc644924</guid>
                    <versionId>ae637826-1f58-4d37-93a3-928b8c983da0</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="error">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a62e37fe-21a4-4277-88d2-78bd439bbde0</parameterMappingId>
                    <processParameterId>2055.2bfbf2b5-2baf-4417-8c69-b2980470a0c7</processParameterId>
                    <parameterMappingParentId>3012.9631343e-79bc-4c2a-b160-97c607624c8e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.error</value>
                    <classRef>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>f00ec461-b3ba-4e6d-b487-e8fd12c810c5</guid>
                    <versionId>fc0187c8-a288-4efd-b229-640983c35808</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ffa9f72d-5c08-46d5-9396-5cf5dfeffa6c</processItemId>
            <processId>1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13</processId>
            <name>Generate BPM Request Number</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.82c8b281-f94d-4131-aabc-c504d57e2e59</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61c6</guid>
            <versionId>2c4f6a9c-4b08-49da-acfc-80ec03418463</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="308" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.82c8b281-f94d-4131-aabc-c504d57e2e59</subProcessId>
                <attachedProcessRef>/1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c</attachedProcessRef>
                <guid>9084b95e-b02b-4898-9251-ede7692e779a</guid>
                <versionId>cde43fdb-1052-4a46-9dec-ee03569f32f6</versionId>
                <parameterMapping name="BPM_Request_Number">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.453dd4f4-68cd-4754-8924-534c9cb7b999</parameterMappingId>
                    <processParameterId>2055.268b31c4-3c29-4092-8b24-cae3868d3d09</processParameterId>
                    <parameterMappingParentId>3012.82c8b281-f94d-4131-aabc-c504d57e2e59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.BPM_Request_Number</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>3a5262a7-74cc-427a-ac62-826b44a18d04</guid>
                    <versionId>2161c2e3-50d7-404d-827f-b416e1aca67d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="branchCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ce21236e-91f3-4ccd-abde-bfd262505b64</parameterMappingId>
                    <processParameterId>2055.37b01447-781c-4780-8505-1b49814ab727</processParameterId>
                    <parameterMappingParentId>3012.82c8b281-f94d-4131-aabc-c504d57e2e59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.idcRequest.appInfo.branch.value</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>de499a04-f4b0-4da6-873f-6b5b668b789b</guid>
                    <versionId>5d1f18aa-a08c-4673-9353-6ea8aabaed6f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="error">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.2218de08-21f6-41a3-b8fe-47775196cd59</parameterMappingId>
                    <processParameterId>2055.07e6c003-f08c-491a-8499-6d67cbb376ff</processParameterId>
                    <parameterMappingParentId>3012.82c8b281-f94d-4131-aabc-c504d57e2e59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.error</value>
                    <classRef>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>46d42b93-146a-403e-984c-d89636d12901</guid>
                    <versionId>95b5f7a6-4163-46ad-998a-79fcd931bded</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ff10e7b9-1834-4a93-8790-d6ce7a03b91d</processItemId>
            <processId>1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.43356fce-fa95-49bb-8522-c9123bfe8b73</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61c5</guid>
            <versionId>3826b735-3c4c-42fe-a774-4d1480b61133</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="466" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.43356fce-fa95-49bb-8522-c9123bfe8b73</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if (tw.local.idcRequest.IDCRequestNature.englishdescription == "New Request") {&#xD;
	tw.local.fullPath = tw.local.idcRequest.customerInformation.CIFNumber+"/DC Inward/"+tw.local.BPM_Request_Number+" - "+tw.local.idcRequest.IDCRequestType.englishdescription+"/Issuance";&#xD;
	tw.local.parentPath = tw.env.FILENET_ROOT_PATH+"/"+tw.local.idcRequest.customerInformation.CIFNumber+"/DC Inward/"+tw.local.BPM_Request_Number+" - "+tw.local.idcRequest.IDCRequestType.englishdescription;&#xD;
&#xD;
}else if (tw.local.idcRequest.IDCRequestType.englishdescription == "IDC Payment") {&#xD;
	tw.local.fullPath = tw.local.idcRequest.customerInformation.CIFNumber+"/DC Inward/"+tw.local.parentIDC.appInfo.instanceID+" - "+tw.local.parentIDC.IDCRequestType.englishdescription+"/Liquidation "+tw.local.BPM_Request_Number;&#xD;
	tw.local.parentPath = tw.env.FILENET_ROOT_PATH+"/"+tw.local.idcRequest.customerInformation.CIFNumber+"/DC Inward/"+tw.local.parentIDC.appInfo.instanceID+" - "+tw.local.parentIDC.IDCRequestType.englishdescription;&#xD;
&#xD;
}&#xD;
else{&#xD;
	tw.local.fullPath = tw.local.idcRequest.customerInformation.CIFNumber+"/DC Inward/"+tw.local.parentIDC.appInfo.instanceID+" - "+tw.local.parentIDC.IDCRequestType.englishdescription+"/Amendment "+tw.local.BPM_Request_Number;&#xD;
	tw.local.parentPath = tw.env.FILENET_ROOT_PATH+"/"+tw.local.idcRequest.customerInformation.CIFNumber+"/DC Inward/"+tw.local.parentIDC.appInfo.instanceID+" - "+tw.local.parentIDC.IDCRequestType.englishdescription;&#xD;
&#xD;
}</script>
                <isRule>false</isRule>
                <guid>e3b0e00c-478d-4307-bfd6-17a6dd47df5e</guid>
                <versionId>6ccd6fd8-cac0-4719-93bd-cdbd8137fd1b</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f18ae69f-33af-41c1-991d-b6b425d02bed</processItemId>
            <processId>1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.ba5252d3-63a4-4c6b-9e28-ad566f839de1</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61c7</guid>
            <versionId>698caf27-4c78-42a8-99c9-0871aa502625</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="870" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.ba5252d3-63a4-4c6b-9e28-ad566f839de1</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>83e45d1d-5c67-413b-9d81-7044f85c2eb3</guid>
                <versionId>a83aa004-f142-4966-9bc6-79811aac89e2</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.0aa8ffda-e271-4d2d-8fb8-38ae0fe2c6a4</processItemId>
            <processId>1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13</processId>
            <name>Create CIF Folder</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.a092f3d2-113f-4d7b-8bee-cb2eb8a3572a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:83e1efe624431d49:-7084ad56:189daab98ba:-1682</guid>
            <versionId>d4678562-b601-4c2e-900a-4222e94420f2</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="593" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.a092f3d2-113f-4d7b-8bee-cb2eb8a3572a</subProcessId>
                <attachedProcessRef>/1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058</attachedProcessRef>
                <guid>17a6e61f-5ba0-47bf-bdfe-771be1706398</guid>
                <versionId>201c5241-a68b-4a4f-92c4-6e24da297c6d</versionId>
                <parameterMapping name="folderId">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ef179c54-4bf7-404c-a856-709a3dd48369</parameterMappingId>
                    <processParameterId>2055.24091ff7-f2c4-4cd3-8703-3723202bc92b</processParameterId>
                    <parameterMappingParentId>3012.a092f3d2-113f-4d7b-8bee-cb2eb8a3572a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>21bb9603-df80-4247-9339-8cf97555cb2a/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>08556f76-cd36-4e26-80aa-de4a8588b674</guid>
                    <versionId>1ac9e131-c9c6-41b2-ae05-c6f2169c15df</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="branchCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.964e9f04-2c3f-4f9a-95f7-af99f503c9a7</parameterMappingId>
                    <processParameterId>2055.561bfa1f-a99e-4008-8288-671ae7cfca79</processParameterId>
                    <parameterMappingParentId>3012.a092f3d2-113f-4d7b-8bee-cb2eb8a3572a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.idcRequest.appInfo.branch.value</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>4ec47aef-f7c4-477b-9925-59589e4be5ea</guid>
                    <versionId>8462e87f-05f5-45b5-a4f3-e726d721f08a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="error">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.33be4636-9737-490c-9125-a7319447db19</parameterMappingId>
                    <processParameterId>2055.2ecc7f8b-c57d-4be3-8108-0e1522ee9e0d</processParameterId>
                    <parameterMappingParentId>3012.a092f3d2-113f-4d7b-8bee-cb2eb8a3572a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.error</value>
                    <classRef>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>0f83fe06-ae9a-459b-955b-836596cd9e28</guid>
                    <versionId>b16d83f5-e442-4b28-8f36-f44ce3f2b9c8</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="customerName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7904ff31-a64b-498c-89e6-50bd88c89831</parameterMappingId>
                    <processParameterId>2055.41a04eae-a175-4a73-8cb0-9cb848cba2aa</processParameterId>
                    <parameterMappingParentId>3012.a092f3d2-113f-4d7b-8bee-cb2eb8a3572a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.idcRequest.customerInformation.customerName</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>ec767545-40dd-46d5-a0b0-0c3f487f3ad2</guid>
                    <versionId>ba8c0df9-b4e4-48f6-8c8f-76ad3dd0d3d3</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="CIF">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.63986914-07ad-4413-b844-290be0546ece</parameterMappingId>
                    <processParameterId>2055.71adeef9-e810-4f45-8463-2e3b6390ee1f</processParameterId>
                    <parameterMappingParentId>3012.a092f3d2-113f-4d7b-8bee-cb2eb8a3572a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.idcRequest.customerInformation.CIFNumber</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>566a1b42-950d-4f57-a4c2-eb6eacb0afe7</guid>
                    <versionId>d757b58b-6a5a-4e5e-bcc3-0984ae6cc5ca</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="customerType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3055e738-5dd3-4b75-b597-91a3dea4ff34</parameterMappingId>
                    <processParameterId>2055.736854dc-22ba-47b3-802a-e3a3b05ad425</processParameterId>
                    <parameterMappingParentId>3012.a092f3d2-113f-4d7b-8bee-cb2eb8a3572a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.idcRequest.customerInformation.customerType</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>a029a813-24e2-4596-97f9-4dd5be47acd4</guid>
                    <versionId>f57cffd9-419d-4a7b-80ef-0b0ee4c35bb0</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.57d7e5d5-0317-4e0c-84f4-59ab3ecc0bf8</processItemId>
            <processId>1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13</processId>
            <name>Create Folder Structure</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.580afed2-4abb-4c67-80f0-d39b946b85a7</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61c4</guid>
            <versionId>f2663875-900b-458b-ad45-079128ba2659</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="715" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.580afed2-4abb-4c67-80f0-d39b946b85a7</subProcessId>
                <attachedProcessRef>/1.76e93686-1290-441b-87b6-9e3624b6ce34</attachedProcessRef>
                <guid>9ed2603b-84bb-4f26-ae11-d8917af93062</guid>
                <versionId>bef88426-69d6-44dc-b60c-56bfdf5dcf40</versionId>
                <parameterMapping name="fullPath">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.84b6cefe-9e5d-4ac4-8f8c-4b752656265f</parameterMappingId>
                    <processParameterId>2055.879cf3c6-dd65-419d-a83f-0676faf89583</processParameterId>
                    <parameterMappingParentId>3012.580afed2-4abb-4c67-80f0-d39b946b85a7</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.fullPath</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>7546a645-9e00-416c-bb46-d1c0dbd893b3</guid>
                    <versionId>39ebf404-c65c-402e-899f-eee394f38425</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="error">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.de5231b0-a498-4af1-9f1d-6d796c6ac9c2</parameterMappingId>
                    <processParameterId>2055.4aab2644-61c0-458f-8b2a-d4e07480c29c</processParameterId>
                    <parameterMappingParentId>3012.580afed2-4abb-4c67-80f0-d39b946b85a7</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>94b54f49-cb1e-4cd3-91b4-66e852f380f0</guid>
                    <versionId>53c69e40-6785-492c-8055-0f8aa2a1c1f8</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="folderID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.0233222a-32ea-41a3-8709-c2faf84f5634</parameterMappingId>
                    <processParameterId>2055.66e460ea-ed37-4b3f-be42-b56dd24f5205</processParameterId>
                    <parameterMappingParentId>3012.580afed2-4abb-4c67-80f0-d39b946b85a7</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.folderID</value>
                    <classRef>21bb9603-df80-4247-9339-8cf97555cb2a/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>c0976508-293d-49fb-b943-83fde30c7c85</guid>
                    <versionId>6917e8c2-b02a-4e31-96b9-8a6914b2630a</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.3c9f6174-0db5-4c59-ab58-18db07b2992b</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get Request Number And CBE" id="1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.9eabaff4-3e88-47e4-bbbe-5cc34cf37cb2" />
                        
                        
                        <ns16:dataInput name="parentIDC" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.6e39d6c4-152e-4681-9ccb-b3d2ea326441" />
                        
                        
                        <ns16:dataOutput name="result" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.f4a7754b-fcbe-4f1f-aaaf-65abfffad2aa" />
                        
                        
                        <ns16:dataOutput name="BPM_Request_Number" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.d22b4a18-865c-4b84-b879-26ee9e3f322f" />
                        
                        
                        <ns16:dataOutput name="fullPath" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.b304617e-98f3-4070-b457-59e470497a2f" />
                        
                        
                        <ns16:dataOutput name="folderID" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.77c3cc2f-ce0f-473c-8551-a2f1093abb8f" />
                        
                        
                        <ns16:dataOutput name="parentPath" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.31e2d85c-18d8-4756-82c5-efad7879177e" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.4ae2afa0-cdd1-4d71-869e-7ea34e15f8aa" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.9eabaff4-3e88-47e4-bbbe-5cc34cf37cb2</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.6e39d6c4-152e-4681-9ccb-b3d2ea326441</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.f4a7754b-fcbe-4f1f-aaaf-65abfffad2aa</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.d22b4a18-865c-4b84-b879-26ee9e3f322f</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.b304617e-98f3-4070-b457-59e470497a2f</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.77c3cc2f-ce0f-473c-8551-a2f1093abb8f</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.31e2d85c-18d8-4756-82c5-efad7879177e</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.4ae2afa0-cdd1-4d71-869e-7ea34e15f8aa</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="d41a5d60-be29-47db-92c5-96e915bb1385">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="77e9d8c2-1d99-4e46-9b50-5c42b8b95623" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>ee1d24e7-d4fb-40ba-a36b-a5a575bf0f11</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f18ae69f-33af-41c1-991d-b6b425d02bed</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3c9f6174-0db5-4c59-ab58-18db07b2992b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ffa9f72d-5c08-46d5-9396-5cf5dfeffa6c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>57d7e5d5-0317-4e0c-84f4-59ab3ecc0bf8</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ff10e7b9-1834-4a93-8790-d6ce7a03b91d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>0aa8ffda-e271-4d2d-8fb8-38ae0fe2c6a4</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="ee1d24e7-d4fb-40ba-a36b-a5a575bf0f11">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.0c5ec9f9-d1a5-42d1-a388-e1781993b6c0</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="f18ae69f-33af-41c1-991d-b6b425d02bed">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="870" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61c7</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>7e06c907-d202-439a-89f4-f4bcbeb94ede</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="ee1d24e7-d4fb-40ba-a36b-a5a575bf0f11" targetRef="3c9f6174-0db5-4c59-ab58-18db07b2992b" name="To Get CBE Sanctions" id="2027.0c5ec9f9-d1a5-42d1-a388-e1781993b6c0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.cc39766e-740a-4aae-91d4-983d3f72c00c" name="Get CBE Sanctions" id="3c9f6174-0db5-4c59-ab58-18db07b2992b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="153" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.0c5ec9f9-d1a5-42d1-a388-e1781993b6c0</ns16:incoming>
                        
                        
                        <ns16:outgoing>ef996237-8120-4633-bb95-541f6ca332c3</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.b7ca2766-fba7-4fca-87a1-873a04b2291c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.idcRequest.customerInformation.CIFNumber</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.e55e3035-c733-4b86-8251-e07e25aafcb2</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.result</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.2bfbf2b5-2baf-4417-8c69-b2980470a0c7</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="3c9f6174-0db5-4c59-ab58-18db07b2992b" targetRef="ffa9f72d-5c08-46d5-9396-5cf5dfeffa6c" name="To Generate BPM Request Number" id="ef996237-8120-4633-bb95-541f6ca332c3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61bd</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.16e2cdd0-aac9-42ab-a4de-fcf8895b3c3c" name="Generate BPM Request Number" id="ffa9f72d-5c08-46d5-9396-5cf5dfeffa6c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="308" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>ef996237-8120-4633-bb95-541f6ca332c3</ns16:incoming>
                        
                        
                        <ns16:outgoing>329ab46a-79d0-4900-b285-65d68e511b11</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.37b01447-781c-4780-8505-1b49814ab727</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.idcRequest.appInfo.branch.value</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.268b31c4-3c29-4092-8b24-cae3868d3d09</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.BPM_Request_Number</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.07e6c003-f08c-491a-8499-6d67cbb376ff</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="ffa9f72d-5c08-46d5-9396-5cf5dfeffa6c" targetRef="ff10e7b9-1834-4a93-8790-d6ce7a03b91d" name="To Script Task" id="329ab46a-79d0-4900-b285-65d68e511b11">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61b4</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.76e93686-1290-441b-87b6-9e3624b6ce34" name="Create Folder Structure" id="57d7e5d5-0317-4e0c-84f4-59ab3ecc0bf8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="715" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>bf0a541e-40c3-47e0-8d38-ad452279ce7f</ns16:incoming>
                        
                        
                        <ns16:outgoing>7e06c907-d202-439a-89f4-f4bcbeb94ede</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.879cf3c6-dd65-419d-a83f-0676faf89583</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.fullPath</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.66e460ea-ed37-4b3f-be42-b56dd24f5205</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.folderID</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="57d7e5d5-0317-4e0c-84f4-59ab3ecc0bf8" targetRef="f18ae69f-33af-41c1-991d-b6b425d02bed" name="To Get Customer Accounts2" id="7e06c907-d202-439a-89f4-f4bcbeb94ede">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61aa</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="ff10e7b9-1834-4a93-8790-d6ce7a03b91d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="466" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>329ab46a-79d0-4900-b285-65d68e511b11</ns16:incoming>
                        
                        
                        <ns16:outgoing>270408cf-aaff-4637-b47f-686d14a5db4a</ns16:outgoing>
                        
                        
                        <ns16:script>if (tw.local.idcRequest.IDCRequestNature.englishdescription == "New Request") {&#xD;
	tw.local.fullPath = tw.local.idcRequest.customerInformation.CIFNumber+"/DC Inward/"+tw.local.BPM_Request_Number+" - "+tw.local.idcRequest.IDCRequestType.englishdescription+"/Issuance";&#xD;
	tw.local.parentPath = tw.env.FILENET_ROOT_PATH+"/"+tw.local.idcRequest.customerInformation.CIFNumber+"/DC Inward/"+tw.local.BPM_Request_Number+" - "+tw.local.idcRequest.IDCRequestType.englishdescription;&#xD;
&#xD;
}else if (tw.local.idcRequest.IDCRequestType.englishdescription == "IDC Payment") {&#xD;
	tw.local.fullPath = tw.local.idcRequest.customerInformation.CIFNumber+"/DC Inward/"+tw.local.parentIDC.appInfo.instanceID+" - "+tw.local.parentIDC.IDCRequestType.englishdescription+"/Liquidation "+tw.local.BPM_Request_Number;&#xD;
	tw.local.parentPath = tw.env.FILENET_ROOT_PATH+"/"+tw.local.idcRequest.customerInformation.CIFNumber+"/DC Inward/"+tw.local.parentIDC.appInfo.instanceID+" - "+tw.local.parentIDC.IDCRequestType.englishdescription;&#xD;
&#xD;
}&#xD;
else{&#xD;
	tw.local.fullPath = tw.local.idcRequest.customerInformation.CIFNumber+"/DC Inward/"+tw.local.parentIDC.appInfo.instanceID+" - "+tw.local.parentIDC.IDCRequestType.englishdescription+"/Amendment "+tw.local.BPM_Request_Number;&#xD;
	tw.local.parentPath = tw.env.FILENET_ROOT_PATH+"/"+tw.local.idcRequest.customerInformation.CIFNumber+"/DC Inward/"+tw.local.parentIDC.appInfo.instanceID+" - "+tw.local.parentIDC.IDCRequestType.englishdescription;&#xD;
&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="ff10e7b9-1834-4a93-8790-d6ce7a03b91d" targetRef="0aa8ffda-e271-4d2d-8fb8-38ae0fe2c6a4" name="To Create CIF Folder" id="270408cf-aaff-4637-b47f-686d14a5db4a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.ede591cb-07bc-4bcb-83f1-369309fcf75b" />
                    
                    
                    <ns16:callActivity calledElement="1.a1e8f4f0-cbdc-4bcf-8b29-71b9b2096058" name="Create CIF Folder" id="0aa8ffda-e271-4d2d-8fb8-38ae0fe2c6a4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="593" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>270408cf-aaff-4637-b47f-686d14a5db4a</ns16:incoming>
                        
                        
                        <ns16:outgoing>bf0a541e-40c3-47e0-8d38-ad452279ce7f</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.71adeef9-e810-4f45-8463-2e3b6390ee1f</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.idcRequest.customerInformation.CIFNumber</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.41a04eae-a175-4a73-8cb0-9cb848cba2aa</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.idcRequest.customerInformation.customerName</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.561bfa1f-a99e-4008-8288-671ae7cfca79</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.idcRequest.appInfo.branch.value</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.736854dc-22ba-47b3-802a-e3a3b05ad425</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.idcRequest.customerInformation.customerType</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.2ecc7f8b-c57d-4be3-8108-0e1522ee9e0d</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="0aa8ffda-e271-4d2d-8fb8-38ae0fe2c6a4" targetRef="57d7e5d5-0317-4e0c-84f4-59ab3ecc0bf8" name="To Create Folder Structure" id="bf0a541e-40c3-47e0-8d38-ad452279ce7f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:endStateId>guid:83e1efe624431d49:-7084ad56:189daab98ba:-26b6</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Create Folder Structure">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.bf0a541e-40c3-47e0-8d38-ad452279ce7f</processLinkId>
            <processId>1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.0aa8ffda-e271-4d2d-8fb8-38ae0fe2c6a4</fromProcessItemId>
            <endStateId>guid:83e1efe624431d49:-7084ad56:189daab98ba:-26b6</endStateId>
            <toProcessItemId>2025.57d7e5d5-0317-4e0c-84f4-59ab3ecc0bf8</toProcessItemId>
            <guid>bebb372f-aca2-4f78-aff4-382fa8aaa54a</guid>
            <versionId>1618bd09-dea3-41d2-9f70-562d28503925</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.0aa8ffda-e271-4d2d-8fb8-38ae0fe2c6a4</fromProcessItemId>
            <toProcessItemId>2025.57d7e5d5-0317-4e0c-84f4-59ab3ecc0bf8</toProcessItemId>
        </link>
        <link name="To Script Task">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.329ab46a-79d0-4900-b285-65d68e511b11</processLinkId>
            <processId>1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.ffa9f72d-5c08-46d5-9396-5cf5dfeffa6c</fromProcessItemId>
            <endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61b4</endStateId>
            <toProcessItemId>2025.ff10e7b9-1834-4a93-8790-d6ce7a03b91d</toProcessItemId>
            <guid>074e6681-d2a3-419d-bd19-03c36ce39efe</guid>
            <versionId>25833a07-f352-475a-adde-a809f797667d</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.ffa9f72d-5c08-46d5-9396-5cf5dfeffa6c</fromProcessItemId>
            <toProcessItemId>2025.ff10e7b9-1834-4a93-8790-d6ce7a03b91d</toProcessItemId>
        </link>
        <link name="To Get Customer Accounts2">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.7e06c907-d202-439a-89f4-f4bcbeb94ede</processLinkId>
            <processId>1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.57d7e5d5-0317-4e0c-84f4-59ab3ecc0bf8</fromProcessItemId>
            <endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61aa</endStateId>
            <toProcessItemId>2025.f18ae69f-33af-41c1-991d-b6b425d02bed</toProcessItemId>
            <guid>91872d09-4d17-4940-ab62-010fb9a0148a</guid>
            <versionId>3b2c2756-f8bd-4704-83c3-d06f1a71156d</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.57d7e5d5-0317-4e0c-84f4-59ab3ecc0bf8</fromProcessItemId>
            <toProcessItemId>2025.f18ae69f-33af-41c1-991d-b6b425d02bed</toProcessItemId>
        </link>
        <link name="To Generate BPM Request Number">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.ef996237-8120-4633-bb95-541f6ca332c3</processLinkId>
            <processId>1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.3c9f6174-0db5-4c59-ab58-18db07b2992b</fromProcessItemId>
            <endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61bd</endStateId>
            <toProcessItemId>2025.ffa9f72d-5c08-46d5-9396-5cf5dfeffa6c</toProcessItemId>
            <guid>2b81d161-4002-4f01-bc13-9d0c94c70a83</guid>
            <versionId>e9d88995-7aba-4378-99bf-289851d5225b</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.3c9f6174-0db5-4c59-ab58-18db07b2992b</fromProcessItemId>
            <toProcessItemId>2025.ffa9f72d-5c08-46d5-9396-5cf5dfeffa6c</toProcessItemId>
        </link>
        <link name="To Create CIF Folder">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.270408cf-aaff-4637-b47f-686d14a5db4a</processLinkId>
            <processId>1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.ff10e7b9-1834-4a93-8790-d6ce7a03b91d</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.0aa8ffda-e271-4d2d-8fb8-38ae0fe2c6a4</toProcessItemId>
            <guid>db1fa063-5f20-44c5-886d-53ac641b4027</guid>
            <versionId>f6deb0c1-e6ef-4731-84c7-323417b46c91</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.ff10e7b9-1834-4a93-8790-d6ce7a03b91d</fromProcessItemId>
            <toProcessItemId>2025.0aa8ffda-e271-4d2d-8fb8-38ae0fe2c6a4</toProcessItemId>
        </link>
    </process>
</teamworks>

