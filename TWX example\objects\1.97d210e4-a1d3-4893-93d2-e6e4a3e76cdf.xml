<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf" name="Get BIC Codes">
        <lastModified>1692540528653</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.02762925-9cb8-44af-bb83-98c369e8336e</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>c2b3d243-d848-4b91-bca7-ecdeb7382f22</guid>
        <versionId>1e6e6ff5-ed7e-4182-adc9-529fba296f72</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:6133" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.3f480f41-e6cb-4ee9-a4cc-12e8d17e7031"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"b3b7863a-03bb-44d3-aebb-32367b3a575b"},{"incoming":["8647027f-64a3-4fc6-8f79-e27bc8775ae7","47b462ce-fa08-415f-8dbd-49a00128172d"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-612e"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"21906b37-9c7b-469e-a7a9-761aa8547510"},{"targetRef":"02762925-9cb8-44af-bb83-98c369e8336e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get BIC codes","declaredType":"sequenceFlow","id":"2027.3f480f41-e6cb-4ee9-a4cc-12e8d17e7031","sourceRef":"b3b7863a-03bb-44d3-aebb-32367b3a575b"},{"startQuantity":1,"outgoing":["d70652fc-1711-437a-8af6-1034582dca14"],"incoming":["2027.3f480f41-e6cb-4ee9-a4cc-12e8d17e7031"],"extensionElements":{"postAssignmentScript":["if (!tw.local.isSuccessful) {\r\n\ttw.local.results = new tw.object.listOf.String();\r\n}"],"nodeVisualInfo":[{"width":95,"x":152,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[],"activityType":["CalledProcess"]},"name":"Get BIC codes","dataInputAssociation":[{"targetRef":"2055.4ad7fd40-8451-480a-840d-e4350d23f2ba","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.local.data"]}}]},{"targetRef":"2055.1094baed-e916-4772-8bd8-91a41441f7ad","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.a5891acc-745c-47e0-825a-7e040254d8ec","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user_id"]}}]},{"targetRef":"2055.034a46fd-24a2-4243-84ce-38fb46ffc959","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.currentProcessInstanceID"]}}]},{"targetRef":"2055.*************-4d10-8b61-5e94d3ad31bd","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"INWARD DOCUMENTARY COLLECTION\""]}}]},{"targetRef":"2055.ce5a4152-49c6-4bf0-8ad0-af9afb12ebf0","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.21defa96-4307-47f8-8cb7-e0bc7234481b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"02762925-9cb8-44af-bb83-98c369e8336e","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.bb8dd17c-590d-440f-8545-364fdb26328e"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorCode"]}}],"sourceRef":["2055.3dfd9149-fb93-49c0-8d6c-3bb6a31c868b"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.390182d1-c178-4449-8178-edd289314ab2"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.9a271b13-fe7a-4bc9-8573-99028a4ff122"]}],"calledElement":"1.12285efa-55e1-4b26-a3e7-0b8ec7cb6f62"},{"targetRef":"6168376b-5d85-4f59-8812-930901fcdfc8","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:4a4dc2e3dad408dc:-15ab7c7b:188a9fa6c36:6b9a"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To is Successful","declaredType":"sequenceFlow","id":"d70652fc-1711-437a-8af6-1034582dca14","sourceRef":"02762925-9cb8-44af-bb83-98c369e8336e"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"bicCodes","isCollection":true,"declaredType":"dataObject","id":"2056.da42d11b-a102-4e87-98a1-e1d44bcc46ac"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"instanceID","isCollection":false,"declaredType":"dataObject","id":"2056.7a09c005-608f-487a-87a4-015a093a7fbc"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"prefix","isCollection":false,"declaredType":"dataObject","id":"2056.5aeeddc8-ef00-4379-8fec-e9f4f5f448aa"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"processName","isCollection":false,"declaredType":"dataObject","id":"2056.0419b17c-e0e0-4022-8cd4-07b05f02fd06"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestAppID","isCollection":false,"declaredType":"dataObject","id":"2056.*************-400c-865f-37fe89dd7a4f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"snapshot","isCollection":false,"declaredType":"dataObject","id":"2056.2940f60d-769b-45ac-8203-877b9498c200"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"userID","isCollection":false,"declaredType":"dataObject","id":"2056.fea8b310-6d3d-480e-80a8-6106aaa835ae"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.95130af3-b957-4c8f-80e0-50a161742c50"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.3901d6eb-8c99-476f-89de-7798b53aac80"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.9eee72e3-98a1-4d23-8e34-a3d044139e65"},{"parallelMultiple":false,"outgoing":["a0c88492-96e8-4ccd-8b11-22fa3e75a9c7"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"965c19aa-1010-442f-82db-71092d7ba07f"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"d1b86337-cb3a-4135-840f-731da30253f8","otherAttributes":{"eventImplId":"c9fd71fd-b2db-4fb0-88b3-4ec72bb0a903"}}],"attachedToRef":"02762925-9cb8-44af-bb83-98c369e8336e","extensionElements":{"nodeVisualInfo":[{"width":24,"x":187,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"4c0148fe-c368-4d49-8289-b2bb91dc0300","outputSet":{}},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.c9280472-0a58-4b18-8f01-a558cfc54f55"},{"outgoing":["8647027f-64a3-4fc6-8f79-e27bc8775ae7","a0fbd43d-bc90-4b23-8d54-8742e16865c1"],"incoming":["d70652fc-1711-437a-8af6-1034582dca14"],"default":"8647027f-64a3-4fc6-8f79-e27bc8775ae7","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":336,"y":76,"declaredType":"TNodeVisualInfo","height":32}]},"name":"is Successful","declaredType":"exclusiveGateway","id":"6168376b-5d85-4f59-8812-930901fcdfc8"},{"targetRef":"21906b37-9c7b-469e-a7a9-761aa8547510","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To Map data","declaredType":"sequenceFlow","id":"8647027f-64a3-4fc6-8f79-e27bc8775ae7","sourceRef":"6168376b-5d85-4f59-8812-930901fcdfc8"},{"targetRef":"5e47d917-a48f-4e1a-8d82-edcec1d432f1","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.isSuccessful\t  ==\t  false"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End Event","declaredType":"sequenceFlow","id":"a0fbd43d-bc90-4b23-8d54-8742e16865c1","sourceRef":"6168376b-5d85-4f59-8812-930901fcdfc8"},{"startQuantity":1,"outgoing":["47b462ce-fa08-415f-8dbd-49a00128172d"],"incoming":["a0fbd43d-bc90-4b23-8d54-8742e16865c1","a0c88492-96e8-4ccd-8b11-22fa3e75a9c7"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":321,"y":182,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"5e47d917-a48f-4e1a-8d82-edcec1d432f1","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"targetRef":"5e47d917-a48f-4e1a-8d82-edcec1d432f1","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"a0c88492-96e8-4ccd-8b11-22fa3e75a9c7","sourceRef":"4c0148fe-c368-4d49-8289-b2bb91dc0300"},{"targetRef":"21906b37-9c7b-469e-a7a9-761aa8547510","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"47b462ce-fa08-415f-8dbd-49a00128172d","sourceRef":"5e47d917-a48f-4e1a-8d82-edcec1d432f1"}],"laneSet":[{"id":"2ed33d78-a529-48a7-b51e-3c1e5010d98c","lane":[{"flowNodeRef":["b3b7863a-03bb-44d3-aebb-32367b3a575b","21906b37-9c7b-469e-a7a9-761aa8547510","02762925-9cb8-44af-bb83-98c369e8336e","4c0148fe-c368-4d49-8289-b2bb91dc0300","6168376b-5d85-4f59-8812-930901fcdfc8","5e47d917-a48f-4e1a-8d82-edcec1d432f1"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"261e9ca5-a033-4934-acb3-3db9c2629ff1","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get BIC Codes","declaredType":"process","id":"1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.7ab8d2cd-a58f-40d9-8058-650d487cb525"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.a0d503bd-a591-43f1-8c98-1bd7d1707fd9"}],"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\/\/\"06948425\"\r\n\"06316421\""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.a11ca880-4f46-4c08-a3b2-7075a4343285"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a11ca880-4f46-4c08-a3b2-7075a4343285</processParameterId>
            <processId>1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>//"06948425"&#xD;
"06316421"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>de390733-dde1-425a-9703-1b9432157eea</guid>
            <versionId>a1463146-044f-4fd7-8a0b-e1772dafa182</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.7ab8d2cd-a58f-40d9-8058-650d487cb525</processParameterId>
            <processId>1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>206ea58e-7352-4966-95b8-792a168eb591</guid>
            <versionId>73857c5e-25f2-4c4b-91e7-c8c8e8ecfef0</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a0d503bd-a591-43f1-8c98-1bd7d1707fd9</processParameterId>
            <processId>1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>ad08c574-8db1-43a0-bf4d-b5888e342197</guid>
            <versionId>db8c5388-1112-45d3-8fee-4b8b06655fa3</versionId>
        </processParameter>
        <processVariable name="bicCodes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.da42d11b-a102-4e87-98a1-e1d44bcc46ac</processVariableId>
            <description isNull="true" />
            <processId>1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>596a7543-7157-45fe-9551-20103f784533</guid>
            <versionId>5c60067a-78cb-4aa9-a658-24e950b046e6</versionId>
        </processVariable>
        <processVariable name="instanceID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7a09c005-608f-487a-87a4-015a093a7fbc</processVariableId>
            <description isNull="true" />
            <processId>1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b091116c-1574-4a9a-affa-b611b9f9707e</guid>
            <versionId>acfd8ded-eadf-4e66-956f-05e0262c27f2</versionId>
        </processVariable>
        <processVariable name="prefix">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5aeeddc8-ef00-4379-8fec-e9f4f5f448aa</processVariableId>
            <description isNull="true" />
            <processId>1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>48f1e78d-d921-41e4-922b-3fffc4511c78</guid>
            <versionId>87740f8e-d55d-46f6-93d5-57071bedad86</versionId>
        </processVariable>
        <processVariable name="processName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0419b17c-e0e0-4022-8cd4-07b05f02fd06</processVariableId>
            <description isNull="true" />
            <processId>1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6a0899f3-cff2-46d1-a435-b6ad04ab7350</guid>
            <versionId>991903c0-29a2-4abb-b906-fd66c787db72</versionId>
        </processVariable>
        <processVariable name="requestAppID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.*************-400c-865f-37fe89dd7a4f</processVariableId>
            <description isNull="true" />
            <processId>1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>01fba538-0651-4e97-b6c7-7614a853b85f</guid>
            <versionId>ed17a39a-cf0f-4b1d-8515-03014b8ee92b</versionId>
        </processVariable>
        <processVariable name="snapshot">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2940f60d-769b-45ac-8203-877b9498c200</processVariableId>
            <description isNull="true" />
            <processId>1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0d0d521f-4645-4f03-bb4c-f235bfcdb585</guid>
            <versionId>71dc387e-ea19-4028-9927-68a713b9f5af</versionId>
        </processVariable>
        <processVariable name="userID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.fea8b310-6d3d-480e-80a8-6106aaa835ae</processVariableId>
            <description isNull="true" />
            <processId>1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b5eb58c7-f507-4b05-a6d5-c37892b77e6c</guid>
            <versionId>8c14c1f1-428f-4faf-b9fc-ca8f529681fd</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.95130af3-b957-4c8f-80e0-50a161742c50</processVariableId>
            <description isNull="true" />
            <processId>1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a5b7f35e-2443-4d8a-9f90-192bf1deb8f9</guid>
            <versionId>ba31f31a-70a1-411a-b9c3-c4104686b243</versionId>
        </processVariable>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3901d6eb-8c99-476f-89de-7798b53aac80</processVariableId>
            <description isNull="true" />
            <processId>1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8ccbda46-f3c6-46cf-b51d-b591c93e09c9</guid>
            <versionId>90e81e88-b8c1-4089-b2bb-c837300cfe89</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9eee72e3-98a1-4d23-8e34-a3d044139e65</processVariableId>
            <description isNull="true" />
            <processId>1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>cd074e89-0412-4662-bd97-de8572452daa</guid>
            <versionId>7ebeff67-dbbe-4f90-b42a-1f16559dca3e</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c9280472-0a58-4b18-8f01-a558cfc54f55</processVariableId>
            <description isNull="true" />
            <processId>1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>79c39cb8-9f66-466f-9238-7da9fa10f580</guid>
            <versionId>40acf866-81f2-4a77-be59-4987e46a8088</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.02762925-9cb8-44af-bb83-98c369e8336e</processItemId>
            <processId>1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf</processId>
            <name>Get BIC codes</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.bad1b796-e3db-4b64-8bf1-75482408ff45</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.5e47d917-a48f-4e1a-8d82-edcec1d432f1</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-612f</guid>
            <versionId>0fa4eb28-87f5-402d-b0ee-9ad13a88ff2f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.db4daac4-caa5-41fc-a1cc-7474e108661f</processItemPrePostId>
                <processItemId>2025.02762925-9cb8-44af-bb83-98c369e8336e</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>bad57bc3-ead3-4ab7-b1af-261bfbdd3fb8</guid>
                <versionId>05bf6727-1439-48ac-ba22-23dcb4174919</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.*************-4de6-b41a-b75c4c5847c6</processItemPrePostId>
                <processItemId>2025.02762925-9cb8-44af-bb83-98c369e8336e</processItemId>
                <location>2</location>
                <script>if (!tw.local.isSuccessful) {&#xD;
	tw.local.results = new tw.object.listOf.String();&#xD;
}</script>
                <guid>4e66d52b-3741-41a4-a91b-0193d561867e</guid>
                <versionId>2e326df3-9446-4aeb-897b-909e032cc0b2</versionId>
            </processPrePosts>
            <layoutData x="152" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e0a</errorHandlerItem>
                <errorHandlerItemId>2025.5e47d917-a48f-4e1a-8d82-edcec1d432f1</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.bad1b796-e3db-4b64-8bf1-75482408ff45</subProcessId>
                <attachedProcessRef>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/1.12285efa-55e1-4b26-a3e7-0b8ec7cb6f62</attachedProcessRef>
                <guid>07629554-9b54-4a63-8af4-29cfdd93b630</guid>
                <versionId>c2fdd0d2-78ac-4659-874c-3a78c0a1a541</versionId>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.df833d33-c3ca-4635-b9b2-8fb9b9d79562</parameterMappingId>
                    <processParameterId>2055.3dfd9149-fb93-49c0-8d6c-3bb6a31c868b</processParameterId>
                    <parameterMappingParentId>3012.bad1b796-e3db-4b64-8bf1-75482408ff45</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>38a12687-5413-41b9-80b1-aa1806c4c116</guid>
                    <versionId>36eed0f1-261c-4a30-b6ce-a7c792a08e0c</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b28b72b8-b3f9-42e1-b828-9951f9fef445</parameterMappingId>
                    <processParameterId>2055.1094baed-e916-4772-8bd8-91a41441f7ad</processParameterId>
                    <parameterMappingParentId>3012.bad1b796-e3db-4b64-8bf1-75482408ff45</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>d1efdf5b-b0cc-4c95-8a36-f3a534e71181</guid>
                    <versionId>3bc2d707-5b1b-4bf9-9c64-b7d9d6b102fb</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c0023b2e-ad30-4250-868d-fb1b277e5c80</parameterMappingId>
                    <processParameterId>2055.034a46fd-24a2-4243-84ce-38fb46ffc959</processParameterId>
                    <parameterMappingParentId>3012.bad1b796-e3db-4b64-8bf1-75482408ff45</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.currentProcessInstanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>931b2483-7c0d-4387-8fd5-42a4ac6aa3cd</guid>
                    <versionId>54ded176-0590-44f0-b67a-6178c36d1231</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.de94dc01-188d-4c72-a3ed-e24eba262a78</parameterMappingId>
                    <processParameterId>2055.390182d1-c178-4449-8178-edd289314ab2</processParameterId>
                    <parameterMappingParentId>3012.bad1b796-e3db-4b64-8bf1-75482408ff45</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>3a5c5054-98bd-4258-b076-46265bcf434c</guid>
                    <versionId>5e9aed10-af29-4d94-9007-0559c978b967</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="bicCodes">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b9a9a230-2b14-4389-aff4-c64082439a47</parameterMappingId>
                    <processParameterId>2055.bb8dd17c-590d-440f-8545-364fdb26328e</processParameterId>
                    <parameterMappingParentId>3012.bad1b796-e3db-4b64-8bf1-75482408ff45</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>0a8cc508-ecf2-4695-8e69-1c25d372f605</guid>
                    <versionId>62be2d06-8b75-44fe-b960-7cb9259e8dc9</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ac0d2037-6ccc-4491-a127-48bf18b6e970</parameterMappingId>
                    <processParameterId>2055.*************-4d10-8b61-5e94d3ad31bd</processParameterId>
                    <parameterMappingParentId>3012.bad1b796-e3db-4b64-8bf1-75482408ff45</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"INWARD DOCUMENTARY COLLECTION"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>84415cd2-1338-4a8d-98d3-2ce6ff829e9a</guid>
                    <versionId>75417f2a-7987-4084-aa43-4781973d99ff</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d367bbb5-e5e8-4bf0-bce1-2ffde9b750c8</parameterMappingId>
                    <processParameterId>2055.9a271b13-fe7a-4bc9-8573-99028a4ff122</processParameterId>
                    <parameterMappingParentId>3012.bad1b796-e3db-4b64-8bf1-75482408ff45</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>c130b516-2aef-4e78-a02d-e670b4b2efa0</guid>
                    <versionId>81ea1f81-0f36-4d20-8d26-fab75d79da8b</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="customerNumber">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c99fbf2a-83ab-40f1-9df5-c445b73d7e16</parameterMappingId>
                    <processParameterId>2055.4ad7fd40-8451-480a-840d-e4350d23f2ba</processParameterId>
                    <parameterMappingParentId>3012.bad1b796-e3db-4b64-8bf1-75482408ff45</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.data</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>5d7e22ca-3cc3-4e45-8569-bcbdf2deeadd</guid>
                    <versionId>a81c5271-97f0-441a-bc07-76ba16061ec6</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.12e25b14-8ed7-4c69-b119-04cb385388a6</parameterMappingId>
                    <processParameterId>2055.ce5a4152-49c6-4bf0-8ad0-af9afb12ebf0</processParameterId>
                    <parameterMappingParentId>3012.bad1b796-e3db-4b64-8bf1-75482408ff45</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>f9c886bf-264a-48f0-bc41-a6bc74eed6cf</guid>
                    <versionId>aa226f1f-e76a-4cc9-9bbb-a3de8947ab47</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.51c3de3b-0ab3-469d-ba06-94ee3f8ee697</parameterMappingId>
                    <processParameterId>2055.a5891acc-745c-47e0-825a-7e040254d8ec</processParameterId>
                    <parameterMappingParentId>3012.bad1b796-e3db-4b64-8bf1-75482408ff45</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>27a22983-7bbb-4d92-a63d-95b11b405288</guid>
                    <versionId>b6480cfa-a482-4ec4-a565-8eb8c26ea08b</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.cf1ea16c-bc7e-4cd5-9b7a-3c07e4d19235</parameterMappingId>
                    <processParameterId>2055.21defa96-4307-47f8-8cb7-e0bc7234481b</processParameterId>
                    <parameterMappingParentId>3012.bad1b796-e3db-4b64-8bf1-75482408ff45</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>e186d049-6ddb-4cd7-83af-52282550f778</guid>
                    <versionId>d9dad1ca-6f79-449c-8197-8861d137692d</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.21906b37-9c7b-469e-a7a9-761aa8547510</processItemId>
            <processId>1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.ded4adff-2801-4d8f-b6b0-d641687f5d61</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-612e</guid>
            <versionId>121124eb-5f0e-43a6-a920-0345512f04d5</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.ded4adff-2801-4d8f-b6b0-d641687f5d61</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>72d6923c-2510-4bb8-9fd3-9f593f96428d</guid>
                <versionId>41b818b3-1dc7-4196-8b8b-bc3fa03ef0bf</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6168376b-5d85-4f59-8812-930901fcdfc8</processItemId>
            <processId>1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf</processId>
            <name>is Successful</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.df387b2b-ca57-477b-bd43-f95e24ad7c83</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189e6fc33e5:-f4d</guid>
            <versionId>3a84cdb1-6650-4640-b6b7-8b9ee861fa42</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="336" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.df387b2b-ca57-477b-bd43-f95e24ad7c83</switchId>
                <guid>4f1c1c85-f448-4ba6-be97-bc1d0d44030c</guid>
                <versionId>7ece7f52-b031-4e95-9887-a005c72e6a50</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.3f338f8b-9b46-4ce4-92dd-6993bec001da</switchConditionId>
                    <switchId>3013.df387b2b-ca57-477b-bd43-f95e24ad7c83</switchId>
                    <seq>1</seq>
                    <endStateId>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:6132</endStateId>
                    <condition>tw.local.isSuccessful	  ==	  false</condition>
                    <guid>956a26c8-582d-40ea-9a7c-0ad6bcf6406d</guid>
                    <versionId>93ca2cbb-cc51-4939-9ab0-f66be533d79f</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.5e47d917-a48f-4e1a-8d82-edcec1d432f1</processItemId>
            <processId>1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.749ae4dd-d9c4-4ce5-9f8a-405743b0039e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3e0a</guid>
            <versionId>de0fc6ad-340d-4387-ab74-0be5829b4d7c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="321" y="182">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.749ae4dd-d9c4-4ce5-9f8a-405743b0039e</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>117e89cd-47fb-4356-bdb8-1a17a7a40c1a</guid>
                <versionId>ec2c1ca7-04a5-47bc-98af-c34b8d43acae</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.02762925-9cb8-44af-bb83-98c369e8336e</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get BIC Codes" id="1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.a11ca880-4f46-4c08-a3b2-7075a4343285">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">//"06948425"&#xD;
"06316421"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.7ab8d2cd-a58f-40d9-8058-650d487cb525" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.a0d503bd-a591-43f1-8c98-1bd7d1707fd9" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="2ed33d78-a529-48a7-b51e-3c1e5010d98c">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="261e9ca5-a033-4934-acb3-3db9c2629ff1" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>b3b7863a-03bb-44d3-aebb-32367b3a575b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>21906b37-9c7b-469e-a7a9-761aa8547510</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>02762925-9cb8-44af-bb83-98c369e8336e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>4c0148fe-c368-4d49-8289-b2bb91dc0300</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6168376b-5d85-4f59-8812-930901fcdfc8</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>5e47d917-a48f-4e1a-8d82-edcec1d432f1</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="b3b7863a-03bb-44d3-aebb-32367b3a575b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.3f480f41-e6cb-4ee9-a4cc-12e8d17e7031</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="21906b37-9c7b-469e-a7a9-761aa8547510">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-612e</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>8647027f-64a3-4fc6-8f79-e27bc8775ae7</ns16:incoming>
                        
                        
                        <ns16:incoming>47b462ce-fa08-415f-8dbd-49a00128172d</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="b3b7863a-03bb-44d3-aebb-32367b3a575b" targetRef="02762925-9cb8-44af-bb83-98c369e8336e" name="To Get BIC codes" id="2027.3f480f41-e6cb-4ee9-a4cc-12e8d17e7031">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.12285efa-55e1-4b26-a3e7-0b8ec7cb6f62" name="Get BIC codes" id="02762925-9cb8-44af-bb83-98c369e8336e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="152" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:postAssignmentScript>if (!tw.local.isSuccessful) {&#xD;
	tw.local.results = new tw.object.listOf.String();&#xD;
}</ns3:postAssignmentScript>
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.3f480f41-e6cb-4ee9-a4cc-12e8d17e7031</ns16:incoming>
                        
                        
                        <ns16:outgoing>d70652fc-1711-437a-8af6-1034582dca14</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.4ad7fd40-8451-480a-840d-e4350d23f2ba</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.data</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.1094baed-e916-4772-8bd8-91a41441f7ad</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a5891acc-745c-47e0-825a-7e040254d8ec</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.034a46fd-24a2-4243-84ce-38fb46ffc959</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.currentProcessInstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.*************-4d10-8b61-5e94d3ad31bd</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"INWARD DOCUMENTARY COLLECTION"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ce5a4152-49c6-4bf0-8ad0-af9afb12ebf0</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.21defa96-4307-47f8-8cb7-e0bc7234481b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.bb8dd17c-590d-440f-8545-364fdb26328e</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.3dfd9149-fb93-49c0-8d6c-3bb6a31c868b</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.390182d1-c178-4449-8178-edd289314ab2</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.9a271b13-fe7a-4bc9-8573-99028a4ff122</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="02762925-9cb8-44af-bb83-98c369e8336e" targetRef="6168376b-5d85-4f59-8812-930901fcdfc8" name="To is Successful" id="d70652fc-1711-437a-8af6-1034582dca14">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:4a4dc2e3dad408dc:-15ab7c7b:188a9fa6c36:6b9a</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="bicCodes" id="2056.da42d11b-a102-4e87-98a1-e1d44bcc46ac" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="instanceID" id="2056.7a09c005-608f-487a-87a4-015a093a7fbc" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="prefix" id="2056.5aeeddc8-ef00-4379-8fec-e9f4f5f448aa" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="processName" id="2056.0419b17c-e0e0-4022-8cd4-07b05f02fd06" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="requestAppID" id="2056.*************-400c-865f-37fe89dd7a4f" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="snapshot" id="2056.2940f60d-769b-45ac-8203-877b9498c200" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="userID" id="2056.fea8b310-6d3d-480e-80a8-6106aaa835ae" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.95130af3-b957-4c8f-80e0-50a161742c50" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.3901d6eb-8c99-476f-89de-7798b53aac80" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.9eee72e3-98a1-4d23-8e34-a3d044139e65" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="02762925-9cb8-44af-bb83-98c369e8336e" parallelMultiple="false" name="Error" id="4c0148fe-c368-4d49-8289-b2bb91dc0300">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="187" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>a0c88492-96e8-4ccd-8b11-22fa3e75a9c7</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="965c19aa-1010-442f-82db-71092d7ba07f" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="d1b86337-cb3a-4135-840f-731da30253f8" eventImplId="c9fd71fd-b2db-4fb0-88b3-4ec72bb0a903">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.c9280472-0a58-4b18-8f01-a558cfc54f55" />
                    
                    
                    <ns16:exclusiveGateway default="8647027f-64a3-4fc6-8f79-e27bc8775ae7" name="is Successful" id="6168376b-5d85-4f59-8812-930901fcdfc8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="336" y="76" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>d70652fc-1711-437a-8af6-1034582dca14</ns16:incoming>
                        
                        
                        <ns16:outgoing>8647027f-64a3-4fc6-8f79-e27bc8775ae7</ns16:outgoing>
                        
                        
                        <ns16:outgoing>a0fbd43d-bc90-4b23-8d54-8742e16865c1</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="6168376b-5d85-4f59-8812-930901fcdfc8" targetRef="21906b37-9c7b-469e-a7a9-761aa8547510" name="To Map data" id="8647027f-64a3-4fc6-8f79-e27bc8775ae7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="6168376b-5d85-4f59-8812-930901fcdfc8" targetRef="5e47d917-a48f-4e1a-8d82-edcec1d432f1" name="To End Event" id="a0fbd43d-bc90-4b23-8d54-8742e16865c1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isSuccessful	  ==	  false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Catch Errors" id="5e47d917-a48f-4e1a-8d82-edcec1d432f1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="321" y="182" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>a0fbd43d-bc90-4b23-8d54-8742e16865c1</ns16:incoming>
                        
                        
                        <ns16:incoming>a0c88492-96e8-4ccd-8b11-22fa3e75a9c7</ns16:incoming>
                        
                        
                        <ns16:outgoing>47b462ce-fa08-415f-8dbd-49a00128172d</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="4c0148fe-c368-4d49-8289-b2bb91dc0300" targetRef="5e47d917-a48f-4e1a-8d82-edcec1d432f1" name="To Catch Errors" id="a0c88492-96e8-4ccd-8b11-22fa3e75a9c7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="5e47d917-a48f-4e1a-8d82-edcec1d432f1" targetRef="21906b37-9c7b-469e-a7a9-761aa8547510" name="To End" id="47b462ce-fa08-415f-8dbd-49a00128172d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To is Successful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.d70652fc-1711-437a-8af6-1034582dca14</processLinkId>
            <processId>1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.02762925-9cb8-44af-bb83-98c369e8336e</fromProcessItemId>
            <endStateId>guid:4a4dc2e3dad408dc:-15ab7c7b:188a9fa6c36:6b9a</endStateId>
            <toProcessItemId>2025.6168376b-5d85-4f59-8812-930901fcdfc8</toProcessItemId>
            <guid>6e1973aa-a022-49cc-b405-bba84b780ada</guid>
            <versionId>096aa3d6-c93b-4ea3-ba8d-e8cfc5555d66</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.02762925-9cb8-44af-bb83-98c369e8336e</fromProcessItemId>
            <toProcessItemId>2025.6168376b-5d85-4f59-8812-930901fcdfc8</toProcessItemId>
        </link>
        <link name="To Map data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.8647027f-64a3-4fc6-8f79-e27bc8775ae7</processLinkId>
            <processId>1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.6168376b-5d85-4f59-8812-930901fcdfc8</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.21906b37-9c7b-469e-a7a9-761aa8547510</toProcessItemId>
            <guid>*************-42b9-bf47-64ed319cf2eb</guid>
            <versionId>10c4f3b8-bf5c-465f-9e80-29083a16da3d</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.6168376b-5d85-4f59-8812-930901fcdfc8</fromProcessItemId>
            <toProcessItemId>2025.21906b37-9c7b-469e-a7a9-761aa8547510</toProcessItemId>
        </link>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.a0fbd43d-bc90-4b23-8d54-8742e16865c1</processLinkId>
            <processId>1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.6168376b-5d85-4f59-8812-930901fcdfc8</fromProcessItemId>
            <endStateId>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:6132</endStateId>
            <toProcessItemId>2025.5e47d917-a48f-4e1a-8d82-edcec1d432f1</toProcessItemId>
            <guid>6bc1bcfc-815a-4667-8183-ef795764857b</guid>
            <versionId>4bedbd00-6a84-4e2a-91ba-5991875bf489</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.6168376b-5d85-4f59-8812-930901fcdfc8</fromProcessItemId>
            <toProcessItemId>2025.5e47d917-a48f-4e1a-8d82-edcec1d432f1</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.47b462ce-fa08-415f-8dbd-49a00128172d</processLinkId>
            <processId>1.97d210e4-a1d3-4893-93d2-e6e4a3e76cdf</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.5e47d917-a48f-4e1a-8d82-edcec1d432f1</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.21906b37-9c7b-469e-a7a9-761aa8547510</toProcessItemId>
            <guid>e6361145-d9f0-4bdc-9d32-12d138d42156</guid>
            <versionId>bc9f58a7-cffd-4498-af4d-8d0ff62f656f</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.5e47d917-a48f-4e1a-8d82-edcec1d432f1</fromProcessItemId>
            <toProcessItemId>2025.21906b37-9c7b-469e-a7a9-761aa8547510</toProcessItemId>
        </link>
    </process>
</teamworks>

