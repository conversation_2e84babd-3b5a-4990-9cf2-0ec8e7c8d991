<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.b066b0aa-3b41-4663-a2f1-ca880f07a183" name="Filter CA Team">
        <lastModified>1692792046073</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.7108b2dc-3135-444c-9b6d-e9066702fdca</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>6521e120-885e-4b4e-9350-e06d494fa96b</guid>
        <versionId>39ccb957-5a9e-4cc1-af21-148756d18c2e</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:9df5bce005da774d:-5bc6ffcb:18a1f9979de:3948" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.9650e8bd-3f84-4f0e-89d2-c6faacc844d6"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":34,"y":145,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"a8949386-a4b7-48a8-a42d-ceb5770465c3"},{"incoming":["f55c3bcd-efb0-42f3-acee-43dc5ba0ce80"],"extensionElements":{"postAssignmentScript":["log.info(\"ProcessInstance : \"+tw.system.currentProcessInstanceID +\" - ServiceName : Filter CA Team : END\");"],"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":1225,"y":144,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:aa4c986259b1691d:-23a6d209:18a11a6235c:3cdc"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"6dee4bc5-23e3-4d72-bc44-067c03344aa7"},{"targetRef":"7108b2dc-3135-444c-9b6d-e9066702fdca","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Check Assign Large Corprate","declaredType":"sequenceFlow","id":"2027.9650e8bd-3f84-4f0e-89d2-c6faacc844d6","sourceRef":"a8949386-a4b7-48a8-a42d-ceb5770465c3"},{"startQuantity":1,"outgoing":["bddb98e9-0204-4867-be7c-71beb4f59f06"],"incoming":["2027.9650e8bd-3f84-4f0e-89d2-c6faacc844d6"],"extensionElements":{"postAssignmentScript":["log.info(\" ServiceName : Check Assign Large Corprate : START\");"],"nodeVisualInfo":[{"width":95,"x":250,"y":127,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["log.info(\"ProcessInstance : \"+tw.system.currentProcessInstanceID +\" - ServiceName : Filter CA Team : START\");\r\nlog.info(\" ServiceName : Check Assign Large Corprate : START\");"],"activityType":["CalledProcess"]},"name":"Check Assign Large Corprate","dataInputAssociation":[{"targetRef":"2055.a2a70141-40f9-485d-84cf-53aff58969a6","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.CIF"]}}]},{"targetRef":"2055.5f070a30-da63-42e9-a0e0-************","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.teamSuffix"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"7108b2dc-3135-444c-9b6d-e9066702fdca","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0","declaredType":"TFormalExpression","content":["tw.local.filteredTeam"]}}],"sourceRef":["2055.4c821ca4-c74b-4703-b8dd-a17c9b0cbc8d"]}],"calledElement":"1.37eab8a6-3b8b-4e52-8824-851827e9889b"},{"outgoing":["96379411-1c4a-4f75-bdde-c7fa3bdc4396","a0d53811-6a0f-4ab5-a47d-2c29ac0d21e0"],"incoming":["bddb98e9-0204-4867-be7c-71beb4f59f06"],"default":"96379411-1c4a-4f75-bdde-c7fa3bdc4396","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":414,"y":141,"declaredType":"TNodeVisualInfo","height":32}],"preAssignmentScript":[]},"name":"Is LRGEC","declaredType":"exclusiveGateway","id":"3521bde9-bf66-48d2-b599-7b9c773e2e2f"},{"targetRef":"88618851-656d-4ce4-82d9-99ada9bc849e","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To Multi Facility","declaredType":"sequenceFlow","id":"96379411-1c4a-4f75-bdde-c7fa3bdc4396","sourceRef":"3521bde9-bf66-48d2-b599-7b9c773e2e2f"},{"startQuantity":1,"outgoing":["fc1cefa7-d336-49e2-bf33-2629b59f0714"],"incoming":["a0d53811-6a0f-4ab5-a47d-2c29ac0d21e0"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":409,"y":12,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Update Teams Name","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"d7494652-dd04-49eb-a64c-76d3bf06385b","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.teamName = tw.local.filteredTeam.name;\r\n\r\nif (tw.local.teamName.indexOf(\"MKR\") &gt; -1) {\r\n    tw.local.TEAM_MANAGER = tw.local.teamName.replace(\"MKR\", \"CHKR\");\r\n} else {\r\n    tw.local.TEAM_MANAGER = tw.local.teamName\r\n}\r\n\r\n\r\ntw.local.parent_team_name = tw.epv.BPMTeamsNamesReassign.CA_Execution_MKR;\r\n    tw.local.tmpvariable = tw.epv.BPMTeamsNamesReassign.CA_Large_Execution_MKR_Manager;\r\n    tw.local.filteredTeam.managerTeam = tw.local.tmpvariable;\r\n    if (tw.local.teamName.indexOf(\"CHKR\") &gt; -1) {\r\n        tw.local.parent_team_name = tw.local.parent_team_name.replace(\"Maker\", \"Checker\");\r\n        tw.local.filteredTeam.managerTeam = tw.local.filteredTeam.managerTeam.replace(\"MKR\", \"CHKR\");\r\n    }\r\n"]}},{"targetRef":"d7494652-dd04-49eb-a64c-76d3bf06385b","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.filteredTeam.members.listLength\t  &gt;\t  0"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Update Teams Name","declaredType":"sequenceFlow","id":"a0d53811-6a0f-4ab5-a47d-2c29ac0d21e0","sourceRef":"3521bde9-bf66-48d2-b599-7b9c773e2e2f"},{"startQuantity":1,"outgoing":["29f099b4-5fcd-4f3d-aef8-64efc389a0ca"],"incoming":["96379411-1c4a-4f75-bdde-c7fa3bdc4396"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":566,"y":121,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Multi Facility","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"88618851-656d-4ce4-82d9-99ada9bc849e","scriptFormat":"text\/x-javascript","script":{"content":["var maxperc = 0;\r\n\r\nfor (var i=0; i&lt;tw.local.Facilities.listLength; i++) {\r\n\tfor (var j=0; j &lt; tw.local.Facilities[i].facilityLines.listLength; j++) {\r\n\t\tif (tw.local.Facilities[i].facilityLines[j].facilityPercentageToBook &gt; maxperc) {\r\n\t\t\ttw.local.branchCode = tw.local.Facilities[i].facilityLines[j].facilityBranch.value;\r\n\t\t\tmaxperc = tw.local.Facilities[i].facilityLines[j].facilityPercentageToBook;\r\n\t\t}\r\n\t}\r\n}\r\n\r\nwriteLog(\"more than one facility \");\r\n\r\nfunction writeLog(msg , variable){  \/\/ vairable is optional\r\n\tvar instanceID = \"\";\r\n\ttry {instanceID = \"Instance ID : \"+tw.system.currentProcessInstance.id  +\"  ::  \";} catch (err) {}\r\n\tvariable == undefined ? variable =\" \" : variable = \" : \"+ variable ;\r\n\tvar message = instanceID + tw.system.model.processApp.name  +\" :: \" + tw.system.serviceFlow.type +\" :: \"+ tw.system.serviceFlow.name +\" :: \"  + msg+\" : \" + variable;\r\n\tlog.info( message);\r\n}\r\n"]}},{"targetRef":"d83c7880-9130-4942-b143-d160a6c41027","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Get Center Code","declaredType":"sequenceFlow","id":"29f099b4-5fcd-4f3d-aef8-64efc389a0ca","sourceRef":"88618851-656d-4ce4-82d9-99ada9bc849e"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"teamName","isCollection":false,"declaredType":"dataObject","id":"2056.6f9e3a45-1b3f-447a-9596-85635b95c30e"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"TEAM_MANAGER","isCollection":false,"declaredType":"dataObject","id":"2056.a4d9e949-7303-4c87-a1be-9c5263e4e5f9"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parent_team_name","isCollection":false,"declaredType":"dataObject","id":"2056.d4d58734-8ef5-421f-b32f-90d5fc80fa55"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"tmpvariable","isCollection":false,"declaredType":"dataObject","id":"2056.13b0fb6d-29c0-45d4-8437-f6353c8fbb74"},{"startQuantity":1,"outgoing":["91e37f5e-76d2-4721-9d74-2f8a83db859f"],"incoming":["29f099b4-5fcd-4f3d-aef8-64efc389a0ca"],"extensionElements":{"postAssignmentScript":["log.info(\" ServiceName : Get Center Code: END\");"],"nodeVisualInfo":[{"width":95,"x":756,"y":111,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["log.info(\" ServiceName : Get Center Code: START\");"],"activityType":["CalledProcess"]},"name":"Get Center Code","dataInputAssociation":[{"targetRef":"2055.cd1847f0-7862-4b2f-814c-daed841758b3","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.branchCode"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"d83c7880-9130-4942-b143-d160a6c41027","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","declaredType":"TFormalExpression","content":["tw.local.center"]}}],"sourceRef":["2055.108d6047-03b9-47d9-abfc-55c662ab0907"]}],"calledElement":"1.caf369f2-a2d3-4b1b-bec5-ace8bfe23165"},{"targetRef":"7777e18e-01c2-41a0-b497-7cbd0b4db94b","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-2b0b"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Get Team From LDAP","declaredType":"sequenceFlow","id":"91e37f5e-76d2-4721-9d74-2f8a83db859f","sourceRef":"d83c7880-9130-4942-b143-d160a6c41027"},{"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"center","isCollection":false,"declaredType":"dataObject","id":"2056.de241f50-98c0-485c-91cf-e151590a14d0"},{"startQuantity":1,"outgoing":["e05da848-30c5-4f7a-b8f1-549cb9769638"],"incoming":["91e37f5e-76d2-4721-9d74-2f8a83db859f"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":940,"y":121,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Get Team From LDAP","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"7777e18e-01c2-41a0-b497-7cbd0b4db94b","scriptFormat":"text\/x-javascript","script":{"content":["writeLog(\" Filter CA Team \");\r\n\/\/writeLog(\"tw.local.center.value\", tw.local.center.value);\r\nwriteLog(\"tw.local.Role\", tw.local.Role);\r\nlog.info(\"is My Team Here .......\"+tw.local.teamName);\r\n\r\nif (tw.local.center != null) {\r\n    var Users;\r\n    tw.local.teamName = tw.local.center.value + tw.local.Role;\r\n    Users = tw.system.org.findRoleByName(tw.local.teamName).allUsers;\r\n\r\n    tw.local.filteredTeam = new tw.object.Team();\r\n    tw.local.filteredTeam.name = tw.local.teamName;\r\n    tw.local.filteredTeam.members = new tw.object.listOf.String();\r\nlog.info(\"Where Is My Team .......\"+tw.local.teamName);\r\n\r\n    for (var i = 0; i &lt; Users.length; i++) {\r\n        tw.local.filteredTeam.members[i] = Users[i].name;\r\n    }\r\n    \r\n    tw.local.TEAM_MANAGER = tw.local.teamName; \/\/make checker is manager\r\n    if (tw.local.teamName.indexOf(\"MKR\") &gt; -1) {\r\n        tw.local.TEAM_MANAGER = tw.local.teamName.replace(\"MKR\", \"CHKR\");\r\n    }\r\n}\r\n\r\nUpdateTeamParentName(tw.local.teamName);\r\n\r\n\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\r\n\r\nfunction UpdateTeamParentName(teamname) {\r\n    try {\r\n        if (teamname.indexOf(\"_CHKR\") != -1 || teamname.indexOf(\"_Chkr\") != -1) {\r\n            tw.local.parent_team_name = tw.epv.BPMTeamsNamesReassign.CA_Execution_CHKR;\r\n            tw.local.tmpvariable = tw.epv.BPMTeamsNamesReassign.CA_Large_Execution_CHKR_Manager;\r\n            tw.local.filteredTeam.managerTeam = tw.local.tmpvariable;\r\n        } else {\r\n            tw.local.parent_team_name = tw.epv.BPMTeamsNamesReassign.CA_Execution_MKR;\r\n            tw.local.tmpvariable = tw.epv.BPMTeamsNamesReassign.CA_Large_Execution_MKR_Manager;\r\n            tw.local.filteredTeam.managerTeam = tw.local.tmpvariable;\r\n        }\r\n    } catch (err) {\r\n       writeLog(err)\r\n    }\r\n\r\n}\r\n\r\nfunction writeLog(msg, variable) { \/\/ vairable is optional\r\n    var instanceID = \"\";\r\n    try {\r\n        instanceID = \"Instance ID : \" + tw.system.currentProcessInstance.id + \"  ::  \";\r\n    } catch (err) {}\r\n    variable == undefined ? variable = \" \" : variable = \" : \" + variable;\r\n    var message = instanceID + tw.system.model.processApp.name + \" :: \" + tw.system.serviceFlow.type + \" :: \" + tw.system.serviceFlow.name + \" :: \" + msg + \" : \" + variable;\r\n    log.info(message);\r\n   \/\/ tw.local.logError.insertIntoList(tw.local.logError.listLength, message);\r\n}"]}},{"targetRef":"a12f1141-371e-4a5a-8153-c2a9a11a617e","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Audit","declaredType":"sequenceFlow","id":"e05da848-30c5-4f7a-b8f1-549cb9769638","sourceRef":"7777e18e-01c2-41a0-b497-7cbd0b4db94b"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"logError","isCollection":true,"declaredType":"dataObject","id":"2056.b8db0cc6-0e44-4870-91d9-394311117c2b"},{"startQuantity":1,"outgoing":["f55c3bcd-efb0-42f3-acee-43dc5ba0ce80"],"incoming":["fc1cefa7-d336-49e2-bf33-2629b59f0714","e05da848-30c5-4f7a-b8f1-549cb9769638"],"extensionElements":{"postAssignmentScript":["log.info(\" ServiceName : Audit: END\");"],"nodeVisualInfo":[{"width":95,"x":1075,"y":121,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["log.info(\" ServiceName : Audit: START\");"],"activityType":["CalledProcess"]},"name":"Audit","dataInputAssociation":[{"targetRef":"2055.0dbed104-185b-44f0-9303-0765cbdb781e","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.InstanceID"]}}]},{"targetRef":"2055.8129799a-130c-4004-beb4-d73c68f78c65","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"\""]}}]},{"targetRef":"2055.8fa8039e-dbe6-4ecf-8897-769135f4dca7","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"\""]}}]},{"targetRef":"2055.e91e9ebe-6e70-4b06-996b-54441169b3ca","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.teamName"]}}]},{"targetRef":"2055.f7296d68-138b-4e58-b910-ce7df924d7f8","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"\""]}}]},{"targetRef":"2055.df186765-bf04-4089-99d9-3f84a4866be2","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.TEAM_MANAGER"]}}]},{"targetRef":"2055.11effbb1-5ad3-4f99-a23c-01eda7584e00","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"1\""]}}]},{"targetRef":"2055.72a822b9-58cf-4b48-9c2d-db5353162497","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","declaredType":"TFormalExpression","content":["1"]}}]},{"targetRef":"2055.d1976350-35cc-4bb7-be72-0d2c2c4e976b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.stepName"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"a12f1141-371e-4a5a-8153-c2a9a11a617e","calledElement":"1.2d69e423-2547-4495-845d-7bccbf743136"},{"targetRef":"6dee4bc5-23e3-4d72-bc44-067c03344aa7","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-27fb"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"f55c3bcd-efb0-42f3-acee-43dc5ba0ce80","sourceRef":"a12f1141-371e-4a5a-8153-c2a9a11a617e"},{"targetRef":"a12f1141-371e-4a5a-8153-c2a9a11a617e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Audit","declaredType":"sequenceFlow","id":"fc1cefa7-d336-49e2-bf33-2629b59f0714","sourceRef":"d7494652-dd04-49eb-a64c-76d3bf06385b"},{"targetRef":"3521bde9-bf66-48d2-b599-7b9c773e2e2f","extensionElements":{"endStateId":["guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-2d2e"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Is LRGEC","declaredType":"sequenceFlow","id":"bddb98e9-0204-4867-be7c-71beb4f59f06","sourceRef":"7108b2dc-3135-444c-9b6d-e9066702fdca"},{"parallelMultiple":false,"outgoing":["f27d1dfc-3336-4459-90de-3cbddff5beca"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"4b57b95f-a403-4f85-a408-bec4b81157a0"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"b5ce7ee0-3160-409c-bfc4-94d5322ce328","otherAttributes":{"eventImplId":"aed9b578-3afc-473e-87ec-b6c7ea888c4f"}}],"attachedToRef":"a12f1141-371e-4a5a-8153-c2a9a11a617e","extensionElements":{"nodeVisualInfo":[{"width":24,"x":1110,"y":179,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"1e54a6ee-38ae-48f0-a614-cafc941a68ec","outputSet":{}},{"parallelMultiple":false,"outgoing":["1936be34-84e6-42c1-a970-979962d7d06d"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"dca2eaea-f48f-429d-bec6-b40aee640a60"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"32b076b4-b16d-4e6e-988a-01bb1c5c345a","otherAttributes":{"eventImplId":"99ee835e-0870-4923-8437-df4b18c0ae49"}}],"attachedToRef":"d83c7880-9130-4942-b143-d160a6c41027","extensionElements":{"nodeVisualInfo":[{"width":24,"x":791,"y":169,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"bb2a0dea-d40e-42d2-aa6a-9eed6ee4ac69","outputSet":{}},{"parallelMultiple":false,"outgoing":["737797f9-b44f-44b6-8d94-ed6399fdc241"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"625841b0-c765-4cbf-b526-4c4ead0f7fa1"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"7b2d42cc-99ea-44ce-81d9-cbddecf32219","otherAttributes":{"eventImplId":"dc5a7162-393c-4d06-8bf1-5add25b0bf2e"}}],"attachedToRef":"7108b2dc-3135-444c-9b6d-e9066702fdca","extensionElements":{"nodeVisualInfo":[{"width":24,"x":285,"y":185,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"777e6d48-4c54-45a2-aa6c-bcaf051cae4f","outputSet":{}},{"startQuantity":1,"outgoing":["fdcfb9dc-a48f-4d57-8499-d4b0b05b19f8"],"incoming":["737797f9-b44f-44b6-8d94-ed6399fdc241","1936be34-84e6-42c1-a970-979962d7d06d","f27d1dfc-3336-4459-90de-3cbddff5beca"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":573,"y":239,"declaredType":"TNodeVisualInfo","height":70}]},"name":"log Error","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"a4f9bb4d-7857-491b-ae5d-b14ad542e004","scriptFormat":"text\/x-javascript","script":{"content":["log.info(\"*============ NBE letter Of Credit =============*\");\r\nlog.info(\"*========================================*\");\r\nlog.info(\"[Filter CA Team -&gt; Log Error ]- start\");\r\nlog.info(\"ProcessInstance :\" +tw.system.currentProcessInstanceID + \" Error : \" + tw.system.error.toString(true));\r\ntw.local.errorMSG=String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\nlog.info(\"[Filter CA Team -&gt; Log Error ]- END\");\r\nlog.info(\"*================================================*\");"]}},{"targetRef":"a4f9bb4d-7857-491b-ae5d-b14ad542e004","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To log Error","declaredType":"sequenceFlow","id":"737797f9-b44f-44b6-8d94-ed6399fdc241","sourceRef":"777e6d48-4c54-45a2-aa6c-bcaf051cae4f"},{"targetRef":"a4f9bb4d-7857-491b-ae5d-b14ad542e004","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To log Error","declaredType":"sequenceFlow","id":"1936be34-84e6-42c1-a970-979962d7d06d","sourceRef":"bb2a0dea-d40e-42d2-aa6a-9eed6ee4ac69"},{"targetRef":"a4f9bb4d-7857-491b-ae5d-b14ad542e004","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To log Error","declaredType":"sequenceFlow","id":"f27d1dfc-3336-4459-90de-3cbddff5beca","sourceRef":"1e54a6ee-38ae-48f0-a614-cafc941a68ec"},{"incoming":["fdcfb9dc-a48f-4d57-8499-d4b0b05b19f8"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"b1082191-5b39-4ba9-877b-56a5416a7387","otherAttributes":{"eventImplId":"ea4b52a4-c781-4140-8336-8f1d5a4b5e7d"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":792,"y":262,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End Event","dataInputAssociation":[{"assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}]}],"declaredType":"endEvent","id":"6e95846b-e400-4cb9-91d3-c08fe5150494"},{"targetRef":"6e95846b-e400-4cb9-91d3-c08fe5150494","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"fdcfb9dc-a48f-4d57-8499-d4b0b05b19f8","sourceRef":"a4f9bb4d-7857-491b-ae5d-b14ad542e004"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.a4f2ae9d-fa53-4179-aa99-a0f7b8cea825"}],"laneSet":[{"id":"5a3ebd06-7477-4a19-a8b3-b7b3d07cd3b1","lane":[{"flowNodeRef":["a8949386-a4b7-48a8-a42d-ceb5770465c3","6dee4bc5-23e3-4d72-bc44-067c03344aa7","7108b2dc-3135-444c-9b6d-e9066702fdca","3521bde9-bf66-48d2-b599-7b9c773e2e2f","d7494652-dd04-49eb-a64c-76d3bf06385b","88618851-656d-4ce4-82d9-99ada9bc849e","d83c7880-9130-4942-b143-d160a6c41027","7777e18e-01c2-41a0-b497-7cbd0b4db94b","a12f1141-371e-4a5a-8153-c2a9a11a617e","1e54a6ee-38ae-48f0-a614-cafc941a68ec","bb2a0dea-d40e-42d2-aa6a-9eed6ee4ac69","777e6d48-4c54-45a2-aa6c-bcaf051cae4f","a4f9bb4d-7857-491b-ae5d-b14ad542e004","6e95846b-e400-4cb9-91d3-c08fe5150494"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"b550c3ab-2780-4856-ac64-8110d949be61","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Filter CA Team","declaredType":"process","id":"1.b066b0aa-3b41-4663-a2f1-ca880f07a183","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0","name":"filteredTeam","isCollection":false,"id":"2055.5a25aaa2-7dd9-4586-9b1a-fdb9ef097535","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnly":"true"}}],"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.f83b19ad-8a7f-4026-bded-63fd31478fbd","epvProcessLinkId":"ecd146fe-6d1a-4425-8b39-f415175fdde2","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{"dataInputRefs":["2055.5b4c0c07-ddcd-4ca2-abd6-0bc175e99794","2055.a858a1e4-ba29-452b-acec-21f8428d40c5","2055.765c6f1c-6674-485d-9994-8791770b83ef","2055.53db05f6-43c4-4dbf-8dff-92ed59498a7c","2055.0e9a2ca1-f260-4db8-96c1-3c698efa22a1","2055.0fae7af1-2e66-4f04-981b-1d9ff6f8491d","2055.5d310308-0665-45be-8f3a-e4b6d674d770","2055.021e0e40-963b-43f5-81c8-a01b0c3b2b47"]}],"outputSet":[{}],"otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnlyOutputs":"true"},"dataInput":[{"itemSubjectRef":"itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0","name":"originalTeam","isCollection":false,"id":"2055.5b4c0c07-ddcd-4ca2-abd6-0bc175e99794","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnly":"true"}},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"_EXE_MKR\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"Role","isCollection":false,"id":"2055.a858a1e4-ba29-452b-acec-21f8428d40c5"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"10101010\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"CIF","isCollection":false,"id":"2055.765c6f1c-6674-485d-9994-8791770b83ef"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"BPM_CA_LRG_CORP_EXE_MKR\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"teamSuffix","isCollection":false,"id":"2055.53db05f6-43c4-4dbf-8dff-92ed59498a7c"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.listOf.toolkit.NBEC.creditFacilityInformation();\nautoObject[0] = new tw.object.toolkit.NBEC.creditFacilityInformation();\nautoObject[0].facilityCode = \"\";\nautoObject[0].overallLimit = 0.0;\nautoObject[0].limitAmount = 0.0;\nautoObject[0].effectiveLimitAmount = 0.0;\nautoObject[0].availableAmount = 0.0;\nautoObject[0].expiryDate = new TWDate();\nautoObject[0].availableFlag = false;\nautoObject[0].authorizedFlag = false;\nautoObject[0].Utilization = 0.0;\nautoObject[0].returnCode = \"\";\nautoObject[0].facilityLines = new tw.object.listOf.toolkit.NBEC.FacilityLinesDetails();\nautoObject[0].facilityLines[0] = new tw.object.toolkit.NBEC.FacilityLinesDetails();\nautoObject[0].facilityLines[0].lineCode = \"\";\nautoObject[0].facilityLines[0].lineAmount = 0.0;\nautoObject[0].facilityLines[0].availableAmount = 0.0;\nautoObject[0].facilityLines[0].effectiveLineAmount = 0.0;\nautoObject[0].facilityLines[0].expiryDate = new TWDate();\nautoObject[0].facilityLines[0].facilityBranch = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject[0].facilityLines[0].facilityBranch.name = \"\";\nautoObject[0].facilityLines[0].facilityBranch.value = \"001\";\nautoObject[0].facilityLines[0].availableFlag = false;\nautoObject[0].facilityLines[0].authorizedFlag = false;\nautoObject[0].facilityLines[0].facilityPercentageToBook = 20;\nautoObject[0].facilityLines[0].internalRemarks = \"\";\nautoObject[0].facilityLines[0].purpose = \"\";\nautoObject[0].facilityLines[0].LCCommissionPercentage = 0;\nautoObject[0].facilityLines[0].LCDef = \"\";\nautoObject[0].facilityLines[0].LCCashCover = \"\";\nautoObject[0].facilityLines[0].IDCCommission = \"\";\nautoObject[0].facilityLines[0].IDCAvalCommPercentage = 0;\nautoObject[0].facilityLines[0].IDCCashCoverPercentage = 0;\nautoObject[0].facilityLines[0].debitAccountNumber = \"\";\nautoObject[0].facilityLines[0].lineCurrency = \"\";\nautoObject[0].facilityLines[0].lineSerialNumber = \"\";\nautoObject[0].facilityLines[0].returnCode = \"\";\nautoObject[0].facilityLines[0].LGCommission = 0;\nautoObject[0].facilityLines[0].LGCashCover_BidBond = 0;\nautoObject[0].facilityLines[0].LGCashCover_Performance = 0;\nautoObject[0].facilityLines[0].LGCashCover_AdvancedPayment = 0;\nautoObject[0].facilityLines[0].restrictedCurrencies = new tw.object.listOf.toolkit.NBEC.restrictedItem();\nautoObject[0].facilityLines[0].restrictedCurrencies[0] = new tw.object.toolkit.NBEC.restrictedItem();\nautoObject[0].facilityLines[0].restrictedCurrencies[0].name = \"\";\nautoObject[0].facilityLines[0].restrictedCurrencies[0].code = \"\";\nautoObject[0].facilityLines[0].restrictedCurrencies[0].source = \"\";\nautoObject[0].facilityLines[0].restrictedCurrencies[0].status = \"\";\nautoObject[0].facilityLines[0].restrictedBranches = new tw.object.listOf.toolkit.NBEC.restrictedItem();\nautoObject[0].facilityLines[0].restrictedBranches[0] = new tw.object.toolkit.NBEC.restrictedItem();\nautoObject[0].facilityLines[0].restrictedBranches[0].name = \"\";\nautoObject[0].facilityLines[0].restrictedBranches[0].code = \"\";\nautoObject[0].facilityLines[0].restrictedBranches[0].source = \"\";\nautoObject[0].facilityLines[0].restrictedBranches[0].status = \"\";\nautoObject[0].facilityLines[0].restrictedProducts = new tw.object.listOf.toolkit.NBEC.restrictedItem();\nautoObject[0].facilityLines[0].restrictedProducts[0] = new tw.object.toolkit.NBEC.restrictedItem();\nautoObject[0].facilityLines[0].restrictedProducts[0].name = \"\";\nautoObject[0].facilityLines[0].restrictedProducts[0].code = \"\";\nautoObject[0].facilityLines[0].restrictedProducts[0].source = \"\";\nautoObject[0].facilityLines[0].restrictedProducts[0].status = \"\";\nautoObject[0].facilityLines[0].restrictedCustomers = new tw.object.listOf.toolkit.NBEC.restrictedItem();\nautoObject[0].facilityLines[0].restrictedCustomers[0] = new tw.object.toolkit.NBEC.restrictedItem();\nautoObject[0].facilityLines[0].restrictedCustomers[0].name = \"\";\nautoObject[0].facilityLines[0].restrictedCustomers[0].code = \"\";\nautoObject[0].facilityLines[0].restrictedCustomers[0].source = \"\";\nautoObject[0].facilityLines[0].restrictedCustomers[0].status = \"\";\nautoObject[0].facilityLines[0].exposureRestrictions = new tw.object.listOf.toolkit.NBEC.restrictedItem();\nautoObject[0].facilityLines[0].exposureRestrictions[0] = new tw.object.toolkit.NBEC.restrictedItem();\nautoObject[0].facilityLines[0].exposureRestrictions[0].name = \"\";\nautoObject[0].facilityLines[0].exposureRestrictions[0].code = \"\";\nautoObject[0].facilityLines[0].exposureRestrictions[0].source = \"\";\nautoObject[0].facilityLines[0].exposureRestrictions[0].status = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c","name":"Facilities","isCollection":true,"id":"2055.0e9a2ca1-f260-4db8-96c1-3c698efa22a1"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"InstanceID","isCollection":false,"id":"2055.0fae7af1-2e66-4f04-981b-1d9ff6f8491d"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\/\/\"001\"\r\n\"100\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"branchCode","isCollection":false,"id":"2055.5d310308-0665-45be-8f3a-e4b6d674d770"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"stepName","isCollection":false,"id":"2055.021e0e40-963b-43f5-81c8-a01b0c3b2b47"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="originalTeam">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5b4c0c07-ddcd-4ca2-abd6-0bc175e99794</processParameterId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>3de7ac99-b231-49ca-b224-cb641e378448</guid>
            <versionId>7c8bb7eb-0f53-4490-8b95-f48fe9357206</versionId>
        </processParameter>
        <processParameter name="Role">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a858a1e4-ba29-452b-acec-21f8428d40c5</processParameterId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"_EXE_MKR"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>b2324643-ab53-4c4c-95e1-6fd308f8e574</guid>
            <versionId>4113f12a-276d-4a70-ac21-9b54c0fefdb3</versionId>
        </processParameter>
        <processParameter name="CIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.765c6f1c-6674-485d-9994-8791770b83ef</processParameterId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>c6cdbb1f-98a4-4eb5-b99b-a0b14eeb88f1</guid>
            <versionId>50cda814-71cf-4d2d-893e-d2af042e6577</versionId>
        </processParameter>
        <processParameter name="teamSuffix">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.53db05f6-43c4-4dbf-8dff-92ed59498a7c</processParameterId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>8dbece55-85af-4453-bd9f-efbd5f13f219</guid>
            <versionId>39ea09b2-76ec-47b9-95c8-49e464df3ad0</versionId>
        </processParameter>
        <processParameter name="Facilities">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.0e9a2ca1-f260-4db8-96c1-3c698efa22a1</processParameterId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c</classId>
            <seq>5</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.listOf.toolkit.NBEC.creditFacilityInformation();
autoObject[0] = new tw.object.toolkit.NBEC.creditFacilityInformation();
autoObject[0].facilityCode = "";
autoObject[0].overallLimit = 0.0;
autoObject[0].limitAmount = 0.0;
autoObject[0].effectiveLimitAmount = 0.0;
autoObject[0].availableAmount = 0.0;
autoObject[0].expiryDate = new TWDate();
autoObject[0].availableFlag = false;
autoObject[0].authorizedFlag = false;
autoObject[0].Utilization = 0.0;
autoObject[0].returnCode = "";
autoObject[0].facilityLines = new tw.object.listOf.toolkit.NBEC.FacilityLinesDetails();
autoObject[0].facilityLines[0] = new tw.object.toolkit.NBEC.FacilityLinesDetails();
autoObject[0].facilityLines[0].lineCode = "";
autoObject[0].facilityLines[0].lineAmount = 0.0;
autoObject[0].facilityLines[0].availableAmount = 0.0;
autoObject[0].facilityLines[0].effectiveLineAmount = 0.0;
autoObject[0].facilityLines[0].expiryDate = new TWDate();
autoObject[0].facilityLines[0].facilityBranch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject[0].facilityLines[0].facilityBranch.name = "";
autoObject[0].facilityLines[0].facilityBranch.value = "001";
autoObject[0].facilityLines[0].availableFlag = false;
autoObject[0].facilityLines[0].authorizedFlag = false;
autoObject[0].facilityLines[0].facilityPercentageToBook = 20;
autoObject[0].facilityLines[0].internalRemarks = "";
autoObject[0].facilityLines[0].purpose = "";
autoObject[0].facilityLines[0].LCCommissionPercentage = 0;
autoObject[0].facilityLines[0].LCDef = "";
autoObject[0].facilityLines[0].LCCashCover = "";
autoObject[0].facilityLines[0].IDCCommission = "";
autoObject[0].facilityLines[0].IDCAvalCommPercentage = 0;
autoObject[0].facilityLines[0].IDCCashCoverPercentage = 0;
autoObject[0].facilityLines[0].debitAccountNumber = "";
autoObject[0].facilityLines[0].lineCurrency = "";
autoObject[0].facilityLines[0].lineSerialNumber = "";
autoObject[0].facilityLines[0].returnCode = "";
autoObject[0].facilityLines[0].LGCommission = 0;
autoObject[0].facilityLines[0].LGCashCover_BidBond = 0;
autoObject[0].facilityLines[0].LGCashCover_Performance = 0;
autoObject[0].facilityLines[0].LGCashCover_AdvancedPayment = 0;
autoObject[0].facilityLines[0].restrictedCurrencies = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject[0].facilityLines[0].restrictedCurrencies[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject[0].facilityLines[0].restrictedCurrencies[0].name = "";
autoObject[0].facilityLines[0].restrictedCurrencies[0].code = "";
autoObject[0].facilityLines[0].restrictedCurrencies[0].source = "";
autoObject[0].facilityLines[0].restrictedCurrencies[0].status = "";
autoObject[0].facilityLines[0].restrictedBranches = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject[0].facilityLines[0].restrictedBranches[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject[0].facilityLines[0].restrictedBranches[0].name = "";
autoObject[0].facilityLines[0].restrictedBranches[0].code = "";
autoObject[0].facilityLines[0].restrictedBranches[0].source = "";
autoObject[0].facilityLines[0].restrictedBranches[0].status = "";
autoObject[0].facilityLines[0].restrictedProducts = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject[0].facilityLines[0].restrictedProducts[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject[0].facilityLines[0].restrictedProducts[0].name = "";
autoObject[0].facilityLines[0].restrictedProducts[0].code = "";
autoObject[0].facilityLines[0].restrictedProducts[0].source = "";
autoObject[0].facilityLines[0].restrictedProducts[0].status = "";
autoObject[0].facilityLines[0].restrictedCustomers = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject[0].facilityLines[0].restrictedCustomers[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject[0].facilityLines[0].restrictedCustomers[0].name = "";
autoObject[0].facilityLines[0].restrictedCustomers[0].code = "";
autoObject[0].facilityLines[0].restrictedCustomers[0].source = "";
autoObject[0].facilityLines[0].restrictedCustomers[0].status = "";
autoObject[0].facilityLines[0].exposureRestrictions = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject[0].facilityLines[0].exposureRestrictions[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject[0].facilityLines[0].exposureRestrictions[0].name = "";
autoObject[0].facilityLines[0].exposureRestrictions[0].code = "";
autoObject[0].facilityLines[0].exposureRestrictions[0].source = "";
autoObject[0].facilityLines[0].exposureRestrictions[0].status = "";
autoObject</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>1478543d-7434-4148-9c9b-020e13259c41</guid>
            <versionId>6a1dfe20-fc3d-491c-97ba-27e5313158f9</versionId>
        </processParameter>
        <processParameter name="InstanceID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.0fae7af1-2e66-4f04-981b-1d9ff6f8491d</processParameterId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>60249273-743d-4e1c-92d8-cb287fbb3617</guid>
            <versionId>38b62d0d-33ec-47d5-9b30-91c00eac67b4</versionId>
        </processParameter>
        <processParameter name="branchCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5d310308-0665-45be-8f3a-e4b6d674d770</processParameterId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>7</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>//"001"&#xD;
"100"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>10104ef7-ee7c-46e5-b3fb-c9fa4ac0935c</guid>
            <versionId>f6d24ce0-2a3e-497d-9a2f-3607345aede7</versionId>
        </processParameter>
        <processParameter name="stepName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.021e0e40-963b-43f5-81c8-a01b0c3b2b47</processParameterId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>8</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>825e395c-16c8-44c6-8e95-3aff08c13a4b</guid>
            <versionId>bd5073e2-8551-4c09-9886-e2ec5b50efef</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.14c513ad-5260-4fa8-9bdf-c9b31108977e</processParameterId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>9</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>9b801552-5f99-44aa-b58c-4ff5a1363f6d</guid>
            <versionId>b11c74b7-d10b-4a24-8f8a-7dc301e39c1f</versionId>
        </processParameter>
        <processParameter name="filteredTeam">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5a25aaa2-7dd9-4586-9b1a-fdb9ef097535</processParameterId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0</classId>
            <seq>9</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>57dcb2e4-9efd-4a7c-abdb-99672824e399</guid>
            <versionId>d94ddb00-b11c-4c82-944e-08f5f86adebd</versionId>
        </processParameter>
        <processVariable name="teamName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6f9e3a45-1b3f-447a-9596-85635b95c30e</processVariableId>
            <description isNull="true" />
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>fafcb2bd-5e47-4bd8-8d0a-a168af60889c</guid>
            <versionId>a75205ce-069f-4bee-a2bd-8e8c505e74b7</versionId>
        </processVariable>
        <processVariable name="TEAM_MANAGER">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a4d9e949-7303-4c87-a1be-9c5263e4e5f9</processVariableId>
            <description isNull="true" />
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>914d7c37-61ce-4f21-bea0-9169f75cc179</guid>
            <versionId>8f948e9b-1a87-41d0-8484-b72fa8faafec</versionId>
        </processVariable>
        <processVariable name="parent_team_name">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d4d58734-8ef5-421f-b32f-90d5fc80fa55</processVariableId>
            <description isNull="true" />
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c79dcdbe-b910-472b-9176-84095d553af4</guid>
            <versionId>b3d609cc-726e-4933-bd4b-810856f2c9e3</versionId>
        </processVariable>
        <processVariable name="tmpvariable">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.13b0fb6d-29c0-45d4-8437-f6353c8fbb74</processVariableId>
            <description isNull="true" />
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>dd9ba4f7-d800-4846-9f1e-05172e0a9247</guid>
            <versionId>9c04e878-14a2-40a7-979d-5681e24f2eca</versionId>
        </processVariable>
        <processVariable name="center">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.de241f50-98c0-485c-91cf-e151590a14d0</processVariableId>
            <description isNull="true" />
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4614ca5c-b4a6-45f3-85c9-c84ea054a54b</guid>
            <versionId>1bf3735d-3e8f-4473-bfad-90c4126e4740</versionId>
        </processVariable>
        <processVariable name="logError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b8db0cc6-0e44-4870-91d9-394311117c2b</processVariableId>
            <description isNull="true" />
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>13346094-b50b-4acd-a803-d835d8d05f98</guid>
            <versionId>449a3adc-bf20-45e4-a988-63e44f1db228</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a4f2ae9d-fa53-4179-aa99-a0f7b8cea825</processVariableId>
            <description isNull="true" />
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>84364ead-6429-41a9-a397-06d3032513d4</guid>
            <versionId>8af752bd-c02c-4bbc-bdc1-8f6ad4e03b87</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.d7494652-dd04-49eb-a64c-76d3bf06385b</processItemId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <name>Update Teams Name</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.11cf101f-**************-de8c21384cb7</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a11a6235c:3cde</guid>
            <versionId>0584c10d-5520-4302-ae88-e5065f65601a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="409" y="12">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.11cf101f-**************-de8c21384cb7</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.teamName = tw.local.filteredTeam.name;&#xD;
&#xD;
if (tw.local.teamName.indexOf("MKR") &gt; -1) {&#xD;
    tw.local.TEAM_MANAGER = tw.local.teamName.replace("MKR", "CHKR");&#xD;
} else {&#xD;
    tw.local.TEAM_MANAGER = tw.local.teamName&#xD;
}&#xD;
&#xD;
&#xD;
tw.local.parent_team_name = tw.epv.BPMTeamsNamesReassign.CA_Execution_MKR;&#xD;
    tw.local.tmpvariable = tw.epv.BPMTeamsNamesReassign.CA_Large_Execution_MKR_Manager;&#xD;
    tw.local.filteredTeam.managerTeam = tw.local.tmpvariable;&#xD;
    if (tw.local.teamName.indexOf("CHKR") &gt; -1) {&#xD;
        tw.local.parent_team_name = tw.local.parent_team_name.replace("Maker", "Checker");&#xD;
        tw.local.filteredTeam.managerTeam = tw.local.filteredTeam.managerTeam.replace("MKR", "CHKR");&#xD;
    }&#xD;
</script>
                <isRule>false</isRule>
                <guid>5edd9f73-1267-4a6c-9281-a174eb18752e</guid>
                <versionId>16502556-0174-4cf2-a948-35846b400140</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.d83c7880-9130-4942-b143-d160a6c41027</processItemId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <name>Get Center Code</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.b1e2bf46-7b87-4efd-b1b6-129f7bb409ae</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.a4f9bb4d-7857-491b-ae5d-b14ad542e004</errorHandlerItemId>
            <guid>guid:aa4c986259b1691d:-23a6d209:18a11a6235c:3ce0</guid>
            <versionId>19673fbb-8df9-4ae6-a5f4-8ef6f132e82b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.c5ee398e-0e6e-40f9-9d75-9d14fb0b4cf2</processItemPrePostId>
                <processItemId>2025.d83c7880-9130-4942-b143-d160a6c41027</processItemId>
                <location>2</location>
                <script>log.info(" ServiceName : Get Center Code: END");</script>
                <guid>c9c7933c-add0-43cf-8f6f-7c80a8dc17eb</guid>
                <versionId>73e3f429-bdb8-47bf-a06d-3aceecbe777d</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.4dee60e5-3a3c-4959-82fc-834f674e74b5</processItemPrePostId>
                <processItemId>2025.d83c7880-9130-4942-b143-d160a6c41027</processItemId>
                <location>1</location>
                <script>log.info(" ServiceName : Get Center Code: START");</script>
                <guid>352fd4fe-4b43-4927-8f6b-4cb0cd0b2f04</guid>
                <versionId>901e14f6-a06c-4453-a717-e959ed4ef051</versionId>
            </processPrePosts>
            <layoutData x="756" y="111">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:aa4c986259b1691d:-23a6d209:18a11a6235c:3cdb</errorHandlerItem>
                <errorHandlerItemId>2025.a4f9bb4d-7857-491b-ae5d-b14ad542e004</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.b1e2bf46-7b87-4efd-b1b6-129f7bb409ae</subProcessId>
                <attachedProcessRef>/1.caf369f2-a2d3-4b1b-bec5-ace8bfe23165</attachedProcessRef>
                <guid>47a07d30-a90c-421f-8a0e-f030d0d2563a</guid>
                <versionId>35b89100-7ffc-4654-a3bd-e72d6b843cbd</versionId>
                <parameterMapping name="Center">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.926ccd50-615f-4d0f-a7f3-dfed8a12fc91</parameterMappingId>
                    <processParameterId>2055.108d6047-03b9-47d9-abfc-55c662ab0907</processParameterId>
                    <parameterMappingParentId>3012.b1e2bf46-7b87-4efd-b1b6-129f7bb409ae</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.center</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>337d1e0a-0f9c-4315-8a5a-3b260c7c21cd</guid>
                    <versionId>6ca90ec2-4583-4a38-a33d-1e29ef76fa19</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="BranchCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7d7b5053-21a8-43a7-bf01-0ebb6495eda9</parameterMappingId>
                    <processParameterId>2055.cd1847f0-7862-4b2f-814c-daed841758b3</processParameterId>
                    <parameterMappingParentId>3012.b1e2bf46-7b87-4efd-b1b6-129f7bb409ae</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.branchCode</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>5c7f3f31-8aa0-49b9-81b7-00142bb14e89</guid>
                    <versionId>8413e943-79ce-40fd-90c1-4945ce991f1c</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3521bde9-bf66-48d2-b599-7b9c773e2e2f</processItemId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <name>Is LRGEC</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.ee44bd89-4d9d-4c60-8c3e-1fb6dec0357d</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a11a6235c:3cda</guid>
            <versionId>51a28c20-1920-4996-90b9-9fa73889c821</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.3caa9c38-16df-4816-ac6b-da69bd4b3085</processItemPrePostId>
                <processItemId>2025.3521bde9-bf66-48d2-b599-7b9c773e2e2f</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>12cc1bc8-c7af-4a4e-96d1-a97dc92c8532</guid>
                <versionId>3015d8da-d52a-47c2-a945-01219c7b3270</versionId>
            </processPrePosts>
            <layoutData x="414" y="141">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.ee44bd89-4d9d-4c60-8c3e-1fb6dec0357d</switchId>
                <guid>a71703de-9482-4eb8-a381-7793d7ec660e</guid>
                <versionId>dd57cf39-6d1f-45d3-bdec-7f43967ee40d</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.470ef044-566c-4cd8-9bb7-d28f309e6ad7</switchConditionId>
                    <switchId>3013.ee44bd89-4d9d-4c60-8c3e-1fb6dec0357d</switchId>
                    <seq>1</seq>
                    <endStateId>guid:9df5bce005da774d:-5bc6ffcb:18a1f9979de:3947</endStateId>
                    <condition>tw.local.filteredTeam.members.listLength	  &gt;	  0</condition>
                    <guid>b896cf85-53b9-450f-ae6c-71ad15e0e37f</guid>
                    <versionId>6eb5713b-a1d0-48c2-9e81-20c9140e3c7c</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.a12f1141-371e-4a5a-8153-c2a9a11a617e</processItemId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <name>Audit</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.d57c28a8-d843-4ec0-81d9-080c31de1c3f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.a4f9bb4d-7857-491b-ae5d-b14ad542e004</errorHandlerItemId>
            <guid>guid:aa4c986259b1691d:-23a6d209:18a11a6235c:3cdd</guid>
            <versionId>5c297562-0393-4349-be11-2b96face7786</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.42817dc4-3ba8-41e5-be8c-f3c292466cb1</processItemPrePostId>
                <processItemId>2025.a12f1141-371e-4a5a-8153-c2a9a11a617e</processItemId>
                <location>1</location>
                <script>log.info(" ServiceName : Audit: START");</script>
                <guid>a4b5a8cb-5c54-4d2c-99b0-a27756a9c572</guid>
                <versionId>a7222781-bc4c-49e0-a4e0-34d2da054277</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.79797b86-60b9-4704-9452-1b87043e05b3</processItemPrePostId>
                <processItemId>2025.a12f1141-371e-4a5a-8153-c2a9a11a617e</processItemId>
                <location>2</location>
                <script>log.info(" ServiceName : Audit: END");</script>
                <guid>9d183f38-e53e-472c-8ab9-ca1c7e7ee1f6</guid>
                <versionId>c5418f3c-83a8-46d2-b725-426fc3b03d01</versionId>
            </processPrePosts>
            <layoutData x="1075" y="121">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:aa4c986259b1691d:-23a6d209:18a11a6235c:3cdb</errorHandlerItem>
                <errorHandlerItemId>2025.a4f9bb4d-7857-491b-ae5d-b14ad542e004</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.d57c28a8-d843-4ec0-81d9-080c31de1c3f</subProcessId>
                <attachedProcessRef>/1.2d69e423-2547-4495-845d-7bccbf743136</attachedProcessRef>
                <guid>6d53b5bc-c29c-4f45-9717-69b86c5daeea</guid>
                <versionId>44400b7c-0bef-4166-9197-b5c2c100b353</versionId>
                <parameterMapping name="Operation">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b7322b88-9f41-4dc8-bc1f-53e879fa75e7</parameterMappingId>
                    <processParameterId>2055.72a822b9-58cf-4b48-9c2d-db5353162497</processParameterId>
                    <parameterMappingParentId>3012.d57c28a8-d843-4ec0-81d9-080c31de1c3f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>1</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>1e3eedfe-8df9-4887-994c-e5f572581548</guid>
                    <versionId>1b621dfa-71cc-402d-811d-905300f2f402</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="FILTER_BRANCH">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.4ac25cfe-56db-470f-ab0c-323b824a1842</parameterMappingId>
                    <processParameterId>2055.f7296d68-138b-4e58-b910-ce7df924d7f8</processParameterId>
                    <parameterMappingParentId>3012.d57c28a8-d843-4ec0-81d9-080c31de1c3f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>""</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>6dbfddcf-035a-40fa-ac38-cc2144b3a3aa</guid>
                    <versionId>20c53d70-1215-4c62-a939-68ac239bb008</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="stepName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3689f354-6c57-4ed8-95f2-91aff8e07462</parameterMappingId>
                    <processParameterId>2055.d1976350-35cc-4bb7-be72-0d2c2c4e976b</processParameterId>
                    <parameterMappingParentId>3012.d57c28a8-d843-4ec0-81d9-080c31de1c3f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.stepName</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>3f37ad79-afe1-4896-8ad3-aac85774295c</guid>
                    <versionId>2488c9e3-0129-4573-ab69-b5b5b43c3238</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="STATUS">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.5981faa1-b5ae-46be-8824-1e3d7538a85c</parameterMappingId>
                    <processParameterId>2055.11effbb1-5ad3-4f99-a23c-01eda7584e00</processParameterId>
                    <parameterMappingParentId>3012.d57c28a8-d843-4ec0-81d9-080c31de1c3f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"1"</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>749a2649-27e0-4eb7-9f38-c091f15d214a</guid>
                    <versionId>28ffc1c7-0d9d-402f-bfc3-1729923ede58</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="TEAM_MANAGER">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.73952130-ebee-45ef-a995-a7921f0a6303</parameterMappingId>
                    <processParameterId>2055.df186765-bf04-4089-99d9-3f84a4866be2</processParameterId>
                    <parameterMappingParentId>3012.d57c28a8-d843-4ec0-81d9-080c31de1c3f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.TEAM_MANAGER</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>df2bf028-cce6-4bf5-b383-74ad9a4ae99b</guid>
                    <versionId>40aac344-700e-415a-aaa5-945d5a8ca1e8</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="Statuss">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f821b9a1-ac27-4836-9b99-ce6de54e0f53</parameterMappingId>
                    <processParameterId>2055.b08b5b31-85f5-4e92-b629-d45112e9085e</processParameterId>
                    <parameterMappingParentId>3012.d57c28a8-d843-4ec0-81d9-080c31de1c3f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>6efed60c-a185-4ff0-875a-1c3bae19eded</guid>
                    <versionId>6cb56a9a-db82-4682-b3e6-184a8a92714d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="TEAM_NAME">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.2fe34a1c-2072-4a7e-9c4c-226fa5afda7a</parameterMappingId>
                    <processParameterId>2055.e91e9ebe-6e70-4b06-996b-54441169b3ca</processParameterId>
                    <parameterMappingParentId>3012.d57c28a8-d843-4ec0-81d9-080c31de1c3f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.teamName</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>0e1c820d-0241-42c8-bd59-0f614c5a947e</guid>
                    <versionId>8f106940-425c-4d2b-a6f1-62ad4ed28d8f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="INSTANCE_ID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3d17e385-7a90-4b9f-8a70-632d2305d8b2</parameterMappingId>
                    <processParameterId>2055.0dbed104-185b-44f0-9303-0765cbdb781e</processParameterId>
                    <parameterMappingParentId>3012.d57c28a8-d843-4ec0-81d9-080c31de1c3f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.InstanceID</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>39d3a29d-2656-49e4-a94b-e55163372cf2</guid>
                    <versionId>98573a4d-9a55-4f39-a8cb-54603a73cc8b</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="OWNER_ID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.5e772593-c999-4768-b9c6-4a312bb9e560</parameterMappingId>
                    <processParameterId>2055.8fa8039e-dbe6-4ecf-8897-769135f4dca7</processParameterId>
                    <parameterMappingParentId>3012.d57c28a8-d843-4ec0-81d9-080c31de1c3f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>""</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>9c177961-f0e8-4bf9-96b9-3aeb4139a093</guid>
                    <versionId>ab4e67f2-20aa-4b02-b98e-8034565fb287</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="TASK_ID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a62afa09-8246-467b-be17-cfab0d4e178e</parameterMappingId>
                    <processParameterId>2055.8129799a-130c-4004-beb4-d73c68f78c65</processParameterId>
                    <parameterMappingParentId>3012.d57c28a8-d843-4ec0-81d9-080c31de1c3f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>""</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>27443752-f5dc-4fce-bb78-3f6340ba6b00</guid>
                    <versionId>dcf3bfee-40ca-44cc-a7ac-c5babd1137ef</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.a4f9bb4d-7857-491b-ae5d-b14ad542e004</processItemId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <name>log Error</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.f2c24ab0-23ff-4e5d-be29-a0ae3e7dca71</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a11a6235c:3cdb</guid>
            <versionId>80005443-2cea-4515-a3f9-834dd8a8a844</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="573" y="239">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.f2c24ab0-23ff-4e5d-be29-a0ae3e7dca71</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>log.info("*============ NBE letter Of Credit =============*");&#xD;
log.info("*========================================*");&#xD;
log.info("[Filter CA Team -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
tw.local.errorMSG=String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Filter CA Team -&gt; Log Error ]- END");&#xD;
log.info("*================================================*");</script>
                <isRule>false</isRule>
                <guid>609b389c-66e7-48df-adb8-7ef4926ae14b</guid>
                <versionId>b96f4c96-0a35-495d-abd0-55b41d518243</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6e95846b-e400-4cb9-91d3-c08fe5150494</processItemId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.08757bda-ee68-481e-916f-9d35596f4ba8</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a11a6235c:3ce1</guid>
            <versionId>8299f2b2-64d7-468f-ac2e-5b472c5ff3fa</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="792" y="262">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.08757bda-ee68-481e-916f-9d35596f4ba8</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>7c3f09dd-5f59-4594-9096-caee75393c18</guid>
                <versionId>fa5195cb-0f34-475a-83cd-4f6abbc7115f</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a65ad4ab-f65a-46c8-b4f2-5d05d0bdeeff</parameterMappingId>
                    <processParameterId>2055.14c513ad-5260-4fa8-9bdf-c9b31108977e</processParameterId>
                    <parameterMappingParentId>3007.08757bda-ee68-481e-916f-9d35596f4ba8</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>01e5acd6-cc64-4b6e-96bd-aef4986874ce</guid>
                    <versionId>37e026af-b61f-4fbd-b41c-bd674c28bbb9</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.7777e18e-01c2-41a0-b497-7cbd0b4db94b</processItemId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <name>Get Team From LDAP</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.9467d5d5-37ee-4d9f-ac4f-1dc53320303d</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a11a6235c:3cdf</guid>
            <versionId>9c8d1681-50d0-4875-8ef7-aec968cfb41a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="940" y="121">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.9467d5d5-37ee-4d9f-ac4f-1dc53320303d</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>writeLog(" Filter CA Team ");&#xD;
//writeLog("tw.local.center.value", tw.local.center.value);&#xD;
writeLog("tw.local.Role", tw.local.Role);&#xD;
log.info("is My Team Here ......."+tw.local.teamName);&#xD;
&#xD;
if (tw.local.center != null) {&#xD;
    var Users;&#xD;
    tw.local.teamName = tw.local.center.value + tw.local.Role;&#xD;
    Users = tw.system.org.findRoleByName(tw.local.teamName).allUsers;&#xD;
&#xD;
    tw.local.filteredTeam = new tw.object.Team();&#xD;
    tw.local.filteredTeam.name = tw.local.teamName;&#xD;
    tw.local.filteredTeam.members = new tw.object.listOf.String();&#xD;
log.info("Where Is My Team ......."+tw.local.teamName);&#xD;
&#xD;
    for (var i = 0; i &lt; Users.length; i++) {&#xD;
        tw.local.filteredTeam.members[i] = Users[i].name;&#xD;
    }&#xD;
    &#xD;
    tw.local.TEAM_MANAGER = tw.local.teamName; //make checker is manager&#xD;
    if (tw.local.teamName.indexOf("MKR") &gt; -1) {&#xD;
        tw.local.TEAM_MANAGER = tw.local.teamName.replace("MKR", "CHKR");&#xD;
    }&#xD;
}&#xD;
&#xD;
UpdateTeamParentName(tw.local.teamName);&#xD;
&#xD;
//////////////////////////////////////&#xD;
&#xD;
function UpdateTeamParentName(teamname) {&#xD;
    try {&#xD;
        if (teamname.indexOf("_CHKR") != -1 || teamname.indexOf("_Chkr") != -1) {&#xD;
            tw.local.parent_team_name = tw.epv.BPMTeamsNamesReassign.CA_Execution_CHKR;&#xD;
            tw.local.tmpvariable = tw.epv.BPMTeamsNamesReassign.CA_Large_Execution_CHKR_Manager;&#xD;
            tw.local.filteredTeam.managerTeam = tw.local.tmpvariable;&#xD;
        } else {&#xD;
            tw.local.parent_team_name = tw.epv.BPMTeamsNamesReassign.CA_Execution_MKR;&#xD;
            tw.local.tmpvariable = tw.epv.BPMTeamsNamesReassign.CA_Large_Execution_MKR_Manager;&#xD;
            tw.local.filteredTeam.managerTeam = tw.local.tmpvariable;&#xD;
        }&#xD;
    } catch (err) {&#xD;
       writeLog(err)&#xD;
    }&#xD;
&#xD;
}&#xD;
&#xD;
function writeLog(msg, variable) { // vairable is optional&#xD;
    var instanceID = "";&#xD;
    try {&#xD;
        instanceID = "Instance ID : " + tw.system.currentProcessInstance.id + "  ::  ";&#xD;
    } catch (err) {}&#xD;
    variable == undefined ? variable = " " : variable = " : " + variable;&#xD;
    var message = instanceID + tw.system.model.processApp.name + " :: " + tw.system.serviceFlow.type + " :: " + tw.system.serviceFlow.name + " :: " + msg + " : " + variable;&#xD;
    log.info(message);&#xD;
   // tw.local.logError.insertIntoList(tw.local.logError.listLength, message);&#xD;
}</script>
                <isRule>false</isRule>
                <guid>c703cc62-55b0-4b0c-8ac5-9db4ebc1371e</guid>
                <versionId>72bf3268-c178-44c6-8524-11a47d4ed3c3</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6dee4bc5-23e3-4d72-bc44-067c03344aa7</processItemId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.23792bd1-345c-4d9e-83e7-694fa3dff658</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a11a6235c:3cdc</guid>
            <versionId>9e41c201-85a7-4476-9493-c5b0f585a767</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.2c9dd6cd-c57b-4660-9b4c-bb23485ea812</processItemPrePostId>
                <processItemId>2025.6dee4bc5-23e3-4d72-bc44-067c03344aa7</processItemId>
                <location>2</location>
                <script>log.info("ProcessInstance : "+tw.system.currentProcessInstanceID +" - ServiceName : Filter CA Team : END");</script>
                <guid>d5f4a01d-6816-48a9-8eba-6a6a2bb60661</guid>
                <versionId>e548d601-ada6-47c1-b4df-328a93438d71</versionId>
            </processPrePosts>
            <layoutData x="1225" y="144">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.23792bd1-345c-4d9e-83e7-694fa3dff658</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>cfeb91db-f4e7-4c69-80fb-8262ff7b6309</guid>
                <versionId>244d6d6f-a8a1-4d8c-beca-503ad8646a1f</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.88618851-656d-4ce4-82d9-99ada9bc849e</processItemId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <name>Multi Facility</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.fb19c4cc-c838-45b1-8832-60f7a3dd67e3</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a11a6235c:3cd9</guid>
            <versionId>c15fe6d0-6d99-45fd-a305-53c8b2acf943</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="566" y="121">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.fb19c4cc-c838-45b1-8832-60f7a3dd67e3</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>var maxperc = 0;&#xD;
&#xD;
for (var i=0; i&lt;tw.local.Facilities.listLength; i++) {&#xD;
	for (var j=0; j &lt; tw.local.Facilities[i].facilityLines.listLength; j++) {&#xD;
		if (tw.local.Facilities[i].facilityLines[j].facilityPercentageToBook &gt; maxperc) {&#xD;
			tw.local.branchCode = tw.local.Facilities[i].facilityLines[j].facilityBranch.value;&#xD;
			maxperc = tw.local.Facilities[i].facilityLines[j].facilityPercentageToBook;&#xD;
		}&#xD;
	}&#xD;
}&#xD;
&#xD;
writeLog("more than one facility ");&#xD;
&#xD;
function writeLog(msg , variable){  // vairable is optional&#xD;
	var instanceID = "";&#xD;
	try {instanceID = "Instance ID : "+tw.system.currentProcessInstance.id  +"  ::  ";} catch (err) {}&#xD;
	variable == undefined ? variable =" " : variable = " : "+ variable ;&#xD;
	var message = instanceID + tw.system.model.processApp.name  +" :: " + tw.system.serviceFlow.type +" :: "+ tw.system.serviceFlow.name +" :: "  + msg+" : " + variable;&#xD;
	log.info( message);&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>95582881-e385-4250-8c02-71ee53f4cd11</guid>
                <versionId>1a2b7156-03bb-43ec-adb9-4e1b08b9d8c4</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.7108b2dc-3135-444c-9b6d-e9066702fdca</processItemId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <name>Check Assign Large Corprate</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.aaa5599e-4af9-41d7-896f-87a905f77cba</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.a4f9bb4d-7857-491b-ae5d-b14ad542e004</errorHandlerItemId>
            <guid>guid:aa4c986259b1691d:-23a6d209:18a11a6235c:3cd8</guid>
            <versionId>efa7431c-9f10-49c2-8e9d-d8e33d0d0854</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.fe815587-5d65-44c9-9c54-55fa21b2e35a</processItemPrePostId>
                <processItemId>2025.7108b2dc-3135-444c-9b6d-e9066702fdca</processItemId>
                <location>2</location>
                <script>log.info(" ServiceName : Check Assign Large Corprate : START");</script>
                <guid>61c0a28e-120d-4ff8-9c3b-d1e068944059</guid>
                <versionId>5c52f037-b757-48b4-9d95-3c43d4eb1a54</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.0d570140-6c80-418b-b3c2-3f86e0ccbc8e</processItemPrePostId>
                <processItemId>2025.7108b2dc-3135-444c-9b6d-e9066702fdca</processItemId>
                <location>1</location>
                <script>log.info("ProcessInstance : "+tw.system.currentProcessInstanceID +" - ServiceName : Filter CA Team : START");&#xD;
log.info(" ServiceName : Check Assign Large Corprate : START");</script>
                <guid>2511782a-b782-4cd9-9176-ce45a1850137</guid>
                <versionId>f68bbe98-cbe3-4ddb-9348-f7cede085c14</versionId>
            </processPrePosts>
            <layoutData x="250" y="127">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:aa4c986259b1691d:-23a6d209:18a11a6235c:3cdb</errorHandlerItem>
                <errorHandlerItemId>2025.a4f9bb4d-7857-491b-ae5d-b14ad542e004</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.aaa5599e-4af9-41d7-896f-87a905f77cba</subProcessId>
                <attachedProcessRef>/1.37eab8a6-3b8b-4e52-8824-851827e9889b</attachedProcessRef>
                <guid>26cc0cbe-517e-4bdd-8bc3-81290a5f1c8a</guid>
                <versionId>06912958-a35f-40c7-a6cf-00d2a3b51ded</versionId>
                <parameterMapping name="teamSuffix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.bef573f4-aa60-49a3-95b8-19b0b65995fb</parameterMappingId>
                    <processParameterId>2055.5f070a30-da63-42e9-a0e0-************</processParameterId>
                    <parameterMappingParentId>3012.aaa5599e-4af9-41d7-896f-87a905f77cba</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.teamSuffix</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>6b2270d1-bddc-4c22-b00d-584b1329688b</guid>
                    <versionId>90a68ea1-629c-418a-bbd3-7be184deb50a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="CIF">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.5937fa54-ed43-49eb-b8b2-f98e7c228ad0</parameterMappingId>
                    <processParameterId>2055.a2a70141-40f9-485d-84cf-53aff58969a6</processParameterId>
                    <parameterMappingParentId>3012.aaa5599e-4af9-41d7-896f-87a905f77cba</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.CIF</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>e3bbf849-511e-41a8-b4c0-9c74805548cf</guid>
                    <versionId>d1e08aff-72b7-4140-9c75-53eeef23a686</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="filteredTeam">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d9e15e49-22fc-4a54-91a7-ea149dfa377e</parameterMappingId>
                    <processParameterId>2055.4c821ca4-c74b-4703-b8dd-a17c9b0cbc8d</processParameterId>
                    <parameterMappingParentId>3012.aaa5599e-4af9-41d7-896f-87a905f77cba</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.filteredTeam</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>dad8097b-2ffb-4774-846d-ae60712d621b</guid>
                    <versionId>f7892a97-f4f0-455e-83c8-a453d970b05b</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.95051c02-224a-48a9-b2f4-6ceebd24a815</epvProcessLinkId>
            <epvId>/21.f83b19ad-8a7f-4026-bded-63fd31478fbd</epvId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <guid>f07e7803-0d37-453a-baf4-5d970398c0a1</guid>
            <versionId>f9668fc9-aedb-4bdb-ae8e-6df3853a1c3e</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.7108b2dc-3135-444c-9b6d-e9066702fdca</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="34" y="145">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Filter CA Team" id="1.b066b0aa-3b41-4663-a2f1-ca880f07a183" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification ns3:readOnlyOutputs="true">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.f83b19ad-8a7f-4026-bded-63fd31478fbd" epvProcessLinkId="ecd146fe-6d1a-4425-8b39-f415175fdde2" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="originalTeam" itemSubjectRef="itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0" isCollection="false" id="2055.5b4c0c07-ddcd-4ca2-abd6-0bc175e99794" ns3:readOnly="true" />
                        
                        
                        <ns16:dataInput name="Role" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.a858a1e4-ba29-452b-acec-21f8428d40c5">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"_EXE_MKR"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="CIF" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.765c6f1c-6674-485d-9994-8791770b83ef">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"10101010"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="teamSuffix" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.53db05f6-43c4-4dbf-8dff-92ed59498a7c">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"BPM_CA_LRG_CORP_EXE_MKR"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="Facilities" itemSubjectRef="itm.12.a1b91a74-fa62-493b-9eb7-fa29e1fda20c" isCollection="true" id="2055.0e9a2ca1-f260-4db8-96c1-3c698efa22a1">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">var autoObject = new tw.object.listOf.toolkit.NBEC.creditFacilityInformation();
autoObject[0] = new tw.object.toolkit.NBEC.creditFacilityInformation();
autoObject[0].facilityCode = "";
autoObject[0].overallLimit = 0.0;
autoObject[0].limitAmount = 0.0;
autoObject[0].effectiveLimitAmount = 0.0;
autoObject[0].availableAmount = 0.0;
autoObject[0].expiryDate = new TWDate();
autoObject[0].availableFlag = false;
autoObject[0].authorizedFlag = false;
autoObject[0].Utilization = 0.0;
autoObject[0].returnCode = "";
autoObject[0].facilityLines = new tw.object.listOf.toolkit.NBEC.FacilityLinesDetails();
autoObject[0].facilityLines[0] = new tw.object.toolkit.NBEC.FacilityLinesDetails();
autoObject[0].facilityLines[0].lineCode = "";
autoObject[0].facilityLines[0].lineAmount = 0.0;
autoObject[0].facilityLines[0].availableAmount = 0.0;
autoObject[0].facilityLines[0].effectiveLineAmount = 0.0;
autoObject[0].facilityLines[0].expiryDate = new TWDate();
autoObject[0].facilityLines[0].facilityBranch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject[0].facilityLines[0].facilityBranch.name = "";
autoObject[0].facilityLines[0].facilityBranch.value = "001";
autoObject[0].facilityLines[0].availableFlag = false;
autoObject[0].facilityLines[0].authorizedFlag = false;
autoObject[0].facilityLines[0].facilityPercentageToBook = 20;
autoObject[0].facilityLines[0].internalRemarks = "";
autoObject[0].facilityLines[0].purpose = "";
autoObject[0].facilityLines[0].LCCommissionPercentage = 0;
autoObject[0].facilityLines[0].LCDef = "";
autoObject[0].facilityLines[0].LCCashCover = "";
autoObject[0].facilityLines[0].IDCCommission = "";
autoObject[0].facilityLines[0].IDCAvalCommPercentage = 0;
autoObject[0].facilityLines[0].IDCCashCoverPercentage = 0;
autoObject[0].facilityLines[0].debitAccountNumber = "";
autoObject[0].facilityLines[0].lineCurrency = "";
autoObject[0].facilityLines[0].lineSerialNumber = "";
autoObject[0].facilityLines[0].returnCode = "";
autoObject[0].facilityLines[0].LGCommission = 0;
autoObject[0].facilityLines[0].LGCashCover_BidBond = 0;
autoObject[0].facilityLines[0].LGCashCover_Performance = 0;
autoObject[0].facilityLines[0].LGCashCover_AdvancedPayment = 0;
autoObject[0].facilityLines[0].restrictedCurrencies = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject[0].facilityLines[0].restrictedCurrencies[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject[0].facilityLines[0].restrictedCurrencies[0].name = "";
autoObject[0].facilityLines[0].restrictedCurrencies[0].code = "";
autoObject[0].facilityLines[0].restrictedCurrencies[0].source = "";
autoObject[0].facilityLines[0].restrictedCurrencies[0].status = "";
autoObject[0].facilityLines[0].restrictedBranches = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject[0].facilityLines[0].restrictedBranches[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject[0].facilityLines[0].restrictedBranches[0].name = "";
autoObject[0].facilityLines[0].restrictedBranches[0].code = "";
autoObject[0].facilityLines[0].restrictedBranches[0].source = "";
autoObject[0].facilityLines[0].restrictedBranches[0].status = "";
autoObject[0].facilityLines[0].restrictedProducts = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject[0].facilityLines[0].restrictedProducts[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject[0].facilityLines[0].restrictedProducts[0].name = "";
autoObject[0].facilityLines[0].restrictedProducts[0].code = "";
autoObject[0].facilityLines[0].restrictedProducts[0].source = "";
autoObject[0].facilityLines[0].restrictedProducts[0].status = "";
autoObject[0].facilityLines[0].restrictedCustomers = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject[0].facilityLines[0].restrictedCustomers[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject[0].facilityLines[0].restrictedCustomers[0].name = "";
autoObject[0].facilityLines[0].restrictedCustomers[0].code = "";
autoObject[0].facilityLines[0].restrictedCustomers[0].source = "";
autoObject[0].facilityLines[0].restrictedCustomers[0].status = "";
autoObject[0].facilityLines[0].exposureRestrictions = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject[0].facilityLines[0].exposureRestrictions[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject[0].facilityLines[0].exposureRestrictions[0].name = "";
autoObject[0].facilityLines[0].exposureRestrictions[0].code = "";
autoObject[0].facilityLines[0].exposureRestrictions[0].source = "";
autoObject[0].facilityLines[0].exposureRestrictions[0].status = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="InstanceID" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.0fae7af1-2e66-4f04-981b-1d9ff6f8491d" />
                        
                        
                        <ns16:dataInput name="branchCode" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.5d310308-0665-45be-8f3a-e4b6d674d770">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">//"001"&#xD;
"100"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="stepName" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.021e0e40-963b-43f5-81c8-a01b0c3b2b47" />
                        
                        
                        <ns16:dataOutput name="filteredTeam" itemSubjectRef="itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0" isCollection="false" id="2055.5a25aaa2-7dd9-4586-9b1a-fdb9ef097535" ns3:readOnly="true" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.5b4c0c07-ddcd-4ca2-abd6-0bc175e99794</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.a858a1e4-ba29-452b-acec-21f8428d40c5</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.765c6f1c-6674-485d-9994-8791770b83ef</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.53db05f6-43c4-4dbf-8dff-92ed59498a7c</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.0e9a2ca1-f260-4db8-96c1-3c698efa22a1</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.0fae7af1-2e66-4f04-981b-1d9ff6f8491d</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.5d310308-0665-45be-8f3a-e4b6d674d770</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.021e0e40-963b-43f5-81c8-a01b0c3b2b47</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="5a3ebd06-7477-4a19-a8b3-b7b3d07cd3b1">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="b550c3ab-2780-4856-ac64-8110d949be61" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>a8949386-a4b7-48a8-a42d-ceb5770465c3</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6dee4bc5-23e3-4d72-bc44-067c03344aa7</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>7108b2dc-3135-444c-9b6d-e9066702fdca</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3521bde9-bf66-48d2-b599-7b9c773e2e2f</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>d7494652-dd04-49eb-a64c-76d3bf06385b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>88618851-656d-4ce4-82d9-99ada9bc849e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>d83c7880-9130-4942-b143-d160a6c41027</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>7777e18e-01c2-41a0-b497-7cbd0b4db94b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a12f1141-371e-4a5a-8153-c2a9a11a617e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1e54a6ee-38ae-48f0-a614-cafc941a68ec</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>bb2a0dea-d40e-42d2-aa6a-9eed6ee4ac69</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>777e6d48-4c54-45a2-aa6c-bcaf051cae4f</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a4f9bb4d-7857-491b-ae5d-b14ad542e004</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6e95846b-e400-4cb9-91d3-c08fe5150494</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="a8949386-a4b7-48a8-a42d-ceb5770465c3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="34" y="145" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.9650e8bd-3f84-4f0e-89d2-c6faacc844d6</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="6dee4bc5-23e3-4d72-bc44-067c03344aa7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1225" y="144" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:aa4c986259b1691d:-23a6d209:18a11a6235c:3cdc</ns3:endStateId>
                            
                            
                            <ns3:postAssignmentScript>log.info("ProcessInstance : "+tw.system.currentProcessInstanceID +" - ServiceName : Filter CA Team : END");</ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>f55c3bcd-efb0-42f3-acee-43dc5ba0ce80</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="a8949386-a4b7-48a8-a42d-ceb5770465c3" targetRef="7108b2dc-3135-444c-9b6d-e9066702fdca" name="To Check Assign Large Corprate" id="2027.9650e8bd-3f84-4f0e-89d2-c6faacc844d6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.37eab8a6-3b8b-4e52-8824-851827e9889b" name="Check Assign Large Corprate" id="7108b2dc-3135-444c-9b6d-e9066702fdca">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="250" y="127" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript>log.info("ProcessInstance : "+tw.system.currentProcessInstanceID +" - ServiceName : Filter CA Team : START");&#xD;
log.info(" ServiceName : Check Assign Large Corprate : START");</ns3:preAssignmentScript>
                            
                            
                            <ns3:postAssignmentScript>log.info(" ServiceName : Check Assign Large Corprate : START");</ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.9650e8bd-3f84-4f0e-89d2-c6faacc844d6</ns16:incoming>
                        
                        
                        <ns16:outgoing>bddb98e9-0204-4867-be7c-71beb4f59f06</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a2a70141-40f9-485d-84cf-53aff58969a6</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.CIF</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5f070a30-da63-42e9-a0e0-************</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.teamSuffix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.4c821ca4-c74b-4703-b8dd-a17c9b0cbc8d</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0">tw.local.filteredTeam</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:exclusiveGateway default="96379411-1c4a-4f75-bdde-c7fa3bdc4396" name="Is LRGEC" id="3521bde9-bf66-48d2-b599-7b9c773e2e2f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="414" y="141" width="32" height="32" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>bddb98e9-0204-4867-be7c-71beb4f59f06</ns16:incoming>
                        
                        
                        <ns16:outgoing>96379411-1c4a-4f75-bdde-c7fa3bdc4396</ns16:outgoing>
                        
                        
                        <ns16:outgoing>a0d53811-6a0f-4ab5-a47d-2c29ac0d21e0</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="3521bde9-bf66-48d2-b599-7b9c773e2e2f" targetRef="88618851-656d-4ce4-82d9-99ada9bc849e" name="To Multi Facility" id="96379411-1c4a-4f75-bdde-c7fa3bdc4396">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Update Teams Name" id="d7494652-dd04-49eb-a64c-76d3bf06385b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="409" y="12" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>a0d53811-6a0f-4ab5-a47d-2c29ac0d21e0</ns16:incoming>
                        
                        
                        <ns16:outgoing>fc1cefa7-d336-49e2-bf33-2629b59f0714</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.teamName = tw.local.filteredTeam.name;&#xD;
&#xD;
if (tw.local.teamName.indexOf("MKR") &gt; -1) {&#xD;
    tw.local.TEAM_MANAGER = tw.local.teamName.replace("MKR", "CHKR");&#xD;
} else {&#xD;
    tw.local.TEAM_MANAGER = tw.local.teamName&#xD;
}&#xD;
&#xD;
&#xD;
tw.local.parent_team_name = tw.epv.BPMTeamsNamesReassign.CA_Execution_MKR;&#xD;
    tw.local.tmpvariable = tw.epv.BPMTeamsNamesReassign.CA_Large_Execution_MKR_Manager;&#xD;
    tw.local.filteredTeam.managerTeam = tw.local.tmpvariable;&#xD;
    if (tw.local.teamName.indexOf("CHKR") &gt; -1) {&#xD;
        tw.local.parent_team_name = tw.local.parent_team_name.replace("Maker", "Checker");&#xD;
        tw.local.filteredTeam.managerTeam = tw.local.filteredTeam.managerTeam.replace("MKR", "CHKR");&#xD;
    }&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="3521bde9-bf66-48d2-b599-7b9c773e2e2f" targetRef="d7494652-dd04-49eb-a64c-76d3bf06385b" name="To Update Teams Name" id="a0d53811-6a0f-4ab5-a47d-2c29ac0d21e0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.filteredTeam.members.listLength	  &gt;	  0</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Multi Facility" id="88618851-656d-4ce4-82d9-99ada9bc849e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="566" y="121" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>96379411-1c4a-4f75-bdde-c7fa3bdc4396</ns16:incoming>
                        
                        
                        <ns16:outgoing>29f099b4-5fcd-4f3d-aef8-64efc389a0ca</ns16:outgoing>
                        
                        
                        <ns16:script>var maxperc = 0;&#xD;
&#xD;
for (var i=0; i&lt;tw.local.Facilities.listLength; i++) {&#xD;
	for (var j=0; j &lt; tw.local.Facilities[i].facilityLines.listLength; j++) {&#xD;
		if (tw.local.Facilities[i].facilityLines[j].facilityPercentageToBook &gt; maxperc) {&#xD;
			tw.local.branchCode = tw.local.Facilities[i].facilityLines[j].facilityBranch.value;&#xD;
			maxperc = tw.local.Facilities[i].facilityLines[j].facilityPercentageToBook;&#xD;
		}&#xD;
	}&#xD;
}&#xD;
&#xD;
writeLog("more than one facility ");&#xD;
&#xD;
function writeLog(msg , variable){  // vairable is optional&#xD;
	var instanceID = "";&#xD;
	try {instanceID = "Instance ID : "+tw.system.currentProcessInstance.id  +"  ::  ";} catch (err) {}&#xD;
	variable == undefined ? variable =" " : variable = " : "+ variable ;&#xD;
	var message = instanceID + tw.system.model.processApp.name  +" :: " + tw.system.serviceFlow.type +" :: "+ tw.system.serviceFlow.name +" :: "  + msg+" : " + variable;&#xD;
	log.info( message);&#xD;
}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="88618851-656d-4ce4-82d9-99ada9bc849e" targetRef="d83c7880-9130-4942-b143-d160a6c41027" name="To Get Center Code" id="29f099b4-5fcd-4f3d-aef8-64efc389a0ca">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="teamName" id="2056.6f9e3a45-1b3f-447a-9596-85635b95c30e" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="TEAM_MANAGER" id="2056.a4d9e949-7303-4c87-a1be-9c5263e4e5f9" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="parent_team_name" id="2056.d4d58734-8ef5-421f-b32f-90d5fc80fa55" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="tmpvariable" id="2056.13b0fb6d-29c0-45d4-8437-f6353c8fbb74" />
                    
                    
                    <ns16:callActivity calledElement="1.caf369f2-a2d3-4b1b-bec5-ace8bfe23165" name="Get Center Code" id="d83c7880-9130-4942-b143-d160a6c41027">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="756" y="111" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript>log.info(" ServiceName : Get Center Code: START");</ns3:preAssignmentScript>
                            
                            
                            <ns3:postAssignmentScript>log.info(" ServiceName : Get Center Code: END");</ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>29f099b4-5fcd-4f3d-aef8-64efc389a0ca</ns16:incoming>
                        
                        
                        <ns16:outgoing>91e37f5e-76d2-4721-9d74-2f8a83db859f</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.cd1847f0-7862-4b2f-814c-daed841758b3</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.branchCode</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.108d6047-03b9-47d9-abfc-55c662ab0907</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13">tw.local.center</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="d83c7880-9130-4942-b143-d160a6c41027" targetRef="7777e18e-01c2-41a0-b497-7cbd0b4db94b" name="To Get Team From LDAP" id="91e37f5e-76d2-4721-9d74-2f8a83db859f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-2b0b</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="false" name="center" id="2056.de241f50-98c0-485c-91cf-e151590a14d0" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Get Team From LDAP" id="7777e18e-01c2-41a0-b497-7cbd0b4db94b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="940" y="121" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>91e37f5e-76d2-4721-9d74-2f8a83db859f</ns16:incoming>
                        
                        
                        <ns16:outgoing>e05da848-30c5-4f7a-b8f1-549cb9769638</ns16:outgoing>
                        
                        
                        <ns16:script>writeLog(" Filter CA Team ");&#xD;
//writeLog("tw.local.center.value", tw.local.center.value);&#xD;
writeLog("tw.local.Role", tw.local.Role);&#xD;
log.info("is My Team Here ......."+tw.local.teamName);&#xD;
&#xD;
if (tw.local.center != null) {&#xD;
    var Users;&#xD;
    tw.local.teamName = tw.local.center.value + tw.local.Role;&#xD;
    Users = tw.system.org.findRoleByName(tw.local.teamName).allUsers;&#xD;
&#xD;
    tw.local.filteredTeam = new tw.object.Team();&#xD;
    tw.local.filteredTeam.name = tw.local.teamName;&#xD;
    tw.local.filteredTeam.members = new tw.object.listOf.String();&#xD;
log.info("Where Is My Team ......."+tw.local.teamName);&#xD;
&#xD;
    for (var i = 0; i &lt; Users.length; i++) {&#xD;
        tw.local.filteredTeam.members[i] = Users[i].name;&#xD;
    }&#xD;
    &#xD;
    tw.local.TEAM_MANAGER = tw.local.teamName; //make checker is manager&#xD;
    if (tw.local.teamName.indexOf("MKR") &gt; -1) {&#xD;
        tw.local.TEAM_MANAGER = tw.local.teamName.replace("MKR", "CHKR");&#xD;
    }&#xD;
}&#xD;
&#xD;
UpdateTeamParentName(tw.local.teamName);&#xD;
&#xD;
//////////////////////////////////////&#xD;
&#xD;
function UpdateTeamParentName(teamname) {&#xD;
    try {&#xD;
        if (teamname.indexOf("_CHKR") != -1 || teamname.indexOf("_Chkr") != -1) {&#xD;
            tw.local.parent_team_name = tw.epv.BPMTeamsNamesReassign.CA_Execution_CHKR;&#xD;
            tw.local.tmpvariable = tw.epv.BPMTeamsNamesReassign.CA_Large_Execution_CHKR_Manager;&#xD;
            tw.local.filteredTeam.managerTeam = tw.local.tmpvariable;&#xD;
        } else {&#xD;
            tw.local.parent_team_name = tw.epv.BPMTeamsNamesReassign.CA_Execution_MKR;&#xD;
            tw.local.tmpvariable = tw.epv.BPMTeamsNamesReassign.CA_Large_Execution_MKR_Manager;&#xD;
            tw.local.filteredTeam.managerTeam = tw.local.tmpvariable;&#xD;
        }&#xD;
    } catch (err) {&#xD;
       writeLog(err)&#xD;
    }&#xD;
&#xD;
}&#xD;
&#xD;
function writeLog(msg, variable) { // vairable is optional&#xD;
    var instanceID = "";&#xD;
    try {&#xD;
        instanceID = "Instance ID : " + tw.system.currentProcessInstance.id + "  ::  ";&#xD;
    } catch (err) {}&#xD;
    variable == undefined ? variable = " " : variable = " : " + variable;&#xD;
    var message = instanceID + tw.system.model.processApp.name + " :: " + tw.system.serviceFlow.type + " :: " + tw.system.serviceFlow.name + " :: " + msg + " : " + variable;&#xD;
    log.info(message);&#xD;
   // tw.local.logError.insertIntoList(tw.local.logError.listLength, message);&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="7777e18e-01c2-41a0-b497-7cbd0b4db94b" targetRef="a12f1141-371e-4a5a-8153-c2a9a11a617e" name="To Audit" id="e05da848-30c5-4f7a-b8f1-549cb9769638">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="logError" id="2056.b8db0cc6-0e44-4870-91d9-394311117c2b" />
                    
                    
                    <ns16:callActivity calledElement="1.2d69e423-2547-4495-845d-7bccbf743136" name="Audit" id="a12f1141-371e-4a5a-8153-c2a9a11a617e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1075" y="121" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript>log.info(" ServiceName : Audit: START");</ns3:preAssignmentScript>
                            
                            
                            <ns3:postAssignmentScript>log.info(" ServiceName : Audit: END");</ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>fc1cefa7-d336-49e2-bf33-2629b59f0714</ns16:incoming>
                        
                        
                        <ns16:incoming>e05da848-30c5-4f7a-b8f1-549cb9769638</ns16:incoming>
                        
                        
                        <ns16:outgoing>f55c3bcd-efb0-42f3-acee-43dc5ba0ce80</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.0dbed104-185b-44f0-9303-0765cbdb781e</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.InstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8129799a-130c-4004-beb4-d73c68f78c65</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">""</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8fa8039e-dbe6-4ecf-8897-769135f4dca7</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">""</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.e91e9ebe-6e70-4b06-996b-54441169b3ca</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.teamName</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.f7296d68-138b-4e58-b910-ce7df924d7f8</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">""</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.df186765-bf04-4089-99d9-3f84a4866be2</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.TEAM_MANAGER</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.11effbb1-5ad3-4f99-a23c-01eda7584e00</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"1"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.72a822b9-58cf-4b48-9c2d-db5353162497</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">1</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.d1976350-35cc-4bb7-be72-0d2c2c4e976b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.stepName</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="a12f1141-371e-4a5a-8153-c2a9a11a617e" targetRef="6dee4bc5-23e3-4d72-bc44-067c03344aa7" name="To End" id="f55c3bcd-efb0-42f3-acee-43dc5ba0ce80">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-27fb</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="d7494652-dd04-49eb-a64c-76d3bf06385b" targetRef="a12f1141-371e-4a5a-8153-c2a9a11a617e" name="To Audit" id="fc1cefa7-d336-49e2-bf33-2629b59f0714">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="7108b2dc-3135-444c-9b6d-e9066702fdca" targetRef="3521bde9-bf66-48d2-b599-7b9c773e2e2f" name="To Is LRGEC" id="bddb98e9-0204-4867-be7c-71beb4f59f06">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:endStateId>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-2d2e</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="a12f1141-371e-4a5a-8153-c2a9a11a617e" parallelMultiple="false" name="Error" id="1e54a6ee-38ae-48f0-a614-cafc941a68ec">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1110" y="179" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>f27d1dfc-3336-4459-90de-3cbddff5beca</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="4b57b95f-a403-4f85-a408-bec4b81157a0" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="b5ce7ee0-3160-409c-bfc4-94d5322ce328" eventImplId="aed9b578-3afc-473e-87ec-b6c7ea888c4f">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="d83c7880-9130-4942-b143-d160a6c41027" parallelMultiple="false" name="Error1" id="bb2a0dea-d40e-42d2-aa6a-9eed6ee4ac69">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="791" y="169" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>1936be34-84e6-42c1-a970-979962d7d06d</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="dca2eaea-f48f-429d-bec6-b40aee640a60" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="32b076b4-b16d-4e6e-988a-01bb1c5c345a" eventImplId="99ee835e-0870-4923-8437-df4b18c0ae49">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="7108b2dc-3135-444c-9b6d-e9066702fdca" parallelMultiple="false" name="Error2" id="777e6d48-4c54-45a2-aa6c-bcaf051cae4f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="285" y="185" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>737797f9-b44f-44b6-8d94-ed6399fdc241</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="625841b0-c765-4cbf-b526-4c4ead0f7fa1" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="7b2d42cc-99ea-44ce-81d9-cbddecf32219" eventImplId="dc5a7162-393c-4d06-8bf1-5add25b0bf2e">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="log Error" id="a4f9bb4d-7857-491b-ae5d-b14ad542e004">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="573" y="239" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>737797f9-b44f-44b6-8d94-ed6399fdc241</ns16:incoming>
                        
                        
                        <ns16:incoming>1936be34-84e6-42c1-a970-979962d7d06d</ns16:incoming>
                        
                        
                        <ns16:incoming>f27d1dfc-3336-4459-90de-3cbddff5beca</ns16:incoming>
                        
                        
                        <ns16:outgoing>fdcfb9dc-a48f-4d57-8499-d4b0b05b19f8</ns16:outgoing>
                        
                        
                        <ns16:script>log.info("*============ NBE letter Of Credit =============*");&#xD;
log.info("*========================================*");&#xD;
log.info("[Filter CA Team -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
tw.local.errorMSG=String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Filter CA Team -&gt; Log Error ]- END");&#xD;
log.info("*================================================*");</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="777e6d48-4c54-45a2-aa6c-bcaf051cae4f" targetRef="a4f9bb4d-7857-491b-ae5d-b14ad542e004" name="To log Error" id="737797f9-b44f-44b6-8d94-ed6399fdc241">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="bb2a0dea-d40e-42d2-aa6a-9eed6ee4ac69" targetRef="a4f9bb4d-7857-491b-ae5d-b14ad542e004" name="To log Error" id="1936be34-84e6-42c1-a970-979962d7d06d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="1e54a6ee-38ae-48f0-a614-cafc941a68ec" targetRef="a4f9bb4d-7857-491b-ae5d-b14ad542e004" name="To log Error" id="f27d1dfc-3336-4459-90de-3cbddff5beca">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:endEvent name="End Event" id="6e95846b-e400-4cb9-91d3-c08fe5150494">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="792" y="262" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>fdcfb9dc-a48f-4d57-8499-d4b0b05b19f8</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:errorEventDefinition id="b1082191-5b39-4ba9-877b-56a5416a7387" eventImplId="ea4b52a4-c781-4140-8336-8f1d5a4b5e7d">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="a4f9bb4d-7857-491b-ae5d-b14ad542e004" targetRef="6e95846b-e400-4cb9-91d3-c08fe5150494" name="To End Event" id="fdcfb9dc-a48f-4d57-8499-d4b0b05b19f8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.a4f2ae9d-fa53-4179-aa99-a0f7b8cea825" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Get Center Code">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.29f099b4-5fcd-4f3d-aef8-64efc389a0ca</processLinkId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.88618851-656d-4ce4-82d9-99ada9bc849e</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.d83c7880-9130-4942-b143-d160a6c41027</toProcessItemId>
            <guid>ac6c1584-de9a-4c14-a3e9-906de9fa7437</guid>
            <versionId>05ef518a-48f5-4e67-98bd-8183afa08fb1</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.88618851-656d-4ce4-82d9-99ada9bc849e</fromProcessItemId>
            <toProcessItemId>2025.d83c7880-9130-4942-b143-d160a6c41027</toProcessItemId>
        </link>
        <link name="To Audit">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.fc1cefa7-d336-49e2-bf33-2629b59f0714</processLinkId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.d7494652-dd04-49eb-a64c-76d3bf06385b</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.a12f1141-371e-4a5a-8153-c2a9a11a617e</toProcessItemId>
            <guid>d5a32d2b-58d1-465c-942d-555ba1c52910</guid>
            <versionId>2d6b66f7-f897-4dfa-93d6-5c523b07f73f</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.d7494652-dd04-49eb-a64c-76d3bf06385b</fromProcessItemId>
            <toProcessItemId>2025.a12f1141-371e-4a5a-8153-c2a9a11a617e</toProcessItemId>
        </link>
        <link name="To Audit">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.e05da848-30c5-4f7a-b8f1-549cb9769638</processLinkId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.7777e18e-01c2-41a0-b497-7cbd0b4db94b</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.a12f1141-371e-4a5a-8153-c2a9a11a617e</toProcessItemId>
            <guid>e73a4133-50c1-4f00-828c-105340c863a4</guid>
            <versionId>3003f69e-0205-4b41-a3e9-b1f82dd94155</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.7777e18e-01c2-41a0-b497-7cbd0b4db94b</fromProcessItemId>
            <toProcessItemId>2025.a12f1141-371e-4a5a-8153-c2a9a11a617e</toProcessItemId>
        </link>
        <link name="To Update Teams Name">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.a0d53811-6a0f-4ab5-a47d-2c29ac0d21e0</processLinkId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.3521bde9-bf66-48d2-b599-7b9c773e2e2f</fromProcessItemId>
            <endStateId>guid:9df5bce005da774d:-5bc6ffcb:18a1f9979de:3947</endStateId>
            <toProcessItemId>2025.d7494652-dd04-49eb-a64c-76d3bf06385b</toProcessItemId>
            <guid>5453b8a6-83fa-4eff-96c5-3114d73ffe19</guid>
            <versionId>47315d6d-5e0f-4369-ba63-ae78cc5b1c89</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="topCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.3521bde9-bf66-48d2-b599-7b9c773e2e2f</fromProcessItemId>
            <toProcessItemId>2025.d7494652-dd04-49eb-a64c-76d3bf06385b</toProcessItemId>
        </link>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.fdcfb9dc-a48f-4d57-8499-d4b0b05b19f8</processLinkId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.a4f9bb4d-7857-491b-ae5d-b14ad542e004</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.6e95846b-e400-4cb9-91d3-c08fe5150494</toProcessItemId>
            <guid>c6d46720-8023-458c-9bd1-2feefdc179b8</guid>
            <versionId>4b55c991-f5df-4a99-83e2-0de07147e136</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.a4f9bb4d-7857-491b-ae5d-b14ad542e004</fromProcessItemId>
            <toProcessItemId>2025.6e95846b-e400-4cb9-91d3-c08fe5150494</toProcessItemId>
        </link>
        <link name="To Get Team From LDAP">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.91e37f5e-76d2-4721-9d74-2f8a83db859f</processLinkId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.d83c7880-9130-4942-b143-d160a6c41027</fromProcessItemId>
            <endStateId>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-2b0b</endStateId>
            <toProcessItemId>2025.7777e18e-01c2-41a0-b497-7cbd0b4db94b</toProcessItemId>
            <guid>0b26b2b0-152c-438a-a3b3-8bcd30fd22d6</guid>
            <versionId>66f253bd-abcc-4d1a-9602-041754978bf6</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.d83c7880-9130-4942-b143-d160a6c41027</fromProcessItemId>
            <toProcessItemId>2025.7777e18e-01c2-41a0-b497-7cbd0b4db94b</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.f55c3bcd-efb0-42f3-acee-43dc5ba0ce80</processLinkId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.a12f1141-371e-4a5a-8153-c2a9a11a617e</fromProcessItemId>
            <endStateId>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-27fb</endStateId>
            <toProcessItemId>2025.6dee4bc5-23e3-4d72-bc44-067c03344aa7</toProcessItemId>
            <guid>59e4d0e7-d45b-434c-af3d-0cd22cd85053</guid>
            <versionId>6bd00455-90a0-4093-bfca-14a19ed9299d</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.a12f1141-371e-4a5a-8153-c2a9a11a617e</fromProcessItemId>
            <toProcessItemId>2025.6dee4bc5-23e3-4d72-bc44-067c03344aa7</toProcessItemId>
        </link>
        <link name="To Is LRGEC">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.bddb98e9-0204-4867-be7c-71beb4f59f06</processLinkId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.7108b2dc-3135-444c-9b6d-e9066702fdca</fromProcessItemId>
            <endStateId>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:-2d2e</endStateId>
            <toProcessItemId>2025.3521bde9-bf66-48d2-b599-7b9c773e2e2f</toProcessItemId>
            <guid>09dfb361-4bf0-4f90-a44a-9e5c352878d2</guid>
            <versionId>b3e74160-817d-40fb-ab78-5cb8e187b7bd</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.7108b2dc-3135-444c-9b6d-e9066702fdca</fromProcessItemId>
            <toProcessItemId>2025.3521bde9-bf66-48d2-b599-7b9c773e2e2f</toProcessItemId>
        </link>
        <link name="To Multi Facility">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.96379411-1c4a-4f75-bdde-c7fa3bdc4396</processLinkId>
            <processId>1.b066b0aa-3b41-4663-a2f1-ca880f07a183</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.3521bde9-bf66-48d2-b599-7b9c773e2e2f</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.88618851-656d-4ce4-82d9-99ada9bc849e</toProcessItemId>
            <guid>d7f1f4e6-3b11-4018-a767-298cd156fa0e</guid>
            <versionId>cb805f1e-385a-427c-af73-34152583ee9b</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.3521bde9-bf66-48d2-b599-7b9c773e2e2f</fromProcessItemId>
            <toProcessItemId>2025.88618851-656d-4ce4-82d9-99ada9bc849e</toProcessItemId>
        </link>
    </process>
</teamworks>

