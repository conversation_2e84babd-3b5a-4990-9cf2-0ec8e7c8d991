<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.fcc27b9d-793f-4d79-b8ed-d17e5877201f" name="Review IDC Request by Branch Compliance Rep">
        <lastModified>1692708718705</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.4fe8f3b9-6936-420f-881f-57ff725e8573</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>false</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>854e00a4-3444-43c0-8564-dfd32aff54b1</guid>
        <versionId>5ebd982d-c295-488a-bbb1-ee9c2b94f322</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.45687d69-7665-4a7a-a3ed-07d1f92982cd"],"isInterrupting":true,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":-60,"y":251,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"03ba0e2b-9e61-463b-afc9-8cc318a54ba4"},{"incoming":["2027.a1a7af68-8cdd-42c3-a6bd-5301f76db972","2027.e5384557-e5a5-4ff6-85fc-b86ec47c9360"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":1101,"y":234,"declaredType":"TNodeVisualInfo","height":44}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"End","declaredType":"endEvent","id":"9992114f-993b-48aa-bd87-b2ac7f3df53e"},{"targetRef":"2025.0ab2e4bb-cb68-4add-8b23-af437a1ba0ee","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"Start To Coach","declaredType":"sequenceFlow","id":"2027.45687d69-7665-4a7a-a3ed-07d1f92982cd","sourceRef":"03ba0e2b-9e61-463b-afc9-8cc318a54ba4"},{"startQuantity":1,"outgoing":["2027.2c590891-71b6-458e-9ee3-f3a3ce0beae8"],"incoming":["2027.0f916482-3ee6-481c-80b4-49eca9d330ec"],"default":"2027.2c590891-71b6-458e-9ee3-f3a3ce0beae8","extensionElements":{"nodeVisualInfo":[{"width":95,"x":671,"y":99,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Status ","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.a40f655e-1d12-4ad7-a4fd-66269eb05204","scriptFormat":"text\/x-javascript","script":{"content":["if (tw.local.selectedAction == tw.epv.Action.returnToInitiator) {\r\n\ttw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.returnedtoInitiator;\r\n\ttw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.initiated;\r\n\t\r\n}else if (tw.local.selectedAction == tw.epv.Action.cancelReques) {\r\n\ttw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.canceled;\r\n\ttw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.canceled;\r\n\tif (tw.local.idcRequest.IDCRequestNature.englishdescription == \"New Request\") {\r\n\t\ttw.local.idcRequest.IDCRequestState = tw.epv.IDCState.canceled;\r\n\t}\r\n} else {\r\n\ttw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingTradeFOReview;\r\n\ttw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inApproval;\t\t\r\n}\r\n\r\ntw.local.idcRequest.stepLog.action = tw.local.selectedAction;"]}},{"targetRef":"2025.eaa2d5e4-652f-4c51-82fd-df5a42daaa0a","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.2c590891-71b6-458e-9ee3-f3a3ce0beae8","sourceRef":"2025.a40f655e-1d12-4ad7-a4fd-66269eb05204"},{"startQuantity":1,"outgoing":["2027.fde9282d-2251-4e30-8d4c-75f282e74ebb"],"incoming":["2027.45687d69-7665-4a7a-a3ed-07d1f92982cd"],"default":"2027.fde9282d-2251-4e30-8d4c-75f282e74ebb","extensionElements":{"nodeVisualInfo":[{"width":95,"x":46,"y":228,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Initialization Script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.0ab2e4bb-cb68-4add-8b23-af437a1ba0ee","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.idcRequest.stepLog = {};\r\ntw.local.idcRequest.stepLog.startTime = new Date();\r\ntw.local.errorVIS = \"NONE\";\r\n\r\ntw.local.action = [];\r\nif (tw.local.idcRequest.appInfo.subStatus == tw.epv.IDCsubStatus.pendingBranchComplianceCancelationConfirmation) {\r\n\ttw.local.action[0] = tw.epv.Action.cancelReques;\r\n}else{\r\n\ttw.local.action[0] = tw.epv.Action.submitRequest;\r\n}\r\ntw.local.action[1] = tw.epv.Action.returnToInitiator;\r\n"]}},{"targetRef":"2025.ccdf90b5-9bd3-40bc-844d-6bd43a4df994","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Review IDC Request by Branch Compliance Rep ","declaredType":"sequenceFlow","id":"2027.fde9282d-2251-4e30-8d4c-75f282e74ebb","sourceRef":"2025.0ab2e4bb-cb68-4add-8b23-af437a1ba0ee"},{"outgoing":["2027.dea43813-d9dc-4fe0-89aa-51adf2f603b3","2027.ad9dccf1-bf74-44df-9568-0a53b84bfc77"],"incoming":["2027.d7f96f9d-db0f-4642-a665-1e97407e6f9d","2027.9e2a2dcf-bd0f-4b0f-8abb-bcc12b04c143","2027.aadd02f6-916e-4869-8bd1-757d05fd43c9","2027.daef80d9-0e92-4f47-8070-56a8b9e54c6e"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":386,"y":228,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"],"preAssignmentScript":[]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"layoutItemId":"Error_Message1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a133aa0d-1812-4316-8f4d-f3acf6ea5103","optionName":"@label","value":"Error Message"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"80c42832-1efb-4ad1-8727-32d25a557967","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ae661e06-8d94-4cf3-8568-dbcdc6aa7d1c","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a627dc11-a925-469d-833d-6d3847adc0fb","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a822f17f-e970-4757-8693-5faf24153eb7","optionName":"@visibility","value":"tw.local.errorVIS"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"057f6a85-8826-4309-807d-d85a0b2dd646","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.46690ca1-5521-4e5c-bc9e-ecb22d75ca97","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"a6c35b4a-ecb5-4fd3-85d7-7052324ea154","version":"8550"},{"contentBoxContrib":[{"contributions":[{"contentBoxContrib":[{"contributions":[{"layoutItemId":"Customer_Information1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"bc792304-d2f7-4a1b-8819-800d67c0a1ba","optionName":"@label","value":"Customer Information"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"272eca51-edaa-4d5a-8589-d99e081773c0","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"60174edb-c557-415a-83be-0d0dee94dc10","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"bfeb143b-a875-4d5f-8e16-************","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0fb1f1e4-ab1f-4ed9-82d5-292cb4306c5b","optionName":"instanceview","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"903a7bd2-148e-4a5b-8400-722153281ba7","optionName":"alertMessage","value":"tw.local.alertMessage"}],"viewUUID":"64.656cc232-8247-43c3-9481-3cc7a9aec2e6","binding":"tw.local.idcRequest.customerInformation","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"4cca9ca2-4756-4e27-8d5a-d6ad632300f2","version":"8550"},{"layoutItemId":"Basic_Details1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c79d5bf6-51a4-4d8c-896a-2c1e973508bf","optionName":"@label","value":"Basic Details"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"31a0d47f-aaca-4e18-82f9-d253c75d11be","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"16bedcc6-4d66-4939-88ec-aa46b7982b04","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2741b562-dc46-46e7-8ecf-482b9d37ed5f","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b2838c50-0a60-463d-8637-d0dd274885f6","optionName":"hasWithdraw","value":"false"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"da1bda0b-9751-45b1-8012-cd1a4f21e030","optionName":"havePaymentTerm","value":"no"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"851c0211-8ff1-45da-87a0-3fbad5c2411b","optionName":"addBill","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5e4b34b8-ca4f-4683-8a8f-73fd10aa5760","optionName":"deleteBill","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6c42882c-e14d-4b3b-84c3-7b7318e8719a","optionName":"addInvoice","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f509f9b2-8a9e-46ce-838d-3058ded8b60f","optionName":"deleteInvoice","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4a10d44e-5037-49a2-8db0-c24ff1a3ed38","optionName":"billExist","value":"0"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b579e6f2-60e1-4848-8332-7ead9531e8ed","optionName":"invoiceExist","value":"0"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"04710077-7e09-480f-8e0d-4fc8cd04e45a","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5bac026b-a7c1-4f98-8b5f-e52e554b7933","optionName":"errorVis","value":"tw.local.errorVIS"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6a4ce03e-e7f6-4ac0-8845-c2eb645cd8f7","optionName":"idcContract","value":"tw.local.idcContract"}],"viewUUID":"64.3d13e9b9-bcc9-4fe9-ac80-3c287115f705","binding":"tw.local.idcRequest","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"0e5d44f6-4c02-4700-8cd5-785d5077b0e1","version":"8550"},{"layoutItemId":"Financial_Details__Branch1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ecfe65fc-6487-418a-8364-72369e95f4d2","optionName":"@label","value":"Financial Details  Branch"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"446d1e93-3dfc-4dce-8706-5edd59d9fce6","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9f4661a3-**************-0237b166386b","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ca451e9d-75da-4cf8-8486-b4f6135eb144","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b345bc78-abe7-4993-8bf0-8afae00e1624","optionName":"requestType","value":"tw.local.idcRequest.IDCRequestType.englishdescription"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3756a0c2-1692-440a-8b0e-3f15b5a24210","optionName":"docAmount","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"bf50c943-5596-4abe-8b85-ad597ed3de93","optionName":"currncy","value":"false"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"24a57d72-c77e-4c0b-8daf-455cb46ed47d","optionName":"CIF","value":"tw.local.idcRequest.customerInformation.CIFNumber"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"77f60bc1-e240-4365-83c6-c6ec33fd5264","optionName":"accountsList","value":"tw.local.accountList[]"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7483afc5-27d9-452d-8632-7dea8eb0701e","optionName":"haveAmountAdvanced","value":"DEFAULT"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"862c000a-6be0-4a73-8eb8-dbc666d3f807","optionName":"tmpUsedAdvancePayment","value":"tw.local.tmpusedAdvancePayment"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9988f96e-3edf-4d51-8f3e-25703a061c37","optionName":"isChecker","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"********-47ef-438a-801c-bd5f6e8097f2","optionName":"currencyVis","value":"tw.local.currencyVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"85ef1b10-3d81-40f0-83ae-613732420c58","optionName":"requestID","value":"tw.local.idcRequest.appInfo.instanceID"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"23778c44-923c-4fa6-81b1-bf8196bdb0ea","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4b9d4e82-670c-4057-87aa-0056ba426bb5","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.74d3cb97-ad59-4249-847b-a21122e44b22","binding":"tw.local.idcRequest.financialDetails","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"39af9c0e-386c-4315-8fd8-e4bc4d0a1e55","version":"8550"},{"layoutItemId":"Attachment1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ecb89bda-648f-4aa1-845f-91f831206c43","optionName":"@label","value":"Attachment"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e0c0eb4b-0841-49b3-822c-db15f52fb53e","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6327e9e8-c8ba-46bd-837f-dd11a332b291","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"967c952a-89dd-4b98-8d6b-a4853f9c11d6","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"DEFAULT\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"217fdc74-15c9-48e3-8434-7b214903dbf1","optionName":"canUpdate","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d2d87e7f-dad9-4a82-80aa-6591d6fe3f99","optionName":"canCreate","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1d352a36-f0c8-4565-85aa-1c90a61f3fe6","optionName":"canDelete","value":"false"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d222eda3-ba5c-4d9c-8812-38c0af121c3a","optionName":"ECMproperties","value":"tw.local.ECMproperties"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"13f43578-7345-448c-89e1-9392eec4a868","optionName":"visiable","value":"false"}],"viewUUID":"64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7","binding":"tw.local.attachment[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"ea73ae17-df18-496e-8913-a3d8d88749bd","version":"8550"},{"layoutItemId":"App_History_View_21","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"50f0b2d8-1b80-4089-88b2-05276a2edb52","optionName":"@label","value":"History"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7432398d-1cfc-4805-8114-b3b32b72030d","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"91494416-45be-4dff-8cd2-a690ef826e6d","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"74277b46-38ab-4722-868a-4c34adfbd787","optionName":"historyVisFlag","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1a0ab0c2-6f46-4601-88ab-187bff923bb0","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}"}],"viewUUID":"64.6ac8a533-b05a-45a1-9e46-1c4542e6b8be","binding":"tw.local.idcRequest.appLog[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"6b6ce5b6-74e8-4517-834c-d4590fc29090","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"3999f3e9-4ab5-413b-8377-69c07ce4b14a"}],"layoutItemId":"Tab_Section1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"44d0e9f8-0e8c-4944-8b80-666399043dd8","optionName":"@label","value":"Tab section"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"65bc910e-1233-47dd-8f2e-6d71b3b788dd","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c30fd57f-2eb5-4ebc-82ea-627e8d99bce3","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d1a93586-bd36-4a26-8d02-69e3f810f7b0","optionName":"sizeStyle","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"X\"}]}"}],"viewUUID":"64.c05b439f-a4bd-48b0-8644-fa3b59052217","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"2a9d45c0-81ab-45c3-80a9-59f7e80d7e72","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"4c632626-4292-4d06-8de0-4dc6dabf88eb"}],"layoutItemId":"DC_Templete1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e2fa476b-6ad0-4ae2-822d-f814e07da011","optionName":"@label","value":"DC Templete"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d8d7cf75-c0ba-4bf0-8eb1-298d26c5b271","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"dc611c48-5458-435d-8a93-b9e551bd28fe","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fd84e6c0-9a47-40b0-84da-b566b47939f5","optionName":"stepLog","value":"tw.local.idcRequest.stepLog"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e68ef455-9674-4f8c-89dd-7f720b0b6aa9","optionName":"action","value":"tw.local.action[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"565f7b17-dde6-4a34-82bc-d512012fe3bc","optionName":"selectedAction","value":"tw.local.selectedAction"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b00241d9-e621-4f3b-8b69-76fa04efd393","optionName":"hasReturnReason","value":"tw.local.hasReturnReason"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d22ebdfb-c39c-4e2a-8a07-a8b737695f7d","optionName":"buttonName","value":"Submit"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0ec4e78d-e7e5-452a-87c7-5abd73463846","optionName":"hasApprovals","value":"tw.local.hasAppovals"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"05825f62-25d9-497f-89ae-3f73677357b6","optionName":"approvals","value":"tw.local.idcRequest.approvals"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fb1e29c3-d9e6-4e4c-8eb8-ef78b65877e2","optionName":"invalidTabs","value":"tw.local.invalidTabs[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"73b1dcf8-19ab-443f-8d55-3e8ea81ea739","optionName":"validationMessage","value":"tw.local.validationMessage"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f82acac6-0a14-404d-8668-9748e05ca778","optionName":"isCAD","value":"false"}],"viewUUID":"64.f9e6899d-e7d7-4296-ba71-268fcd57e296","binding":"tw.local.idcRequest.appInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"8e47622d-c2b8-4507-8e39-6bc5f933f277","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"Review IDC Request by Branch Compliance Rep ","isForCompensation":false,"completionQuantity":1,"id":"2025.674068e6-9658-41eb-b7c7-f9ebe6ace86e"},{"outgoing":["2027.d7f96f9d-db0f-4642-a665-1e97407e6f9d"],"incoming":["2027.dea43813-d9dc-4fe0-89aa-51adf2f603b3"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition"}],"extensionElements":{"default":["2027.d7f96f9d-db0f-4642-a665-1e97407e6f9d"],"nodeVisualInfo":[{"width":24,"x":395,"y":387,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page","declaredType":"intermediateThrowEvent","id":"2025.df8c1d03-6eee-4dd9-8be1-f6e44fb77597"},{"targetRef":"2025.df8c1d03-6eee-4dd9-8be1-f6e44fb77597","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"50eae49c-c415-458c-a0c2-2ac4ec8e8bdd","coachEventPath":"DC_Templete1\/saveState"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomLeft","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To Stay on page","declaredType":"sequenceFlow","id":"2027.dea43813-d9dc-4fe0-89aa-51adf2f603b3","sourceRef":"2025.674068e6-9658-41eb-b7c7-f9ebe6ace86e"},{"targetRef":"2025.b925df26-c251-4d42-9f18-790ee6646ce7","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"891654b5-4515-4b04-8856-41896e98237d","coachEventPath":"DC_Templete1\/submit"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.ad9dccf1-bf74-44df-9568-0a53b84bfc77","sourceRef":"2025.674068e6-9658-41eb-b7c7-f9ebe6ace86e"},{"targetRef":"2025.674068e6-9658-41eb-b7c7-f9ebe6ace86e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Review IDC Request by Branch Compliance Rep ","declaredType":"sequenceFlow","id":"2027.d7f96f9d-db0f-4642-a665-1e97407e6f9d","sourceRef":"2025.df8c1d03-6eee-4dd9-8be1-f6e44fb77597"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"action","isCollection":true,"declaredType":"dataObject","id":"2056.95442752-f74d-4d5f-9be4-63b0eedbd654"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"selectedAction","isCollection":false,"declaredType":"dataObject","id":"2056.e07008b5-84f5-4a66-bdce-9147ef829a85"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"false"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"hasAppovals","isCollection":false,"declaredType":"dataObject","id":"2056.9bd329d1-e5e7-4c4c-b7f1-2d787c41a1ab"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"true"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"hasReturnReason","isCollection":false,"declaredType":"dataObject","id":"2056.5086d9f2-ed1b-4c87-9853-babb6cbfd54d"},{"startQuantity":1,"outgoing":["2027.5be73306-3480-4550-b624-135fb150539b"],"incoming":["2027.ad9dccf1-bf74-44df-9568-0a53b84bfc77"],"default":"2027.5be73306-3480-4550-b624-135fb150539b","extensionElements":{"nodeVisualInfo":[{"color":"#95D087","width":95,"x":386,"y":99,"declaredType":"TNodeVisualInfo","height":70}]},"name":"validation","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.b925df26-c251-4d42-9f18-790ee6646ce7","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.message = \"\";\r\nvar mandatoryTriggered = false;\r\nvar tempLength = 0 ;\r\n\/\/tw.local.invalidTabs = [];\r\n\/*\r\n* =========================================================================================================\r\n*  \r\n* Add a coach validation error \r\n* \t\t\r\n* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\n*\r\n* =========================================================================================================\r\n*\/\r\n\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\n{\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\n\tfromMandatory &amp;&amp; mandatoryTriggered ? \"\" : tw.local.message += \"&lt;li dir='rtl'&gt;\" + validationMessage + \"&lt;\/li&gt;\";\r\n}\r\n\r\n\/*\r\n* ==============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is in the past and the last variable to exclude today\r\n*\t\r\n* EX:\tnotPastDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)\r\n*\r\n* =============================================================================================================================\r\n*\/\r\n\r\nfunction notPastDate(date , fieldName , controlMessage , validationMessage , exclude)\r\n{\r\n\tif (exclude)\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &lt;= new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\t\r\n\t}\r\n\telse\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &lt; new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\r\n\t}\r\n}\r\n\r\n\/*\r\n* ==============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is between two dates\r\n*\t\r\n* EX:\tdateBetween(tw.local.date , date1 , date2 , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!')\r\n*\r\n* =============================================================================================================================\r\n*\/\r\n\r\nfunction dateBetween(field , date1 , date2 , fieldName , controlMessage , validationMessage)\r\n{\r\n\tif(field &lt; date1 &amp;&amp; field &gt; date2)\r\n\t{\r\n\t \treturn true;\r\n\t}\r\n\taddError(fieldName , controlMessage , validationMessage);\r\n\treturn false;\r\n}\r\n\r\n\/*\r\n* ===============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is in the future and the last varaible to exculde today\r\n*\t\r\n* EX:\tnotFutureDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)\r\n*\r\n* ===============================================================================================================================\r\n*\/\r\n\r\nfunction notFutureDate(date , fieldName ,  controlMessage , validationMessage , exculde)\r\n{\r\n\tif (exculde)\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &gt;= new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\t\r\n\t}\r\n\telse\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &gt; new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\r\n\t}\r\n}\r\n\r\n\r\n\/*\r\n* =================================================================================================================================\r\n*\r\n* Add a coach validation error if the string length is less than given length\r\n*\t\r\n* EX:\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\r\n*\r\n* =================================================================================================================================\r\n*\/\r\n\r\nfunction minLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the string length is greater than given length\r\n*\t\r\n* EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field != null &amp;&amp; field != undefined &amp;&amp; field.length &gt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the number is greater than given value\r\n*\t\r\n* EX:\tmaxNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction maxNumber(field , fieldName , max , controlMessage , validationMessage)\r\n{\r\n\tif (field &gt; max)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the number is less than given value\r\n*\t\r\n* EX:\tminNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction minNumber(field , fieldName , min , controlMessage , validationMessage)\r\n{\r\n\tif (field &lt; min)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a coach validation error if the field is null 'Mandatory'\r\n*\t\r\n* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\r\nfunction mandatory(field , fieldName)\r\n{\r\n\tif (field == null || field == undefined )\r\n\t{\r\n\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\tmandatoryTriggered = true;\r\n\t\treturn false;\r\n\t}\r\n\telse\r\n\t{\r\n\t\tswitch (typeof field)\r\n\t\t{\r\n\t\t\tcase \"string\":\r\n\t\t\t\tif (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"number\":\r\n\t\t\t\tif (field == 0.0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a validation mark on the tab if there is an error\r\n*\t\r\n* EX:\tvalidateTab(1)\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\r\nfunction validateTab(index)\r\n{\r\n\tif (tw.system.coachValidation.validationErrors.length &gt; tempLength)\r\n\t{\r\n\t\ttempLength = tw.system.coachValidation.validationErrors.length ;\r\n\t\ttw.local.invalidTabs[tw.local.invalidTabs.length] = index;\r\n\t}\t\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a validation mark on the tab if there is an error\r\n*\t\r\n* EX:\tvalidateTab(1)\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\/\/function validateDecimal(field, fieldName, controlMessage , validationMessage) {\r\n\/\/   regexString = `^\\\\d{1,12}(\\\\.\\\\d{1,12})?$`;\r\n\/\/   regex = new RegExp(regexString);\r\n\/\/\r\n\/\/  if (!regex.test(field))\r\n\/\/\t{\r\n\/\/\t\taddError(fieldName , controlMessage , validationMessage);\r\n\/\/\t\treturn false;\r\n\/\/\t}\r\n\/\/\treturn true;\r\n\/\/}\r\n\/\/-----------------------------------------financial Details---------------------------------------------------------\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.documentAmount,\"tw.local.idcRequest.financialDetails.documentAmount\");\r\n\/\/validateDecimal(tw.local.idcRequest.financialDetails.documentAmount, \"tw.local.idcRequest.financialDetails.documentAmount\", \"max length is 14\" , \"max length is 14\");\r\n\/\/minNumber(tw.local.idcRequest.financialDetails.documentAmount , \"tw.local.idcRequest.financialDetails.documentAmount\" , 0.01 , \"must be more than 0\" , \"must be more than 0\");\r\n\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.chargesAccount,\"tw.local.idcRequest.financialDetails.chargesAccount\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.paymentAccount,\"tw.local.idcRequest.financialDetails.paymentAccount\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.documentCurrency.code,\"tw.local.idcRequest.financialDetails.documentCurrency.code\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.code,\"tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.code\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.sourceOfFunds.code,\"tw.local.idcRequest.financialDetails.sourceOfFunds.code\");\r\n\/\/----------------------------------basic details------------------------------------------------------------------------------\r\n\r\n\/\/mandatory(tw.local.idcRequest.importPurpose.code,\"tw.local.idcRequest.importPurpose.code\");\r\n\/\/mandatory(tw.local.idcRequest.paymentTerms.code,\"tw.local.idcRequest.paymentTerms.code\");\r\n\/\/mandatory(tw.local.idcRequest.documentsSource.code,\"tw.local.idcRequest.documentsSource.code\");\r\n\/\/mandatory(tw.local.idcRequest.productCategory.code,\"tw.local.idcRequest.productCategory.code\");\r\n\/\/mandatory(tw.local.idcRequest.commodityDescription,\"tw.local.idcRequest.commodityDescription\");\r\n\/\/if (tw.local.idcRequest.invoices.length&gt;0){\r\n\/\/\tfor (var i=0; i&lt;tw.local.idcRequest.invoices.length; i++) {\r\n\/\/\t\tmandatory(tw.local.idcRequest.invoices[i].number,\"tw.local.idcRequest.invoices[\"+i+\"].number\");\r\n\/\/\t\tmandatory(tw.local.idcRequest.invoices[i].date,\"tw.local.idcRequest.invoices[\"+i+\"].date\");\r\n\/\/\t}\r\n\/\/}\r\n\/\/if(tw.local.idcRequest.billOfLading.length&gt;0){\r\n\/\/\tfor (var i=0; i&lt;tw.local.idcRequest.billOfLading.length; i++) {\r\n\/\/\t\tmandatory(tw.local.idcRequest.billOfLading[i].number,\"tw.local.idcRequest.billOfLading[\"+i+\"].number\");\r\n\/\/\t\tmandatory(tw.local.idcRequest.billOfLading[i].date,\"tw.local.idcRequest.billOfLading[\"+i+\"].date\");\r\n\/\/\t}\r\n\/\/}\r\n\/\/mandatory(tw.local.idcRequest.countryOfOrigin.code,\"tw.local.idcRequest.countryOfOrigin.code\");\r\n\/\/----------------------------------------app info------------------------------------------------------------------------------------\r\n\/\/mandatory(tw.local.selectedAction,\"tw.local.selectedAction\");\r\n\/\/------------------------------------financial Details fo -------------------------------------------------------------------\t\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.firstInstallementMaturityDate,\"tw.local.idcRequest.financialDetails.firstInstallementMaturityDate\" );\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.name,\"tw.local.idcRequest.financialDetails.beneficiaryDetails.name\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.bank,\"tw.local.idcRequest.financialDetails.beneficiaryDetails.bank\" );\t\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.account,\"tw.local.idcRequest.financialDetails.beneficiaryDetails.account\" );\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.country.code,\"tw.local.idcRequest.financialDetails.beneficiaryDetails.country.code\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.tradeFinanceApprovalNumber,\"tw.local.idcRequest.financialDetails.tradeFinanceApprovalNumber\" );\r\n\/\/if (tw.local.selectedAction == tw.epv.Action.submitRequest) {\r\n\/\/\tmandatory(tw.local.idcRequest.financialDetails.executionHub.code,\"tw.local.idcRequest.financialDetails.executionHub.code\");\r\n\/\/}\r\n\/\/var sum = tw.local.idcRequest.financialDetails.cashAmtInDocCurrency + tw.local.idcRequest.financialDetails.CashAmtWithNoCurrency + tw.local.idcRequest.financialDetails.facilityAmtInDocCurrency + tw.local.idcRequest.financialDetails.facilityAmtWithNoCurrency;\r\n\/\/if(sum != tw.local.idcRequest.financialDetails.amtPayableByNBE){\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.cashAmtInDocCurrency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" );\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.CashAmtWithNoCurrency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" );\t\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.facilityAmtInDocCurrency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" );\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.facilityAmtWithNoCurrency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency\" );\t\r\n\/\/\r\n\/\/}\r\n\/\/\r\n\/\/var sum =  tw.local.idcRequest.financialDetails.amtSight+tw.local.idcRequest.financialDetails.amtDeferredNoAvalized +tw.local.idcRequest.financialDetails.amtDeferredAvalized;\r\n\/\/if (sum!=tw.local.idcRequest.financialDetails.amtPayableByNBE) {\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.amtSight\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\");\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.amtDeferredNoAvalized\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\");\t\r\n\/\/\taddError(\"tw.local.idcRequest.financialDetails.amtDeferredAvalized\" , \"please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\");\t\r\n\/\/}\r\n\/\/for (var i=0; i&lt;tw.local.idcRequest.financialDetails.paymentTerms.length; i++) {\r\n\/\/\t\r\n\/\/\tmandatory(tw.local.idcRequest.financialDetails.paymentTerms[i].installmentDate,\"tw.local.idcRequest.financialDetails.paymentTerms[\"+i+\"].installmentDate\");\r\n\/\/\tmandatory(tw.local.idcRequest.financialDetails.paymentTerms[i].installmentAmount,\"tw.local.idcRequest.financialDetails.paymentTerms[\"+i+\"].installmentAmount\");\r\n\/\/}\r\n\/\/-------------------------------action-----------------------------------------------------------------------\r\nif (tw.local.selectedAction == tw.epv.Action.returnToInitiator) {\r\n\tmandatory(tw.local.idcRequest.stepLog.returnReason,\"tw.local.idcRequest.stepLog.returnReason\");\r\n}\r\nif (tw.local.selectedAction == tw.epv.Action.cancelReques) {\r\n\tmandatory(tw.local.idcRequest.stepLog.comment,\"tw.local.idcRequest.stepLog.comment\");\r\n}\r\n\r\n\/\/if (tw.local.selectedAction == tw.epv.Action.terminateRequest) {\r\n\/\/\tmandatory(tw.local.idcRequest.stepLog.comment,\"tw.local.idcRequest.stepLog.comment\");\r\n\/\/}\r\n\/\/\r\n\/\/if ((tw.local.idcRequest.approvals.CAD==true|| \r\n\/\/    tw.local.idcRequest.approvals.compliance==true ||\r\n\/\/    tw.local.idcRequest.approvals.treasury==true))\r\n\/\/{   \r\n\/\/    if (tw.local.selectedAction != \"Obtain Approvals\") {\r\n\/\/       addError(\"tw.local.selectedAction\", \"Please uncheck Approvals\");\r\n\/\/    }\r\n\/\/}\r\n\/\/else if (tw.local.selectedAction == \"Obtain Approvals\")\r\n\/\/{\r\n\/\/    addError(\"tw.local.selectedAction\", \"Please check Approvals\");\r\n\/\/}\r\nmandatory(tw.local.selectedAction,\"tw.local.selectedAction\");"]}},{"outgoing":["2027.9e2a2dcf-bd0f-4b0f-8abb-bcc12b04c143","2027.0f916482-3ee6-481c-80b4-49eca9d330ec"],"incoming":["2027.5be73306-3480-4550-b624-135fb150539b"],"default":"2027.0f916482-3ee6-481c-80b4-49eca9d330ec","gatewayDirection":"Unspecified","extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":32,"x":556,"y":118,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Have Errors","declaredType":"exclusiveGateway","id":"2025.ff420b18-2db5-439f-9743-f8d1ea64ad6f"},{"targetRef":"2025.ff420b18-2db5-439f-9743-f8d1ea64ad6f","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Have Errors","declaredType":"sequenceFlow","id":"2027.5be73306-3480-4550-b624-135fb150539b","sourceRef":"2025.b925df26-c251-4d42-9f18-790ee6646ce7"},{"targetRef":"2025.674068e6-9658-41eb-b7c7-f9ebe6ace86e","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.system.coachValidation.validationErrors.length\t  &gt;\t  0"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"yes","declaredType":"sequenceFlow","id":"2027.9e2a2dcf-bd0f-4b0f-8abb-bcc12b04c143","sourceRef":"2025.ff420b18-2db5-439f-9743-f8d1ea64ad6f"},{"targetRef":"2025.a40f655e-1d12-4ad7-a4fd-66269eb05204","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"no","declaredType":"sequenceFlow","id":"2027.0f916482-3ee6-481c-80b4-49eca9d330ec","sourceRef":"2025.ff420b18-2db5-439f-9743-f8d1ea64ad6f"},{"startQuantity":1,"outgoing":["2027.aadd02f6-916e-4869-8bd1-757d05fd43c9"],"incoming":["2027.fde9282d-2251-4e30-8d4c-75f282e74ebb"],"default":"2027.aadd02f6-916e-4869-8bd1-757d05fd43c9","extensionElements":{"nodeVisualInfo":[{"width":95,"x":216,"y":228,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Step Name","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.ccdf90b5-9bd3-40bc-844d-6bd43a4df994","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.idcRequest.appInfo.stepName = tw.system.processInstance.task.subject;\r\ntw.local.idcRequest.appInfo.appID = tw.system.processInstance.id;\r\n\r\n"]}},{"targetRef":"2025.674068e6-9658-41eb-b7c7-f9ebe6ace86e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Review IDC Request by Branch Compliance Rep ","declaredType":"sequenceFlow","id":"2027.aadd02f6-916e-4869-8bd1-757d05fd43c9","sourceRef":"2025.ccdf90b5-9bd3-40bc-844d-6bd43a4df994"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"message","isCollection":false,"declaredType":"dataObject","id":"2056.17535f57-83c9-4c47-a23c-0162bc928b6f"},{"outgoing":["2027.687c7107-7d98-4641-89ee-8dcfad8e8956","2027.e5384557-e5a5-4ff6-85fc-b86ec47c9360"],"incoming":["2027.b2765b1e-4861-45ee-8280-6fd1cd13212f"],"default":"2027.e5384557-e5a5-4ff6-85fc-b86ec47c9360","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":950,"y":118,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Exclusive Gateway","declaredType":"exclusiveGateway","id":"2025.6de48f82-d506-4a36-8f83-b70bf08ccaec"},{"targetRef":"2025.e3fbc36e-7be8-42ed-a6bf-bf0ecf20fd16","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.selectedAction\t  ==\t  tw.epv.Action.cancelReques"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To cancel request","declaredType":"sequenceFlow","id":"2027.687c7107-7d98-4641-89ee-8dcfad8e8956","sourceRef":"2025.6de48f82-d506-4a36-8f83-b70bf08ccaec"},{"outgoing":["2027.a1a7af68-8cdd-42c3-a6bd-5301f76db972"],"incoming":["2027.687c7107-7d98-4641-89ee-8dcfad8e8956"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":931,"y":211,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"declaredType":"callActivity","startQuantity":1,"default":"2027.a1a7af68-8cdd-42c3-a6bd-5301f76db972","name":"cancel request","dataInputAssociation":[{"targetRef":"2055.78c6654b-50aa-4a5c-aaad-9f13f7bd698d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","declaredType":"TFormalExpression","content":["tw.local.idcRequest"]}}]},{"targetRef":"2055.e1f37cfb-9885-410e-90b8-669755b06eea","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.folderId"]}}]},{"targetRef":"2055.b0b96570-bf1a-4e4c-918a-88cabd4ab475","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentFolderPath"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.e3fbc36e-7be8-42ed-a6bf-bf0ecf20fd16","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.fa5b66e4-0cee-4527-8fe2-b592fb502b90"]}],"calledElement":"1.96dc5449-b281-4b46-8b84-bf73531c54ff"},{"targetRef":"9992114f-993b-48aa-bd87-b2ac7f3df53e","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.a1a7af68-8cdd-42c3-a6bd-5301f76db972","sourceRef":"2025.e3fbc36e-7be8-42ed-a6bf-bf0ecf20fd16"},{"targetRef":"9992114f-993b-48aa-bd87-b2ac7f3df53e","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.e5384557-e5a5-4ff6-85fc-b86ec47c9360","sourceRef":"2025.6de48f82-d506-4a36-8f83-b70bf08ccaec"},{"itemSubjectRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","name":"accountList","isCollection":true,"declaredType":"dataObject","id":"2056.4698232d-b5eb-4d81-8290-3763ce058272"},{"itemSubjectRef":"itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67","name":"tmpusedAdvancePayment","isCollection":false,"declaredType":"dataObject","id":"2056.7675b2ea-6825-40aa-8060-627cd796133f"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"invalidTabs","isCollection":true,"declaredType":"dataObject","id":"2056.646c3c20-c9e7-4cf6-825e-f1df12195933"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"validationMessage","isCollection":false,"declaredType":"dataObject","id":"2056.b28c1b25-e7bd-4cc6-87ad-f065f93d24f2"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"DEFAULT\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"currencyVis","isCollection":false,"declaredType":"dataObject","id":"2056.1606b9a8-9f69-4a1b-80f7-a3f3e8451c0f"},{"outgoing":["2027.b2765b1e-4861-45ee-8280-6fd1cd13212f"],"incoming":["2027.2c590891-71b6-458e-9ee3-f3a3ce0beae8"],"extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":95,"x":821,"y":99,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"declaredType":"callActivity","startQuantity":1,"default":"2027.b2765b1e-4861-45ee-8280-6fd1cd13212f","name":"Database Integration","dataInputAssociation":[{"targetRef":"2055.a7f76cb6-349a-4e05-84c4-a0307bd1074b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Compliance\""]}}]},{"targetRef":"2055.3fafd6ca-c323-45ea-8653-0015f902d198","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.selectedAction"]}}]},{"targetRef":"2055.79f352fc-0629-430a-87cd-5b10dbdc4454","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["true"]}}]},{"targetRef":"2055.4144ba7c-0a79-4853-8fd2-6aa9481cd0bb","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","declaredType":"TFormalExpression","content":["tw.local.idcRequest"]}}]},{"targetRef":"2055.d3b53e19-8f75-427c-8e43-fe25518ef721","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.idcRequest.appLog"]}}]},{"targetRef":"2055.96de5ca4-16df-4f86-8d6d-dc1ae75db0f9","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1","declaredType":"TFormalExpression","content":["tw.local.idcContract"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.eaa2d5e4-652f-4c51-82fd-df5a42daaa0a","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","declaredType":"TFormalExpression","content":["tw.local.idcRequest"]}}],"sourceRef":["2055.9ad81a8c-1b47-4f45-81b1-c0c0120c97d1"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.e92b92ce-41f1-49d7-82cf-acf4e366b0e0"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.idcRequest.appLog"]}}],"sourceRef":["2055.b08a2cbe-96ec-4d2d-8efe-ec915a097b44"]}],"calledElement":"1.9f0a859b-5010-4ab6-947a-81ad99803cf1"},{"targetRef":"2025.6de48f82-d506-4a36-8f83-b70bf08ccaec","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exclusive Gateway","declaredType":"sequenceFlow","id":"2027.b2765b1e-4861-45ee-8280-6fd1cd13212f","sourceRef":"2025.eaa2d5e4-652f-4c51-82fd-df5a42daaa0a"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"declaredType":"dataObject","id":"2056.361d283c-6280-494c-84b3-2b208d111256"},{"startQuantity":1,"outgoing":["2027.daef80d9-0e92-4f47-8070-56a8b9e54c6e"],"incoming":["2027.defca81c-add7-4f05-8a3b-784f0277d901","2027.582987a0-a4a3-45fd-828a-827cb45ab5a4"],"default":"2027.daef80d9-0e92-4f47-8070-56a8b9e54c6e","extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":680,"y":335,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["alert(\"pre error \"+tw.local.idcContract.facilities.length);"]},"name":"Handling Error","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.a10bd8f4-3955-4392-83d0-95775ddf568e","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.errorMSG = String(tw.error.data);\r\ntw.local.errorVIS = \"EDITABLE\";"]}},{"parallelMultiple":false,"outgoing":["2027.defca81c-add7-4f05-8a3b-784f0277d901"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2025.7179102f-6d6f-4766-85b8-1713d0831e50"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition"}],"attachedToRef":"2025.e3fbc36e-7be8-42ed-a6bf-bf0ecf20fd16","extensionElements":{"default":["2027.defca81c-add7-4f05-8a3b-784f0277d901"],"nodeVisualInfo":[{"width":24,"x":966,"y":269,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"2025.7bab5193-44f4-49ef-89aa-a7edaf67a844","outputSet":{}},{"parallelMultiple":false,"outgoing":["2027.582987a0-a4a3-45fd-828a-827cb45ab5a4"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2025.c8ebc3e2-5f15-422f-8907-b89dc0f23e8d"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition"}],"attachedToRef":"2025.eaa2d5e4-652f-4c51-82fd-df5a42daaa0a","extensionElements":{"default":["2027.582987a0-a4a3-45fd-828a-827cb45ab5a4"],"nodeVisualInfo":[{"width":24,"x":856,"y":157,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error 1","declaredType":"boundaryEvent","id":"2025.39241282-0b3a-4d38-8991-a91780160465","outputSet":{}},{"targetRef":"2025.a10bd8f4-3955-4392-83d0-95775ddf568e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Handling Error","declaredType":"sequenceFlow","id":"2027.defca81c-add7-4f05-8a3b-784f0277d901","sourceRef":"2025.7bab5193-44f4-49ef-89aa-a7edaf67a844"},{"targetRef":"2025.a10bd8f4-3955-4392-83d0-95775ddf568e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Handling Error","declaredType":"sequenceFlow","id":"2027.582987a0-a4a3-45fd-828a-827cb45ab5a4","sourceRef":"2025.39241282-0b3a-4d38-8991-a91780160465"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.eccce85a-e67e-4d7e-8c6a-5b6dace4f3ed"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorVIS","isCollection":false,"declaredType":"dataObject","id":"2056.030d1f86-d5bc-454c-8755-f123fa8cfed0"},{"targetRef":"2025.674068e6-9658-41eb-b7c7-f9ebe6ace86e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightBottom","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Review IDC Request by Branch Compliance Rep ","declaredType":"sequenceFlow","id":"2027.daef80d9-0e92-4f47-8070-56a8b9e54c6e","sourceRef":"2025.a10bd8f4-3955-4392-83d0-95775ddf568e"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"alertMessage","isCollection":false,"declaredType":"dataObject","id":"2056.40b004b1-570c-45ff-8442-0c8737a9ee69"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"38bad2a0-4680-421a-b988-bce3f08f0c13","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"e2a337ea-7fbc-4f30-81c4-98c8f116ae78","processType":"None"}],"exposedAs":["NotExposed"]},"implementation":"##unspecified","documentation":[{"textFormat":"text\/plain"}],"name":"Review IDC Request by Branch Compliance Rep","declaredType":"globalUserTask","id":"1.fcc27b9d-793f-4d79-b8ed-d17e5877201f","ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.1ced5e6a-3105-4bab-a14d-b6dcc7ee828a"},{"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"attachment","isCollection":true,"id":"2055.a1d974ed-0f69-49c0-b688-d2bc04f25f43"},{"itemSubjectRef":"itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1","name":"idcContract","isCollection":false,"id":"2055.38c836fb-47a2-4046-8a24-dfcacf35b225"}],"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.02818ba4-c183-4dfb-8924-18e2d9a515dd","epvProcessLinkId":"aee02dab-f257-48d5-8e2e-2ba534e01291","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.8ce8b34e-54bb-4623-a4c9-ab892efacac6","epvProcessLinkId":"c3097059-ecc3-444e-8577-841551bb6130","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e","epvProcessLinkId":"6345e77d-e047-4784-8bd0-d931d4ec3e5e","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.e5829eee-0ab1-4f47-9191-f0f8705bc33e","epvProcessLinkId":"95d20f21-f4b5-4497-84b2-a8cbe63d482c","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = {};\nautoObject.IDCRequestState = \"\";\nautoObject.commodityDescription = \"\";\nautoObject.countryOfOrigin = {};\nautoObject.countryOfOrigin.id = 0;\nautoObject.countryOfOrigin.code = \"\";\nautoObject.countryOfOrigin.arabicdescription = \"\";\nautoObject.countryOfOrigin.englishdescription = \"\";\nautoObject.appInfo = {};\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = {};\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.productsDetails = {};\nautoObject.productsDetails.destinationPort = \"\";\nautoObject.productsDetails.shippingDate = new Date();\nautoObject.productsDetails.HSProduct = {};\nautoObject.productsDetails.HSProduct.id = 0;\nautoObject.productsDetails.HSProduct.code = \"\";\nautoObject.productsDetails.HSProduct.arabicdescription = \"\";\nautoObject.productsDetails.HSProduct.englishdescription = \"\";\nautoObject.productsDetails.incoterms = {};\nautoObject.productsDetails.incoterms.id = 0;\nautoObject.productsDetails.incoterms.code = \"\";\nautoObject.productsDetails.incoterms.arabicdescription = \"\";\nautoObject.productsDetails.incoterms.englishdescription = \"\";\nautoObject.productsDetails.ACID = \"\";\nautoObject.productsDetails.CBECommodityClassification = {};\nautoObject.productsDetails.CBECommodityClassification.id = 0;\nautoObject.productsDetails.CBECommodityClassification.code = \"\";\nautoObject.productsDetails.CBECommodityClassification.arabicdescription = \"\";\nautoObject.productsDetails.CBECommodityClassification.englishdescription = \"\";\nautoObject.productsDetails.shipmentMethod = {};\nautoObject.productsDetails.shipmentMethod.id = 0;\nautoObject.productsDetails.shipmentMethod.code = \"\";\nautoObject.productsDetails.shipmentMethod.arabicdescription = \"\";\nautoObject.productsDetails.shipmentMethod.englishdescription = \"\";\nautoObject.financialDetails = {};\nautoObject.financialDetails.isAdvancePaymentsUsed = false;\nautoObject.financialDetails.paymentTerms = [];\nautoObject.financialDetails.paymentTerms[0] = {};\nautoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;\nautoObject.financialDetails.paymentTerms[0].installmentDate = new Date();\nautoObject.financialDetails.usedAdvancePayment = [];\nautoObject.financialDetails.usedAdvancePayment[0] = {};\nautoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = {};\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;\nautoObject.financialDetails.discountAmt = 0.0;\nautoObject.financialDetails.firstInstallementMaturityDate = new Date();\nautoObject.financialDetails.facilityAmtInDocCurrency = 0.0;\nautoObject.financialDetails.amtSight = 0.0;\nautoObject.financialDetails.beneficiaryDetails = {};\nautoObject.financialDetails.beneficiaryDetails.account = \"\";\nautoObject.financialDetails.beneficiaryDetails.bank = \"\";\nautoObject.financialDetails.beneficiaryDetails.correspondentRefNum = \"\";\nautoObject.financialDetails.beneficiaryDetails.name = \"\";\nautoObject.financialDetails.beneficiaryDetails.country = {};\nautoObject.financialDetails.beneficiaryDetails.country.id = 0;\nautoObject.financialDetails.beneficiaryDetails.country.code = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.arabicdescription = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.englishdescription = \"\";\nautoObject.financialDetails.amtDeferredNoAvalized = 0.0;\nautoObject.financialDetails.amtPayableByNBE = 0.0;\nautoObject.financialDetails.executionHub = {};\nautoObject.financialDetails.executionHub.id = 0;\nautoObject.financialDetails.executionHub.code = \"\";\nautoObject.financialDetails.executionHub.arabicdescription = \"\";\nautoObject.financialDetails.executionHub.englishdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency = {};\nautoObject.financialDetails.sourceOfForeignCurrency.id = 0;\nautoObject.financialDetails.sourceOfForeignCurrency.code = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.englishdescription = \"\";\nautoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.documentCurrency = {};\nautoObject.financialDetails.documentCurrency.id = 0;\nautoObject.financialDetails.documentCurrency.code = \"\";\nautoObject.financialDetails.documentCurrency.arabicdescription = \"\";\nautoObject.financialDetails.documentCurrency.englishdescription = \"\";\nautoObject.financialDetails.daysTillMaturity = 0;\nautoObject.financialDetails.tradeFinanceApprovalNumber = \"\";\nautoObject.financialDetails.amountAdvanced = 0.0;\nautoObject.financialDetails.paymentAccount = \"\";\nautoObject.financialDetails.tradeFOReferenceNumber = \"\";\nautoObject.financialDetails.documentAmount = 0.0;\nautoObject.financialDetails.sourceOfFunds = {};\nautoObject.financialDetails.sourceOfFunds.id = 0;\nautoObject.financialDetails.sourceOfFunds.code = \"\";\nautoObject.financialDetails.sourceOfFunds.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfFunds.englishdescription = \"\";\nautoObject.financialDetails.chargesAccount = \"\";\nautoObject.financialDetails.CashAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.amtDeferredAvalized = 0.0;\nautoObject.financialDetails.amtPaidbyOtherBanks = 0.0;\nautoObject.financialDetails.cashAmtInDocCurrency = 0.0;\nautoObject.IDCRequestType = {};\nautoObject.IDCRequestType.id = 0;\nautoObject.IDCRequestType.code = \"\";\nautoObject.IDCRequestType.arabicdescription = \"\";\nautoObject.IDCRequestType.englishdescription = \"\";\nautoObject.isIDCWithdrawn = false;\nautoObject.IDCRequestStage = \"\";\nautoObject.FCContractNumber = \"\";\nautoObject.billOfLading = [];\nautoObject.billOfLading[0] = {};\nautoObject.billOfLading[0].date = new Date();\nautoObject.billOfLading[0].number = \"\";\nautoObject.importPurpose = {};\nautoObject.importPurpose.id = 0;\nautoObject.importPurpose.code = \"\";\nautoObject.importPurpose.arabicdescription = \"\";\nautoObject.importPurpose.englishdescription = \"\";\nautoObject.IDCRequestNature = {};\nautoObject.IDCRequestNature.id = 0;\nautoObject.IDCRequestNature.code = \"\";\nautoObject.IDCRequestNature.arabicdescription = \"\";\nautoObject.IDCRequestNature.englishdescription = \"\";\nautoObject.customerInformation = {};\nautoObject.customerInformation.CIFNumber = \"\";\nautoObject.customerInformation.importCardNumber = \"\";\nautoObject.customerInformation.commercialRegistrationNumber = \"\";\nautoObject.customerInformation.customerName = \"\";\nautoObject.customerInformation.isCustomeSanctionedbyCBE = false;\nautoObject.customerInformation.CBENumber = \"\";\nautoObject.customerInformation.customerSector = \"\";\nautoObject.customerInformation.facilityType = \"\";\nautoObject.customerInformation.commercialRegistrationOffice = \"\";\nautoObject.customerInformation.taxCardNumber = \"\";\nautoObject.customerInformation.customerType = \"\";\nautoObject.invoices = [];\nautoObject.invoices[0] = {};\nautoObject.invoices[0].date = new Date();\nautoObject.invoices[0].number = \"\";\nautoObject.productCategory = {};\nautoObject.productCategory.id = 0;\nautoObject.productCategory.code = \"\";\nautoObject.productCategory.arabicdescription = \"\";\nautoObject.productCategory.englishdescription = \"\";\nautoObject.documentsSource = {};\nautoObject.documentsSource.id = 0;\nautoObject.documentsSource.code = \"\";\nautoObject.documentsSource.arabicdescription = \"\";\nautoObject.documentsSource.englishdescription = \"\";\nautoObject.ParentIDCRequestNumber = \"\";\nautoObject.paymentTerms = {};\nautoObject.paymentTerms.id = 0;\nautoObject.paymentTerms.code = \"\";\nautoObject.paymentTerms.arabicdescription = \"\";\nautoObject.paymentTerms.englishdescription = \"\";\nautoObject.approvals = {};\nautoObject.approvals.CAD = false;\nautoObject.approvals.treasury = false;\nautoObject.approvals.compliance = false;\nautoObject.appLog = [];\nautoObject.appLog[0] = {};\nautoObject.appLog[0].startTime = new Date();\nautoObject.appLog[0].endTime = new Date();\nautoObject.appLog[0].userName = \"\";\nautoObject.appLog[0].role = \"\";\nautoObject.appLog[0].step = \"\";\nautoObject.appLog[0].action = \"\";\nautoObject.appLog[0].comment = \"\";\nautoObject.appLog[0].terminateReason = \"\";\nautoObject.appLog[0].returnReason = \"\";\nautoObject.stepLog = {};\nautoObject.stepLog.startTime = new Date();\nautoObject.stepLog.endTime = new Date();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.8c22e68f-ffd6-4c68-95c5-85ce6c0d3ffa"},{"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"attachment","isCollection":true,"id":"2055.7ab26f1f-ad2f-4e75-ba8f-fe0f1eceef6a"},{"itemSubjectRef":"itm.12.ae22157e-8e19-4a60-a294-712ff5dc96df","name":"ECMproperties","isCollection":false,"id":"2055.ac26554a-eb1e-4e5f-8f4a-e579642f937c"},{"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"folderId","isCollection":false,"id":"2055.68399116-bb74-42ed-81db-c91a5974a390"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentFolderPath","isCollection":false,"id":"2055.4491d16f-b059-4d50-92d3-4f7afb6b188e"},{"itemSubjectRef":"itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1","name":"idcContract","isCollection":false,"id":"2055.8c31e0c6-583d-4ab8-8d2b-823252fe938f"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"1.bb7e1662-2c52-4ee4-830c-50662e3b6a44"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.8c22e68f-ffd6-4c68-95c5-85ce6c0d3ffa</processParameterId>
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>cb0c7cf4-41fc-45a5-a5a9-2a73b20ad273</guid>
            <versionId>d7495200-f9d4-402d-9353-ffb10d313513</versionId>
        </processParameter>
        <processParameter name="attachment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.7ab26f1f-ad2f-4e75-ba8f-fe0f1eceef6a</processParameterId>
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d6ef780c-2435-4a8a-99f4-2b462cf19e96</guid>
            <versionId>9417befa-9a84-4c4e-8809-bf14e228b83f</versionId>
        </processParameter>
        <processParameter name="ECMproperties">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.ac26554a-eb1e-4e5f-8f4a-e579642f937c</processParameterId>
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.ae22157e-8e19-4a60-a294-712ff5dc96df</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>c291be27-dde0-40fc-8faf-f033336b95a9</guid>
            <versionId>9dd6e401-f92e-47bc-8f92-5fa8c8e76d24</versionId>
        </processParameter>
        <processParameter name="folderId">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.68399116-bb74-42ed-81db-c91a5974a390</processParameterId>
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>2a4c13e3-a7c0-4ba7-8cd6-bd11d72d2727</guid>
            <versionId>3752932a-3c0a-4148-9f11-8d8cdfb6bcc2</versionId>
        </processParameter>
        <processParameter name="parentFolderPath">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.4491d16f-b059-4d50-92d3-4f7afb6b188e</processParameterId>
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>75aa3982-353f-4844-8271-48a8fc9ac3f9</guid>
            <versionId>bd992d9b-3183-4fa0-baac-9004a20ac991</versionId>
        </processParameter>
        <processParameter name="idcContract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.8c31e0c6-583d-4ab8-8d2b-823252fe938f</processParameterId>
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7cc171d6-0914-4a47-a029-d11ddab136df</guid>
            <versionId>67bd640a-a86f-4cc3-9931-5a988c306ea0</versionId>
        </processParameter>
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.1ced5e6a-3105-4bab-a14d-b6dcc7ee828a</processParameterId>
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>7</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a4c343c3-c7a5-48e9-8cdd-05bc3b0e32a2</guid>
            <versionId>3f50edc5-b26e-4012-be91-df72c24e67fd</versionId>
        </processParameter>
        <processParameter name="attachment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a1d974ed-0f69-49c0-b688-d2bc04f25f43</processParameterId>
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <seq>8</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>8e885bd4-1f06-4bd4-872f-176771ea92f1</guid>
            <versionId>35eb2cf2-b448-42b9-bf51-83c4961e00af</versionId>
        </processParameter>
        <processParameter name="idcContract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.38c836fb-47a2-4046-8a24-dfcacf35b225</processParameterId>
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classId>
            <seq>9</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>366a38ba-ff33-42ad-b470-2b512f7efea6</guid>
            <versionId>cd424c34-8f86-4124-82ff-f72dbbfa660b</versionId>
        </processParameter>
        <processVariable name="action">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.95442752-f74d-4d5f-9be4-63b0eedbd654</processVariableId>
            <description isNull="true" />
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f8d873c5-6d17-48cb-bd56-1c842578da54</guid>
            <versionId>1648ad4e-e492-4527-81ad-d8e26af6c1b1</versionId>
        </processVariable>
        <processVariable name="selectedAction">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e07008b5-84f5-4a66-bdce-9147ef829a85</processVariableId>
            <description isNull="true" />
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e4b01c42-407d-40c5-84bf-ab5c027aecd0</guid>
            <versionId>b73b1213-8d3b-4f6e-84fc-3b5b54de2d98</versionId>
        </processVariable>
        <processVariable name="hasAppovals">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9bd329d1-e5e7-4c4c-b7f1-2d787c41a1ab</processVariableId>
            <description isNull="true" />
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5c75ca74-6755-4f84-9842-70cfdbc74cc3</guid>
            <versionId>8d4e6ae0-25f8-4da1-a810-29f8095c09df</versionId>
        </processVariable>
        <processVariable name="hasReturnReason">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5086d9f2-ed1b-4c87-9853-babb6cbfd54d</processVariableId>
            <description isNull="true" />
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>037f9a4a-fa7c-415c-a615-27ec37457f12</guid>
            <versionId>9929ae77-f21d-4fa4-a176-356ba3be248b</versionId>
        </processVariable>
        <processVariable name="message">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.17535f57-83c9-4c47-a23c-0162bc928b6f</processVariableId>
            <description isNull="true" />
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6283fbc6-553e-4b0a-b4cf-49f9f11d0810</guid>
            <versionId>d04cba98-3e34-4490-aa5c-aee1e41ba7ed</versionId>
        </processVariable>
        <processVariable name="accountList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4698232d-b5eb-4d81-8290-3763ce058272</processVariableId>
            <description isNull="true" />
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>852a33ec-b5e4-4705-8d91-86d082630f22</guid>
            <versionId>998bdee3-f471-4dbb-9fb2-2d5b784285a0</versionId>
        </processVariable>
        <processVariable name="tmpusedAdvancePayment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7675b2ea-6825-40aa-8060-627cd796133f</processVariableId>
            <description isNull="true" />
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.fc23c5c1-8fd3-4584-89be-8352a606da67</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>06058a7c-15ac-467e-ab0a-480d1b135923</guid>
            <versionId>0c7d48c9-a579-4401-8caa-78307c478c75</versionId>
        </processVariable>
        <processVariable name="invalidTabs">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.646c3c20-c9e7-4cf6-825e-f1df12195933</processVariableId>
            <description isNull="true" />
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1303313d-7926-46a1-887b-8e872f8caf53</guid>
            <versionId>3d71d680-bcfd-46bf-ae96-01a862ae5a51</versionId>
        </processVariable>
        <processVariable name="validationMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b28c1b25-e7bd-4cc6-87ad-f065f93d24f2</processVariableId>
            <description isNull="true" />
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c31ff9d2-cfcc-4a47-8437-6b60b18358b5</guid>
            <versionId>dbf21500-7b18-4896-bdf3-6e26195f2095</versionId>
        </processVariable>
        <processVariable name="currencyVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.1606b9a8-9f69-4a1b-80f7-a3f3e8451c0f</processVariableId>
            <description isNull="true" />
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>feb1744d-17d0-43c5-96a2-5a25cfc1edcb</guid>
            <versionId>12a084a4-4a62-4d11-b9d5-67d8584b7ac4</versionId>
        </processVariable>
        <processVariable name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.361d283c-6280-494c-84b3-2b208d111256</processVariableId>
            <description isNull="true" />
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e446b4fc-dc89-442c-b218-9e261a84fe9f</guid>
            <versionId>9343e123-**************-cfbdd97e3dde</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.eccce85a-e67e-4d7e-8c6a-5b6dace4f3ed</processVariableId>
            <description isNull="true" />
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <namespace>2</namespace>
            <seq>12</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5ebf4d2e-d2a3-4513-a74a-49fd02f82cc8</guid>
            <versionId>df95f169-c0b8-4837-b804-66a3dd626103</versionId>
        </processVariable>
        <processVariable name="errorVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.030d1f86-d5bc-454c-8755-f123fa8cfed0</processVariableId>
            <description isNull="true" />
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <namespace>2</namespace>
            <seq>13</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5105e98c-4929-4624-9ddd-c59f647a9b1d</guid>
            <versionId>64623044-f044-4e6f-baa2-589cafbbbed9</versionId>
        </processVariable>
        <processVariable name="alertMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.40b004b1-570c-45ff-8442-0c8737a9ee69</processVariableId>
            <description isNull="true" />
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <namespace>2</namespace>
            <seq>14</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b1f99c5e-c407-41d9-8e55-156e2cd69f07</guid>
            <versionId>24fa61a1-b39a-4f76-afa4-8f34966cd816</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.4fe8f3b9-6936-420f-881f-57ff725e8573</processItemId>
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.89acbb52-236d-4ed6-8403-4b282205669b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-60ff</guid>
            <versionId>24cdd388-8fd8-4b93-b26d-94039583ba33</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.eaa2d5e4-652f-4c51-82fd-df5a42daaa0a</processItemId>
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <name>Database Integration</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.557133b8-6299-4d69-9ba4-ea513a1eebb8</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-6165</guid>
            <versionId>3b3e5529-f539-4614-9e1a-1d0eadd075ca</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.557133b8-6299-4d69-9ba4-ea513a1eebb8</subProcessId>
                <attachedProcessRef>/1.9f0a859b-5010-4ab6-947a-81ad99803cf1</attachedProcessRef>
                <guid>09dd2c95-2bcd-41e2-be8e-d540a05973cd</guid>
                <versionId>f009d65c-abc2-4dc1-995c-b756c820e92d</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.e3fbc36e-7be8-42ed-a6bf-bf0ecf20fd16</processItemId>
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <name>cancel request</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.f91cd246-9a9f-4054-949c-f2890a3d6d23</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-60fd</guid>
            <versionId>6246c8c8-373b-4fba-94ce-ba9c4a7efd41</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.f91cd246-9a9f-4054-949c-f2890a3d6d23</subProcessId>
                <attachedProcessRef>/1.96dc5449-b281-4b46-8b84-bf73531c54ff</attachedProcessRef>
                <guid>a0f59034-2907-496f-8019-1e5868fd918b</guid>
                <versionId>f8532a58-97d6-4c9d-87f1-f51839431bca</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.34a162e5-7f0d-457b-b7a5-6f5e6821acc4</processItemId>
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.b552675a-f03a-42b0-9a26-5e9765055db7</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-60fe</guid>
            <versionId>d378e989-e441-4ff9-950d-231f9492cf54</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.b552675a-f03a-42b0-9a26-5e9765055db7</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>d8ddaf36-814a-4b7a-af9b-19b2a90de547</guid>
                <versionId>2b7a392a-b0fb-42de-929f-b4499a383a81</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.ea0ee1b8-b9be-41a4-996e-6c8e5fb38342</epvProcessLinkId>
            <epvId>/21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e</epvId>
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <guid>7582e7be-8b5e-4b2e-9e44-cc06ecef2508</guid>
            <versionId>4f39d9ca-b070-44f4-835a-3a2baa32a450</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.ef25be18-3d02-402a-a7c7-abad404c7e0d</epvProcessLinkId>
            <epvId>/21.8ce8b34e-54bb-4623-a4c9-ab892efacac6</epvId>
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <guid>eeceae66-8ea7-4aa0-9094-99e73411cdbc</guid>
            <versionId>b087b44b-2365-449f-82ee-3203a6465146</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.ad943a34-4022-4f07-b89b-cb8a15acb302</epvProcessLinkId>
            <epvId>/21.02818ba4-c183-4dfb-8924-18e2d9a515dd</epvId>
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <guid>97279e7e-a029-40bd-a82e-7088a12a10c8</guid>
            <versionId>f72429a2-b4cd-4bc1-b02b-59fa9397e3fc</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.5bcb5644-4321-43f1-92f1-321d3ce314fe</epvProcessLinkId>
            <epvId>/21.e5829eee-0ab1-4f47-9191-f0f8705bc33e</epvId>
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <guid>db4588f7-01f7-4542-ab68-dddec935fb30</guid>
            <versionId>ff71276f-4a3c-4f75-acdb-80ba2fcb3b4e</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.4fe8f3b9-6936-420f-881f-57ff725e8573</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="1.bb7e1662-2c52-4ee4-830c-50662e3b6a44" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:globalUserTask implementation="##unspecified" name="Review IDC Request by Branch Compliance Rep" id="1.fcc27b9d-793f-4d79-b8ed-d17e5877201f">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:userTaskImplementation processType="None" isClosed="false" id="e2a337ea-7fbc-4f30-81c4-98c8f116ae78">
                            
                            
                            <ns16:startEvent name="Start" id="03ba0e2b-9e61-463b-afc9-8cc318a54ba4">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="-60" y="251" width="24" height="24" color="#F8F8F8" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.45687d69-7665-4a7a-a3ed-07d1f92982cd</ns16:outgoing>
                                
                            
                            </ns16:startEvent>
                            
                            
                            <ns16:endEvent name="End" id="9992114f-993b-48aa-bd87-b2ac7f3df53e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1101" y="234" width="24" height="44" color="#F8F8F8" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.a1a7af68-8cdd-42c3-a6bd-5301f76db972</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.e5384557-e5a5-4ff6-85fc-b86ec47c9360</ns16:incoming>
                                
                            
                            </ns16:endEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="03ba0e2b-9e61-463b-afc9-8cc318a54ba4" targetRef="2025.0ab2e4bb-cb68-4add-8b23-af437a1ba0ee" name="Start To Coach" id="2027.45687d69-7665-4a7a-a3ed-07d1f92982cd">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.2c590891-71b6-458e-9ee3-f3a3ce0beae8" name="Set Status " id="2025.a40f655e-1d12-4ad7-a4fd-66269eb05204">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="671" y="99" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.0f916482-3ee6-481c-80b4-49eca9d330ec</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.2c590891-71b6-458e-9ee3-f3a3ce0beae8</ns16:outgoing>
                                
                                
                                <ns16:script>if (tw.local.selectedAction == tw.epv.Action.returnToInitiator) {&#xD;
	tw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.returnedtoInitiator;&#xD;
	tw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.initiated;&#xD;
	&#xD;
}else if (tw.local.selectedAction == tw.epv.Action.cancelReques) {&#xD;
	tw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.canceled;&#xD;
	tw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.canceled;&#xD;
	if (tw.local.idcRequest.IDCRequestNature.englishdescription == "New Request") {&#xD;
		tw.local.idcRequest.IDCRequestState = tw.epv.IDCState.canceled;&#xD;
	}&#xD;
} else {&#xD;
	tw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingTradeFOReview;&#xD;
	tw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inApproval;		&#xD;
}&#xD;
&#xD;
tw.local.idcRequest.stepLog.action = tw.local.selectedAction;</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.a40f655e-1d12-4ad7-a4fd-66269eb05204" targetRef="2025.eaa2d5e4-652f-4c51-82fd-df5a42daaa0a" name="To End" id="2027.2c590891-71b6-458e-9ee3-f3a3ce0beae8">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.fde9282d-2251-4e30-8d4c-75f282e74ebb" name="Initialization Script" id="2025.0ab2e4bb-cb68-4add-8b23-af437a1ba0ee">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="46" y="228" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.45687d69-7665-4a7a-a3ed-07d1f92982cd</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.fde9282d-2251-4e30-8d4c-75f282e74ebb</ns16:outgoing>
                                
                                
                                <ns16:script>tw.local.idcRequest.stepLog = {};&#xD;
tw.local.idcRequest.stepLog.startTime = new Date();&#xD;
tw.local.errorVIS = "NONE";&#xD;
&#xD;
tw.local.action = [];&#xD;
if (tw.local.idcRequest.appInfo.subStatus == tw.epv.IDCsubStatus.pendingBranchComplianceCancelationConfirmation) {&#xD;
	tw.local.action[0] = tw.epv.Action.cancelReques;&#xD;
}else{&#xD;
	tw.local.action[0] = tw.epv.Action.submitRequest;&#xD;
}&#xD;
tw.local.action[1] = tw.epv.Action.returnToInitiator;&#xD;
</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.0ab2e4bb-cb68-4add-8b23-af437a1ba0ee" targetRef="2025.ccdf90b5-9bd3-40bc-844d-6bd43a4df994" name="To Review IDC Request by Branch Compliance Rep " id="2027.fde9282d-2251-4e30-8d4c-75f282e74ebb">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns3:formTask name="Review IDC Request by Branch Compliance Rep " id="2025.674068e6-9658-41eb-b7c7-f9ebe6ace86e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="386" y="228" width="95" height="70" />
                                    
                                    
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    
                                    
                                    <ns3:preAssignmentScript />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.d7f96f9d-db0f-4642-a665-1e97407e6f9d</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.9e2a2dcf-bd0f-4b0f-8abb-bcc12b04c143</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.aadd02f6-916e-4869-8bd1-757d05fd43c9</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.daef80d9-0e92-4f47-8070-56a8b9e54c6e</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.dea43813-d9dc-4fe0-89aa-51adf2f603b3</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.ad9dccf1-bf74-44df-9568-0a53b84bfc77</ns16:outgoing>
                                
                                
                                <ns3:formDefinition>
                                    
                                    
                                    <ns19:coachDefinition>
                                        
                                        
                                        <ns19:layout>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>a6c35b4a-ecb5-4fd3-85d7-7052324ea154</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>Error_Message1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>a133aa0d-1812-4316-8f4d-f3acf6ea5103</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Error Message</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>80c42832-1efb-4ad1-8727-32d25a557967</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>ae661e06-8d94-4cf3-8568-dbcdc6aa7d1c</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>SHOW</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>a627dc11-a925-469d-833d-6d3847adc0fb</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>a822f17f-e970-4757-8693-5faf24153eb7</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>057f6a85-8826-4309-807d-d85a0b2dd646</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.46690ca1-5521-4e5c-bc9e-ecb22d75ca97</ns19:viewUUID>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>8e47622d-c2b8-4507-8e39-6bc5f933f277</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>DC_Templete1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>e2fa476b-6ad0-4ae2-822d-f814e07da011</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>DC Templete</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>d8d7cf75-c0ba-4bf0-8eb1-298d26c5b271</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>dc611c48-5458-435d-8a93-b9e551bd28fe</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>SHOW</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>fd84e6c0-9a47-40b0-84da-b566b47939f5</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>stepLog</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.idcRequest.stepLog</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>e68ef455-9674-4f8c-89dd-7f720b0b6aa9</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>action</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.action[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>565f7b17-dde6-4a34-82bc-d512012fe3bc</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>selectedAction</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.selectedAction</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>b00241d9-e621-4f3b-8b69-76fa04efd393</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>hasReturnReason</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.hasReturnReason</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>d22ebdfb-c39c-4e2a-8a07-a8b737695f7d</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>buttonName</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Submit</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>0ec4e78d-e7e5-452a-87c7-5abd73463846</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>hasApprovals</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.hasAppovals</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>05825f62-25d9-497f-89ae-3f73677357b6</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>approvals</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.idcRequest.approvals</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>fb1e29c3-d9e6-4e4c-8eb8-ef78b65877e2</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>invalidTabs</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.invalidTabs[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>73b1dcf8-19ab-443f-8d55-3e8ea81ea739</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>validationMessage</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.validationMessage</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>f82acac6-0a14-404d-8668-9748e05ca778</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>isCAD</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>false</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</ns19:viewUUID>
                                                
                                                
                                                <ns19:binding>tw.local.idcRequest.appInfo</ns19:binding>
                                                
                                                
                                                <ns19:contentBoxContrib>
                                                    
                                                    
                                                    <ns19:id>4c632626-4292-4d06-8de0-4dc6dabf88eb</ns19:id>
                                                    
                                                    
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    
                                                    
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        
                                                        
                                                        <ns19:id>2a9d45c0-81ab-45c3-80a9-59f7e80d7e72</ns19:id>
                                                        
                                                        
                                                        <ns19:layoutItemId>Tab_Section1</ns19:layoutItemId>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>44d0e9f8-0e8c-4944-8b80-666399043dd8</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>Tab section</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>65bc910e-1233-47dd-8f2e-6d71b3b788dd</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value />
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>c30fd57f-2eb5-4ebc-82ea-627e8d99bce3</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>SHOW</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>d1a93586-bd36-4a26-8d02-69e3f810f7b0</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>sizeStyle</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"X"}]}</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:viewUUID>64.c05b439f-a4bd-48b0-8644-fa3b59052217</ns19:viewUUID>
                                                        
                                                        
                                                        <ns19:contentBoxContrib>
                                                            
                                                            
                                                            <ns19:id>3999f3e9-4ab5-413b-8377-69c07ce4b14a</ns19:id>
                                                            
                                                            
                                                            <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>4cca9ca2-4756-4e27-8d5a-d6ad632300f2</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Customer_Information1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>bc792304-d2f7-4a1b-8819-800d67c0a1ba</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Customer Information</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>272eca51-edaa-4d5a-8589-d99e081773c0</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>60174edb-c557-415a-83be-0d0dee94dc10</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>bfeb143b-a875-4d5f-8e16-************</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>0fb1f1e4-ab1f-4ed9-82d5-292cb4306c5b</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>instanceview</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>903a7bd2-148e-4a5b-8400-722153281ba7</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.alertMessage</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.656cc232-8247-43c3-9481-3cc7a9aec2e6</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.idcRequest.customerInformation</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>0e5d44f6-4c02-4700-8cd5-785d5077b0e1</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Basic_Details1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>c79d5bf6-51a4-4d8c-896a-2c1e973508bf</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Basic Details</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>31a0d47f-aaca-4e18-82f9-d253c75d11be</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>16bedcc6-4d66-4939-88ec-aa46b7982b04</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>2741b562-dc46-46e7-8ecf-482b9d37ed5f</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>b2838c50-0a60-463d-8637-d0dd274885f6</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>hasWithdraw</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>da1bda0b-9751-45b1-8012-cd1a4f21e030</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>havePaymentTerm</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>no</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>851c0211-8ff1-45da-87a0-3fbad5c2411b</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>addBill</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>5e4b34b8-ca4f-4683-8a8f-73fd10aa5760</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>deleteBill</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>6c42882c-e14d-4b3b-84c3-7b7318e8719a</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>addInvoice</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>f509f9b2-8a9e-46ce-838d-3058ded8b60f</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>deleteInvoice</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>4a10d44e-5037-49a2-8db0-c24ff1a3ed38</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>billExist</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>0</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>b579e6f2-60e1-4848-8332-7ead9531e8ed</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>invoiceExist</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>0</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>04710077-7e09-480f-8e0d-4fc8cd04e45a</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>5bac026b-a7c1-4f98-8b5f-e52e554b7933</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>6a4ce03e-e7f6-4ac0-8845-c2eb645cd8f7</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>idcContract</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.idcContract</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.3d13e9b9-bcc9-4fe9-ac80-3c287115f705</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.idcRequest</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>39af9c0e-386c-4315-8fd8-e4bc4d0a1e55</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Financial_Details__Branch1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>ecfe65fc-6487-418a-8364-72369e95f4d2</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Financial Details  Branch</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>446d1e93-3dfc-4dce-8706-5edd59d9fce6</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>9f4661a3-**************-0237b166386b</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>ca451e9d-75da-4cf8-8486-b4f6135eb144</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>b345bc78-abe7-4993-8bf0-8afae00e1624</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>requestType</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.idcRequest.IDCRequestType.englishdescription</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>3756a0c2-1692-440a-8b0e-3f15b5a24210</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>docAmount</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>bf50c943-5596-4abe-8b85-ad597ed3de93</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>currncy</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>24a57d72-c77e-4c0b-8daf-455cb46ed47d</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>CIF</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.idcRequest.customerInformation.CIFNumber</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>77f60bc1-e240-4365-83c6-c6ec33fd5264</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>accountsList</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.accountList[]</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>7483afc5-27d9-452d-8632-7dea8eb0701e</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>haveAmountAdvanced</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>DEFAULT</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>862c000a-6be0-4a73-8eb8-dbc666d3f807</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>tmpUsedAdvancePayment</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.tmpusedAdvancePayment</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>9988f96e-3edf-4d51-8f3e-25703a061c37</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>isChecker</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>********-47ef-438a-801c-bd5f6e8097f2</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>currencyVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.currencyVis</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>85ef1b10-3d81-40f0-83ae-613732420c58</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>requestID</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.idcRequest.appInfo.instanceID</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>23778c44-923c-4fa6-81b1-bf8196bdb0ea</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>4b9d4e82-670c-4057-87aa-0056ba426bb5</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.74d3cb97-ad59-4249-847b-a21122e44b22</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.idcRequest.financialDetails</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>ea73ae17-df18-496e-8913-a3d8d88749bd</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Attachment1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>ecb89bda-648f-4aa1-845f-91f831206c43</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Attachment</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>e0c0eb4b-0841-49b3-822c-db15f52fb53e</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>6327e9e8-c8ba-46bd-837f-dd11a332b291</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>967c952a-89dd-4b98-8d6b-a4853f9c11d6</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>217fdc74-15c9-48e3-8434-7b214903dbf1</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canUpdate</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>d2d87e7f-dad9-4a82-80aa-6591d6fe3f99</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canCreate</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>1d352a36-f0c8-4565-85aa-1c90a61f3fe6</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canDelete</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>d222eda3-ba5c-4d9c-8812-38c0af121c3a</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>ECMproperties</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.ECMproperties</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>13f43578-7345-448c-89e1-9392eec4a868</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>visiable</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.attachment[]</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>6b6ce5b6-74e8-4517-834c-d4590fc29090</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>App_History_View_21</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>50f0b2d8-1b80-4089-88b2-05276a2edb52</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>History</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>7432398d-1cfc-4805-8114-b3b32b72030d</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>91494416-45be-4dff-8cd2-a690ef826e6d</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>74277b46-38ab-4722-868a-4c34adfbd787</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>historyVisFlag</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>None</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>1a0ab0c2-6f46-4601-88ab-187bff923bb0</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.6ac8a533-b05a-45a1-9e46-1c4542e6b8be</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.idcRequest.appLog[]</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                        
                                                        </ns19:contentBoxContrib>
                                                        
                                                    
                                                    </ns19:contributions>
                                                    
                                                
                                                </ns19:contentBoxContrib>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                        
                                        </ns19:layout>
                                        
                                    
                                    </ns19:coachDefinition>
                                    
                                
                                </ns3:formDefinition>
                                
                            
                            </ns3:formTask>
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page" id="2025.df8c1d03-6eee-4dd9-8be1-f6e44fb77597">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="395" y="387" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                    
                                    <ns3:default>2027.d7f96f9d-db0f-4642-a665-1e97407e6f9d</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.dea43813-d9dc-4fe0-89aa-51adf2f603b3</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.d7f96f9d-db0f-4642-a665-1e97407e6f9d</ns16:outgoing>
                                
                                
                                <ns3:postponeTaskEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.674068e6-9658-41eb-b7c7-f9ebe6ace86e" targetRef="2025.df8c1d03-6eee-4dd9-8be1-f6e44fb77597" name="To Stay on page" id="2027.dea43813-d9dc-4fe0-89aa-51adf2f603b3">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomLeft</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="50eae49c-c415-458c-a0c2-2ac4ec8e8bdd">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/saveState</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.674068e6-9658-41eb-b7c7-f9ebe6ace86e" targetRef="2025.b925df26-c251-4d42-9f18-790ee6646ce7" name="To End" id="2027.ad9dccf1-bf74-44df-9568-0a53b84bfc77">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="891654b5-4515-4b04-8856-41896e98237d">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/submit</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.df8c1d03-6eee-4dd9-8be1-f6e44fb77597" targetRef="2025.674068e6-9658-41eb-b7c7-f9ebe6ace86e" name="To Review IDC Request by Branch Compliance Rep " id="2027.d7f96f9d-db0f-4642-a665-1e97407e6f9d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomRight</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="action" id="2056.95442752-f74d-4d5f-9be4-63b0eedbd654" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="selectedAction" id="2056.e07008b5-84f5-4a66-bdce-9147ef829a85" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="hasAppovals" id="2056.9bd329d1-e5e7-4c4c-b7f1-2d787c41a1ab">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">false</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="hasReturnReason" id="2056.5086d9f2-ed1b-4c87-9853-babb6cbfd54d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">true</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.5be73306-3480-4550-b624-135fb150539b" name="validation" id="2025.b925df26-c251-4d42-9f18-790ee6646ce7">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="386" y="99" width="95" height="70" color="#95D087" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.ad9dccf1-bf74-44df-9568-0a53b84bfc77</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.5be73306-3480-4550-b624-135fb150539b</ns16:outgoing>
                                
                                
                                <ns16:script>tw.local.message = "";&#xD;
var mandatoryTriggered = false;&#xD;
var tempLength = 0 ;&#xD;
//tw.local.invalidTabs = [];&#xD;
/*&#xD;
* =========================================================================================================&#xD;
*  &#xD;
* Add a coach validation error &#xD;
* 		&#xD;
* EX:	addError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');&#xD;
*&#xD;
* =========================================================================================================&#xD;
*/&#xD;
&#xD;
function addError(fieldName , controlMessage , validationMessage , fromMandatory)&#xD;
{&#xD;
	tw.system.coachValidation.addValidationError(fieldName, controlMessage);&#xD;
	fromMandatory &amp;&amp; mandatoryTriggered ? "" : tw.local.message += "&lt;li dir='rtl'&gt;" + validationMessage + "&lt;/li&gt;";&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is in the past and the last variable to exclude today&#xD;
*	&#xD;
* EX:	notPastDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)&#xD;
*&#xD;
* =============================================================================================================================&#xD;
*/&#xD;
&#xD;
function notPastDate(date , fieldName , controlMessage , validationMessage , exclude)&#xD;
{&#xD;
	if (exclude)&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &lt;= new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;	&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &lt; new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is between two dates&#xD;
*	&#xD;
* EX:	dateBetween(tw.local.date , date1 , date2 , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =============================================================================================================================&#xD;
*/&#xD;
&#xD;
function dateBetween(field , date1 , date2 , fieldName , controlMessage , validationMessage)&#xD;
{&#xD;
	if(field &lt; date1 &amp;&amp; field &gt; date2)&#xD;
	{&#xD;
	 	return true;&#xD;
	}&#xD;
	addError(fieldName , controlMessage , validationMessage);&#xD;
	return false;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ===============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is in the future and the last varaible to exculde today&#xD;
*	&#xD;
* EX:	notFutureDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)&#xD;
*&#xD;
* ===============================================================================================================================&#xD;
*/&#xD;
&#xD;
function notFutureDate(date , fieldName ,  controlMessage , validationMessage , exculde)&#xD;
{&#xD;
	if (exculde)&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &gt;= new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;	&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &gt; new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
&#xD;
/*&#xD;
* =================================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the string length is less than given length&#xD;
*	&#xD;
* EX:	minLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =================================================================================================================================&#xD;
*/&#xD;
&#xD;
function minLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the string length is greater than given length&#xD;
*	&#xD;
* EX:	maxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function maxLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &gt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the number is greater than given value&#xD;
*	&#xD;
* EX:	maxNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function maxNumber(field , fieldName , max , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field &gt; max)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the number is less than given value&#xD;
*	&#xD;
* EX:	minNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function minNumber(field , fieldName , min , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field &lt; min)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the field is null 'Mandatory'&#xD;
*	&#xD;
* EX:	notNull(tw.local.name , 'tw.local.name')&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
&#xD;
function mandatory(field , fieldName)&#xD;
{&#xD;
	if (field == null || field == undefined )&#xD;
	{&#xD;
		addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
		mandatoryTriggered = true;&#xD;
		return false;&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		switch (typeof field)&#xD;
		{&#xD;
			case "string":&#xD;
				if (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)&#xD;
				{&#xD;
					addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
			case "number":&#xD;
				if (field == 0.0)&#xD;
				{&#xD;
					addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
		}&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a validation mark on the tab if there is an error&#xD;
*	&#xD;
* EX:	validateTab(1)&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
&#xD;
function validateTab(index)&#xD;
{&#xD;
	if (tw.system.coachValidation.validationErrors.length &gt; tempLength)&#xD;
	{&#xD;
		tempLength = tw.system.coachValidation.validationErrors.length ;&#xD;
		tw.local.invalidTabs[tw.local.invalidTabs.length] = index;&#xD;
	}	&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a validation mark on the tab if there is an error&#xD;
*	&#xD;
* EX:	validateTab(1)&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
//function validateDecimal(field, fieldName, controlMessage , validationMessage) {&#xD;
//   regexString = `^\\d{1,12}(\\.\\d{1,12})?$`;&#xD;
//   regex = new RegExp(regexString);&#xD;
//&#xD;
//  if (!regex.test(field))&#xD;
//	{&#xD;
//		addError(fieldName , controlMessage , validationMessage);&#xD;
//		return false;&#xD;
//	}&#xD;
//	return true;&#xD;
//}&#xD;
//-----------------------------------------financial Details---------------------------------------------------------&#xD;
//mandatory(tw.local.idcRequest.financialDetails.documentAmount,"tw.local.idcRequest.financialDetails.documentAmount");&#xD;
//validateDecimal(tw.local.idcRequest.financialDetails.documentAmount, "tw.local.idcRequest.financialDetails.documentAmount", "max length is 14" , "max length is 14");&#xD;
//minNumber(tw.local.idcRequest.financialDetails.documentAmount , "tw.local.idcRequest.financialDetails.documentAmount" , 0.01 , "must be more than 0" , "must be more than 0");&#xD;
&#xD;
//mandatory(tw.local.idcRequest.financialDetails.chargesAccount,"tw.local.idcRequest.financialDetails.chargesAccount");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.paymentAccount,"tw.local.idcRequest.financialDetails.paymentAccount");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.documentCurrency.code,"tw.local.idcRequest.financialDetails.documentCurrency.code");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.code,"tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.code");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.sourceOfFunds.code,"tw.local.idcRequest.financialDetails.sourceOfFunds.code");&#xD;
//----------------------------------basic details------------------------------------------------------------------------------&#xD;
&#xD;
//mandatory(tw.local.idcRequest.importPurpose.code,"tw.local.idcRequest.importPurpose.code");&#xD;
//mandatory(tw.local.idcRequest.paymentTerms.code,"tw.local.idcRequest.paymentTerms.code");&#xD;
//mandatory(tw.local.idcRequest.documentsSource.code,"tw.local.idcRequest.documentsSource.code");&#xD;
//mandatory(tw.local.idcRequest.productCategory.code,"tw.local.idcRequest.productCategory.code");&#xD;
//mandatory(tw.local.idcRequest.commodityDescription,"tw.local.idcRequest.commodityDescription");&#xD;
//if (tw.local.idcRequest.invoices.length&gt;0){&#xD;
//	for (var i=0; i&lt;tw.local.idcRequest.invoices.length; i++) {&#xD;
//		mandatory(tw.local.idcRequest.invoices[i].number,"tw.local.idcRequest.invoices["+i+"].number");&#xD;
//		mandatory(tw.local.idcRequest.invoices[i].date,"tw.local.idcRequest.invoices["+i+"].date");&#xD;
//	}&#xD;
//}&#xD;
//if(tw.local.idcRequest.billOfLading.length&gt;0){&#xD;
//	for (var i=0; i&lt;tw.local.idcRequest.billOfLading.length; i++) {&#xD;
//		mandatory(tw.local.idcRequest.billOfLading[i].number,"tw.local.idcRequest.billOfLading["+i+"].number");&#xD;
//		mandatory(tw.local.idcRequest.billOfLading[i].date,"tw.local.idcRequest.billOfLading["+i+"].date");&#xD;
//	}&#xD;
//}&#xD;
//mandatory(tw.local.idcRequest.countryOfOrigin.code,"tw.local.idcRequest.countryOfOrigin.code");&#xD;
//----------------------------------------app info------------------------------------------------------------------------------------&#xD;
//mandatory(tw.local.selectedAction,"tw.local.selectedAction");&#xD;
//------------------------------------financial Details fo -------------------------------------------------------------------	&#xD;
//mandatory(tw.local.idcRequest.financialDetails.firstInstallementMaturityDate,"tw.local.idcRequest.financialDetails.firstInstallementMaturityDate" );&#xD;
//mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.name,"tw.local.idcRequest.financialDetails.beneficiaryDetails.name");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.bank,"tw.local.idcRequest.financialDetails.beneficiaryDetails.bank" );	&#xD;
//mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.account,"tw.local.idcRequest.financialDetails.beneficiaryDetails.account" );&#xD;
//mandatory(tw.local.idcRequest.financialDetails.beneficiaryDetails.country.code,"tw.local.idcRequest.financialDetails.beneficiaryDetails.country.code");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.tradeFinanceApprovalNumber,"tw.local.idcRequest.financialDetails.tradeFinanceApprovalNumber" );&#xD;
//if (tw.local.selectedAction == tw.epv.Action.submitRequest) {&#xD;
//	mandatory(tw.local.idcRequest.financialDetails.executionHub.code,"tw.local.idcRequest.financialDetails.executionHub.code");&#xD;
//}&#xD;
//var sum = tw.local.idcRequest.financialDetails.cashAmtInDocCurrency + tw.local.idcRequest.financialDetails.CashAmtWithNoCurrency + tw.local.idcRequest.financialDetails.facilityAmtInDocCurrency + tw.local.idcRequest.financialDetails.facilityAmtWithNoCurrency;&#xD;
//if(sum != tw.local.idcRequest.financialDetails.amtPayableByNBE){&#xD;
//	addError("tw.local.idcRequest.financialDetails.cashAmtInDocCurrency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" );&#xD;
//	addError("tw.local.idcRequest.financialDetails.CashAmtWithNoCurrency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" );	&#xD;
//	addError("tw.local.idcRequest.financialDetails.facilityAmtInDocCurrency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" );&#xD;
//	addError("tw.local.idcRequest.financialDetails.facilityAmtWithNoCurrency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Cash Amount-Document Currency,Cash Amount-no Currency,Facility Amount-Document Currency,Facility Amount-No Currency" );	&#xD;
//&#xD;
//}&#xD;
//&#xD;
//var sum =  tw.local.idcRequest.financialDetails.amtSight+tw.local.idcRequest.financialDetails.amtDeferredNoAvalized +tw.local.idcRequest.financialDetails.amtDeferredAvalized;&#xD;
//if (sum!=tw.local.idcRequest.financialDetails.amtPayableByNBE) {&#xD;
//	addError("tw.local.idcRequest.financialDetails.amtSight" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization");&#xD;
//	addError("tw.local.idcRequest.financialDetails.amtDeferredNoAvalized" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization");	&#xD;
//	addError("tw.local.idcRequest.financialDetails.amtDeferredAvalized" , "please ensure that the follwing fields is equal to Amount Payable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization");	&#xD;
//}&#xD;
//for (var i=0; i&lt;tw.local.idcRequest.financialDetails.paymentTerms.length; i++) {&#xD;
//	&#xD;
//	mandatory(tw.local.idcRequest.financialDetails.paymentTerms[i].installmentDate,"tw.local.idcRequest.financialDetails.paymentTerms["+i+"].installmentDate");&#xD;
//	mandatory(tw.local.idcRequest.financialDetails.paymentTerms[i].installmentAmount,"tw.local.idcRequest.financialDetails.paymentTerms["+i+"].installmentAmount");&#xD;
//}&#xD;
//-------------------------------action-----------------------------------------------------------------------&#xD;
if (tw.local.selectedAction == tw.epv.Action.returnToInitiator) {&#xD;
	mandatory(tw.local.idcRequest.stepLog.returnReason,"tw.local.idcRequest.stepLog.returnReason");&#xD;
}&#xD;
if (tw.local.selectedAction == tw.epv.Action.cancelReques) {&#xD;
	mandatory(tw.local.idcRequest.stepLog.comment,"tw.local.idcRequest.stepLog.comment");&#xD;
}&#xD;
&#xD;
//if (tw.local.selectedAction == tw.epv.Action.terminateRequest) {&#xD;
//	mandatory(tw.local.idcRequest.stepLog.comment,"tw.local.idcRequest.stepLog.comment");&#xD;
//}&#xD;
//&#xD;
//if ((tw.local.idcRequest.approvals.CAD==true|| &#xD;
//    tw.local.idcRequest.approvals.compliance==true ||&#xD;
//    tw.local.idcRequest.approvals.treasury==true))&#xD;
//{   &#xD;
//    if (tw.local.selectedAction != "Obtain Approvals") {&#xD;
//       addError("tw.local.selectedAction", "Please uncheck Approvals");&#xD;
//    }&#xD;
//}&#xD;
//else if (tw.local.selectedAction == "Obtain Approvals")&#xD;
//{&#xD;
//    addError("tw.local.selectedAction", "Please check Approvals");&#xD;
//}&#xD;
mandatory(tw.local.selectedAction,"tw.local.selectedAction");</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:exclusiveGateway default="2027.0f916482-3ee6-481c-80b4-49eca9d330ec" gatewayDirection="Unspecified" name="Have Errors" id="2025.ff420b18-2db5-439f-9743-f8d1ea64ad6f">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="556" y="118" width="32" height="32" />
                                    
                                    
                                    <ns3:postAssignmentScript />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.5be73306-3480-4550-b624-135fb150539b</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.9e2a2dcf-bd0f-4b0f-8abb-bcc12b04c143</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.0f916482-3ee6-481c-80b4-49eca9d330ec</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.b925df26-c251-4d42-9f18-790ee6646ce7" targetRef="2025.ff420b18-2db5-439f-9743-f8d1ea64ad6f" name="To Have Errors" id="2027.5be73306-3480-4550-b624-135fb150539b">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.ff420b18-2db5-439f-9743-f8d1ea64ad6f" targetRef="2025.674068e6-9658-41eb-b7c7-f9ebe6ace86e" name="yes" id="2027.9e2a2dcf-bd0f-4b0f-8abb-bcc12b04c143">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.system.coachValidation.validationErrors.length	  &gt;	  0</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.ff420b18-2db5-439f-9743-f8d1ea64ad6f" targetRef="2025.a40f655e-1d12-4ad7-a4fd-66269eb05204" name="no" id="2027.0f916482-3ee6-481c-80b4-49eca9d330ec">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.aadd02f6-916e-4869-8bd1-757d05fd43c9" name="Set Step Name" id="2025.ccdf90b5-9bd3-40bc-844d-6bd43a4df994">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="216" y="228" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.fde9282d-2251-4e30-8d4c-75f282e74ebb</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.aadd02f6-916e-4869-8bd1-757d05fd43c9</ns16:outgoing>
                                
                                
                                <ns16:script>tw.local.idcRequest.appInfo.stepName = tw.system.processInstance.task.subject;&#xD;
tw.local.idcRequest.appInfo.appID = tw.system.processInstance.id;&#xD;
&#xD;
</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.ccdf90b5-9bd3-40bc-844d-6bd43a4df994" targetRef="2025.674068e6-9658-41eb-b7c7-f9ebe6ace86e" name="To Review IDC Request by Branch Compliance Rep " id="2027.aadd02f6-916e-4869-8bd1-757d05fd43c9">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="message" id="2056.17535f57-83c9-4c47-a23c-0162bc928b6f" />
                            
                            
                            <ns16:exclusiveGateway default="2027.e5384557-e5a5-4ff6-85fc-b86ec47c9360" name="Exclusive Gateway" id="2025.6de48f82-d506-4a36-8f83-b70bf08ccaec">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="950" y="118" width="32" height="32" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.b2765b1e-4861-45ee-8280-6fd1cd13212f</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.687c7107-7d98-4641-89ee-8dcfad8e8956</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.e5384557-e5a5-4ff6-85fc-b86ec47c9360</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.6de48f82-d506-4a36-8f83-b70bf08ccaec" targetRef="2025.e3fbc36e-7be8-42ed-a6bf-bf0ecf20fd16" name="To cancel request" id="2027.687c7107-7d98-4641-89ee-8dcfad8e8956">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.selectedAction	  ==	  tw.epv.Action.cancelReques</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:callActivity calledElement="1.96dc5449-b281-4b46-8b84-bf73531c54ff" default="2027.a1a7af68-8cdd-42c3-a6bd-5301f76db972" name="cancel request" id="2025.e3fbc36e-7be8-42ed-a6bf-bf0ecf20fd16">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="931" y="211" width="95" height="70" />
                                    
                                    
                                    <ns3:preAssignmentScript />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.687c7107-7d98-4641-89ee-8dcfad8e8956</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.a1a7af68-8cdd-42c3-a6bd-5301f76db972</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.78c6654b-50aa-4a5c-aaad-9f13f7bd698d</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67">tw.local.idcRequest</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.e1f37cfb-9885-410e-90b8-669755b06eea</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.folderId</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.b0b96570-bf1a-4e4c-918a-88cabd4ab475</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.parentFolderPath</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.fa5b66e4-0cee-4527-8fe2-b592fb502b90</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.e3fbc36e-7be8-42ed-a6bf-bf0ecf20fd16" targetRef="9992114f-993b-48aa-bd87-b2ac7f3df53e" name="To End" id="2027.a1a7af68-8cdd-42c3-a6bd-5301f76db972">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.6de48f82-d506-4a36-8f83-b70bf08ccaec" targetRef="9992114f-993b-48aa-bd87-b2ac7f3df53e" name="To End" id="2027.e5384557-e5a5-4ff6-85fc-b86ec47c9360">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42" isCollection="true" name="accountList" id="2056.4698232d-b5eb-4d81-8290-3763ce058272" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67" isCollection="false" name="tmpusedAdvancePayment" id="2056.7675b2ea-6825-40aa-8060-627cd796133f" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="true" name="invalidTabs" id="2056.646c3c20-c9e7-4cf6-825e-f1df12195933" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="validationMessage" id="2056.b28c1b25-e7bd-4cc6-87ad-f065f93d24f2" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="currencyVis" id="2056.1606b9a8-9f69-4a1b-80f7-a3f3e8451c0f">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">"DEFAULT"</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:callActivity calledElement="1.9f0a859b-5010-4ab6-947a-81ad99803cf1" default="2027.b2765b1e-4861-45ee-8280-6fd1cd13212f" name="Database Integration" id="2025.eaa2d5e4-652f-4c51-82fd-df5a42daaa0a">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="821" y="99" width="95" height="70" />
                                    
                                    
                                    <ns3:postAssignmentScript />
                                    
                                    
                                    <ns3:preAssignmentScript />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.2c590891-71b6-458e-9ee3-f3a3ce0beae8</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.b2765b1e-4861-45ee-8280-6fd1cd13212f</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.a7f76cb6-349a-4e05-84c4-a0307bd1074b</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Compliance"</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.3fafd6ca-c323-45ea-8653-0015f902d198</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.selectedAction</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.79f352fc-0629-430a-87cd-5b10dbdc4454</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">true</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.4144ba7c-0a79-4853-8fd2-6aa9481cd0bb</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67">tw.local.idcRequest</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.d3b53e19-8f75-427c-8e43-fe25518ef721</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.idcRequest.appLog</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.96de5ca4-16df-4f86-8d6d-dc1ae75db0f9</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1">tw.local.idcContract</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.9ad81a8c-1b47-4f45-81b1-c0c0120c97d1</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67">tw.local.idcRequest</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.e92b92ce-41f1-49d7-82cf-acf4e366b0e0</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.b08a2cbe-96ec-4d2d-8efe-ec915a097b44</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.idcRequest.appLog</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.eaa2d5e4-652f-4c51-82fd-df5a42daaa0a" targetRef="2025.6de48f82-d506-4a36-8f83-b70bf08ccaec" name="To Exclusive Gateway" id="2027.b2765b1e-4861-45ee-8280-6fd1cd13212f">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" name="error" id="2056.361d283c-6280-494c-84b3-2b208d111256" />
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.daef80d9-0e92-4f47-8070-56a8b9e54c6e" name="Handling Error" id="2025.a10bd8f4-3955-4392-83d0-95775ddf568e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="680" y="335" width="95" height="70" color="#FF7782" />
                                    
                                    
                                    <ns3:preAssignmentScript>alert("pre error "+tw.local.idcContract.facilities.length);</ns3:preAssignmentScript>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.defca81c-add7-4f05-8a3b-784f0277d901</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.582987a0-a4a3-45fd-828a-827cb45ab5a4</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.daef80d9-0e92-4f47-8070-56a8b9e54c6e</ns16:outgoing>
                                
                                
                                <ns16:script>tw.local.errorMSG = String(tw.error.data);&#xD;
tw.local.errorVIS = "EDITABLE";</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:boundaryEvent cancelActivity="true" attachedToRef="2025.e3fbc36e-7be8-42ed-a6bf-bf0ecf20fd16" parallelMultiple="false" name="Error" id="2025.7bab5193-44f4-49ef-89aa-a7edaf67a844">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="966" y="269" width="24" height="24" />
                                    
                                    
                                    <ns3:default>2027.defca81c-add7-4f05-8a3b-784f0277d901</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.defca81c-add7-4f05-8a3b-784f0277d901</ns16:outgoing>
                                
                                
                                <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2025.7179102f-6d6f-4766-85b8-1713d0831e50" />
                                
                                
                                <ns16:outputSet />
                                
                                
                                <ns16:errorEventDefinition>
                                    
                                    
                                    <ns16:extensionElements>
                                        
                                        
                                        <ns4:errorEventSettings>
                                            
                                            
                                            <ns4:catchAll>true</ns4:catchAll>
                                            
                                        
                                        </ns4:errorEventSettings>
                                        
                                    
                                    </ns16:extensionElements>
                                    
                                
                                </ns16:errorEventDefinition>
                                
                            
                            </ns16:boundaryEvent>
                            
                            
                            <ns16:boundaryEvent cancelActivity="true" attachedToRef="2025.eaa2d5e4-652f-4c51-82fd-df5a42daaa0a" parallelMultiple="false" name="Error 1" id="2025.39241282-0b3a-4d38-8991-a91780160465">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="856" y="157" width="24" height="24" />
                                    
                                    
                                    <ns3:default>2027.582987a0-a4a3-45fd-828a-827cb45ab5a4</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.582987a0-a4a3-45fd-828a-827cb45ab5a4</ns16:outgoing>
                                
                                
                                <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2025.c8ebc3e2-5f15-422f-8907-b89dc0f23e8d" />
                                
                                
                                <ns16:outputSet />
                                
                                
                                <ns16:errorEventDefinition>
                                    
                                    
                                    <ns16:extensionElements>
                                        
                                        
                                        <ns4:errorEventSettings>
                                            
                                            
                                            <ns4:catchAll>true</ns4:catchAll>
                                            
                                        
                                        </ns4:errorEventSettings>
                                        
                                    
                                    </ns16:extensionElements>
                                    
                                
                                </ns16:errorEventDefinition>
                                
                            
                            </ns16:boundaryEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.7bab5193-44f4-49ef-89aa-a7edaf67a844" targetRef="2025.a10bd8f4-3955-4392-83d0-95775ddf568e" name="To Handling Error" id="2027.defca81c-add7-4f05-8a3b-784f0277d901">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.39241282-0b3a-4d38-8991-a91780160465" targetRef="2025.a10bd8f4-3955-4392-83d0-95775ddf568e" name="To Handling Error" id="2027.582987a0-a4a3-45fd-828a-827cb45ab5a4">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.eccce85a-e67e-4d7e-8c6a-5b6dace4f3ed" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorVIS" id="2056.030d1f86-d5bc-454c-8755-f123fa8cfed0" />
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.a10bd8f4-3955-4392-83d0-95775ddf568e" targetRef="2025.674068e6-9658-41eb-b7c7-f9ebe6ace86e" name="To Review IDC Request by Branch Compliance Rep " id="2027.daef80d9-0e92-4f47-8070-56a8b9e54c6e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>leftCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>rightBottom</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="alertMessage" id="2056.40b004b1-570c-45ff-8442-0c8737a9ee69" />
                            
                            
                            <ns3:htmlHeaderTag id="38bad2a0-4680-421a-b988-bce3f08f0c13">
                                
                                
                                <ns3:tagName>viewport</ns3:tagName>
                                
                                
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                
                                
                                <ns3:enabled>true</ns3:enabled>
                                
                            
                            </ns3:htmlHeaderTag>
                            
                        
                        </ns3:userTaskImplementation>
                        
                        
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.02818ba4-c183-4dfb-8924-18e2d9a515dd" epvProcessLinkId="aee02dab-f257-48d5-8e2e-2ba534e01291" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.8ce8b34e-54bb-4623-a4c9-ab892efacac6" epvProcessLinkId="c3097059-ecc3-444e-8577-841551bb6130" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e" epvProcessLinkId="6345e77d-e047-4784-8bd0-d931d4ec3e5e" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.e5829eee-0ab1-4f47-9191-f0f8705bc33e" epvProcessLinkId="95d20f21-f4b5-4497-84b2-a8cbe63d482c" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.8c22e68f-ffd6-4c68-95c5-85ce6c0d3ffa">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">var autoObject = {};
autoObject.IDCRequestState = "";
autoObject.commodityDescription = "";
autoObject.countryOfOrigin = {};
autoObject.countryOfOrigin.id = 0;
autoObject.countryOfOrigin.code = "";
autoObject.countryOfOrigin.arabicdescription = "";
autoObject.countryOfOrigin.englishdescription = "";
autoObject.appInfo = {};
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = {};
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.productsDetails = {};
autoObject.productsDetails.destinationPort = "";
autoObject.productsDetails.shippingDate = new Date();
autoObject.productsDetails.HSProduct = {};
autoObject.productsDetails.HSProduct.id = 0;
autoObject.productsDetails.HSProduct.code = "";
autoObject.productsDetails.HSProduct.arabicdescription = "";
autoObject.productsDetails.HSProduct.englishdescription = "";
autoObject.productsDetails.incoterms = {};
autoObject.productsDetails.incoterms.id = 0;
autoObject.productsDetails.incoterms.code = "";
autoObject.productsDetails.incoterms.arabicdescription = "";
autoObject.productsDetails.incoterms.englishdescription = "";
autoObject.productsDetails.ACID = "";
autoObject.productsDetails.CBECommodityClassification = {};
autoObject.productsDetails.CBECommodityClassification.id = 0;
autoObject.productsDetails.CBECommodityClassification.code = "";
autoObject.productsDetails.CBECommodityClassification.arabicdescription = "";
autoObject.productsDetails.CBECommodityClassification.englishdescription = "";
autoObject.productsDetails.shipmentMethod = {};
autoObject.productsDetails.shipmentMethod.id = 0;
autoObject.productsDetails.shipmentMethod.code = "";
autoObject.productsDetails.shipmentMethod.arabicdescription = "";
autoObject.productsDetails.shipmentMethod.englishdescription = "";
autoObject.financialDetails = {};
autoObject.financialDetails.isAdvancePaymentsUsed = false;
autoObject.financialDetails.paymentTerms = [];
autoObject.financialDetails.paymentTerms[0] = {};
autoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;
autoObject.financialDetails.paymentTerms[0].installmentDate = new Date();
autoObject.financialDetails.usedAdvancePayment = [];
autoObject.financialDetails.usedAdvancePayment[0] = {};
autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = {};
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = "";
autoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;
autoObject.financialDetails.discountAmt = 0.0;
autoObject.financialDetails.firstInstallementMaturityDate = new Date();
autoObject.financialDetails.facilityAmtInDocCurrency = 0.0;
autoObject.financialDetails.amtSight = 0.0;
autoObject.financialDetails.beneficiaryDetails = {};
autoObject.financialDetails.beneficiaryDetails.account = "";
autoObject.financialDetails.beneficiaryDetails.bank = "";
autoObject.financialDetails.beneficiaryDetails.correspondentRefNum = "";
autoObject.financialDetails.beneficiaryDetails.name = "";
autoObject.financialDetails.beneficiaryDetails.country = {};
autoObject.financialDetails.beneficiaryDetails.country.id = 0;
autoObject.financialDetails.beneficiaryDetails.country.code = "";
autoObject.financialDetails.beneficiaryDetails.country.arabicdescription = "";
autoObject.financialDetails.beneficiaryDetails.country.englishdescription = "";
autoObject.financialDetails.amtDeferredNoAvalized = 0.0;
autoObject.financialDetails.amtPayableByNBE = 0.0;
autoObject.financialDetails.executionHub = {};
autoObject.financialDetails.executionHub.id = 0;
autoObject.financialDetails.executionHub.code = "";
autoObject.financialDetails.executionHub.arabicdescription = "";
autoObject.financialDetails.executionHub.englishdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency = {};
autoObject.financialDetails.sourceOfForeignCurrency.id = 0;
autoObject.financialDetails.sourceOfForeignCurrency.code = "";
autoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency.englishdescription = "";
autoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;
autoObject.financialDetails.documentCurrency = {};
autoObject.financialDetails.documentCurrency.id = 0;
autoObject.financialDetails.documentCurrency.code = "";
autoObject.financialDetails.documentCurrency.arabicdescription = "";
autoObject.financialDetails.documentCurrency.englishdescription = "";
autoObject.financialDetails.daysTillMaturity = 0;
autoObject.financialDetails.tradeFinanceApprovalNumber = "";
autoObject.financialDetails.amountAdvanced = 0.0;
autoObject.financialDetails.paymentAccount = "";
autoObject.financialDetails.tradeFOReferenceNumber = "";
autoObject.financialDetails.documentAmount = 0.0;
autoObject.financialDetails.sourceOfFunds = {};
autoObject.financialDetails.sourceOfFunds.id = 0;
autoObject.financialDetails.sourceOfFunds.code = "";
autoObject.financialDetails.sourceOfFunds.arabicdescription = "";
autoObject.financialDetails.sourceOfFunds.englishdescription = "";
autoObject.financialDetails.chargesAccount = "";
autoObject.financialDetails.CashAmtWithNoCurrency = 0.0;
autoObject.financialDetails.amtDeferredAvalized = 0.0;
autoObject.financialDetails.amtPaidbyOtherBanks = 0.0;
autoObject.financialDetails.cashAmtInDocCurrency = 0.0;
autoObject.IDCRequestType = {};
autoObject.IDCRequestType.id = 0;
autoObject.IDCRequestType.code = "";
autoObject.IDCRequestType.arabicdescription = "";
autoObject.IDCRequestType.englishdescription = "";
autoObject.isIDCWithdrawn = false;
autoObject.IDCRequestStage = "";
autoObject.FCContractNumber = "";
autoObject.billOfLading = [];
autoObject.billOfLading[0] = {};
autoObject.billOfLading[0].date = new Date();
autoObject.billOfLading[0].number = "";
autoObject.importPurpose = {};
autoObject.importPurpose.id = 0;
autoObject.importPurpose.code = "";
autoObject.importPurpose.arabicdescription = "";
autoObject.importPurpose.englishdescription = "";
autoObject.IDCRequestNature = {};
autoObject.IDCRequestNature.id = 0;
autoObject.IDCRequestNature.code = "";
autoObject.IDCRequestNature.arabicdescription = "";
autoObject.IDCRequestNature.englishdescription = "";
autoObject.customerInformation = {};
autoObject.customerInformation.CIFNumber = "";
autoObject.customerInformation.importCardNumber = "";
autoObject.customerInformation.commercialRegistrationNumber = "";
autoObject.customerInformation.customerName = "";
autoObject.customerInformation.isCustomeSanctionedbyCBE = false;
autoObject.customerInformation.CBENumber = "";
autoObject.customerInformation.customerSector = "";
autoObject.customerInformation.facilityType = "";
autoObject.customerInformation.commercialRegistrationOffice = "";
autoObject.customerInformation.taxCardNumber = "";
autoObject.customerInformation.customerType = "";
autoObject.invoices = [];
autoObject.invoices[0] = {};
autoObject.invoices[0].date = new Date();
autoObject.invoices[0].number = "";
autoObject.productCategory = {};
autoObject.productCategory.id = 0;
autoObject.productCategory.code = "";
autoObject.productCategory.arabicdescription = "";
autoObject.productCategory.englishdescription = "";
autoObject.documentsSource = {};
autoObject.documentsSource.id = 0;
autoObject.documentsSource.code = "";
autoObject.documentsSource.arabicdescription = "";
autoObject.documentsSource.englishdescription = "";
autoObject.ParentIDCRequestNumber = "";
autoObject.paymentTerms = {};
autoObject.paymentTerms.id = 0;
autoObject.paymentTerms.code = "";
autoObject.paymentTerms.arabicdescription = "";
autoObject.paymentTerms.englishdescription = "";
autoObject.approvals = {};
autoObject.approvals.CAD = false;
autoObject.approvals.treasury = false;
autoObject.approvals.compliance = false;
autoObject.appLog = [];
autoObject.appLog[0] = {};
autoObject.appLog[0].startTime = new Date();
autoObject.appLog[0].endTime = new Date();
autoObject.appLog[0].userName = "";
autoObject.appLog[0].role = "";
autoObject.appLog[0].step = "";
autoObject.appLog[0].action = "";
autoObject.appLog[0].comment = "";
autoObject.appLog[0].terminateReason = "";
autoObject.appLog[0].returnReason = "";
autoObject.stepLog = {};
autoObject.stepLog.startTime = new Date();
autoObject.stepLog.endTime = new Date();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="attachment" itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" id="2055.7ab26f1f-ad2f-4e75-ba8f-fe0f1eceef6a" />
                        
                        
                        <ns16:dataInput name="ECMproperties" itemSubjectRef="itm.12.ae22157e-8e19-4a60-a294-712ff5dc96df" isCollection="false" id="2055.ac26554a-eb1e-4e5f-8f4a-e579642f937c" />
                        
                        
                        <ns16:dataInput name="folderId" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.68399116-bb74-42ed-81db-c91a5974a390" />
                        
                        
                        <ns16:dataInput name="parentFolderPath" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.4491d16f-b059-4d50-92d3-4f7afb6b188e" />
                        
                        
                        <ns16:dataInput name="idcContract" itemSubjectRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1" isCollection="false" id="2055.8c31e0c6-583d-4ab8-8d2b-823252fe938f" />
                        
                        
                        <ns16:dataOutput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.1ced5e6a-3105-4bab-a14d-b6dcc7ee828a" />
                        
                        
                        <ns16:dataOutput name="attachment" itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" id="2055.a1d974ed-0f69-49c0-b688-d2bc04f25f43" />
                        
                        
                        <ns16:dataOutput name="idcContract" itemSubjectRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1" isCollection="false" id="2055.38c836fb-47a2-4046-8a24-dfcacf35b225" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                
                </ns16:globalUserTask>
                
            
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.b1f215cb-d2b4-40cd-a032-8fd2b36c9404</processLinkId>
            <processId>1.fcc27b9d-793f-4d79-b8ed-d17e5877201f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.4fe8f3b9-6936-420f-881f-57ff725e8573</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.34a162e5-7f0d-457b-b7a5-6f5e6821acc4</toProcessItemId>
            <guid>709dfe3a-dc9b-48ce-b67c-9e3e40b3eacb</guid>
            <versionId>25553d7d-54b4-4be0-93b8-bb4186f0100b</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.4fe8f3b9-6936-420f-881f-57ff725e8573</fromProcessItemId>
            <toProcessItemId>2025.34a162e5-7f0d-457b-b7a5-6f5e6821acc4</toProcessItemId>
        </link>
    </process>
</teamworks>

