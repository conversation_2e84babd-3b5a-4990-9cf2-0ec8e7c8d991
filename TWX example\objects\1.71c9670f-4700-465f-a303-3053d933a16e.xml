<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.71c9670f-4700-465f-a303-3053d933a16e" name="Get Applied Charges">
        <lastModified>1689576389871</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.71c9670f-4700-465f-a303-3053d933a16e</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.cd288e3a-34c7-4520-9593-f984fe0a05cf</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>38269e1d-4e19-44d2-b884-517f6bea1ab6</guid>
        <versionId>61c8ad15-3252-4361-8e7e-221b85be1614</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:2e93acfacc1269a3:3c8702fd:189609f03e7:-198b" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.eca4de6a-54e2-41e9-847f-28d7c8f1b58e"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"a5bfc600-565d-441b-a58b-fb9802794859"},{"incoming":["4e9ba234-68db-4c29-87d8-df431e3b0fe2"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5ae0"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"166b2b3d-b507-4c3e-8d38-0f202a377430"},{"targetRef":"cd288e3a-34c7-4520-9593-f984fe0a05cf","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Linked Service Flow","declaredType":"sequenceFlow","id":"2027.eca4de6a-54e2-41e9-847f-28d7c8f1b58e","sourceRef":"a5bfc600-565d-441b-a58b-fb9802794859"},{"startQuantity":1,"outgoing":["4e9ba234-68db-4c29-87d8-df431e3b0fe2"],"incoming":["2027.eca4de6a-54e2-41e9-847f-28d7c8f1b58e"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":193,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Linked Service Flow","dataInputAssociation":[{"targetRef":"2055.14265890-c52a-4d39-8c9f-d75b6ac76ba6","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.userID"]}}]},{"targetRef":"2055.274c7620-28df-4c70-8530-e935d5491cd1","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.transactionToken"]}}]},{"targetRef":"2055.0b8de2d5-0b2a-4763-8ee6-c3fc2ce831b2","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.event"]}}]},{"targetRef":"2055.7bbc2788-10b1-4788-8e16-d7ea34195175","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.local.data"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"cd288e3a-34c7-4520-9593-f984fe0a05cf","calledElement":"1.510b8c22-c137-4e39-baf6-e66105ef0be6"},{"targetRef":"166b2b3d-b507-4c3e-8d38-0f202a377430","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:45353b16cdfc415c:-1fa23e4a:188b088e34d:-5dcb"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"4e9ba234-68db-4c29-87d8-df431e3b0fe2","sourceRef":"cd288e3a-34c7-4520-9593-f984fe0a05cf"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"event","isCollection":false,"declaredType":"dataObject","id":"2056.efe244c2-cc73-444d-809a-9f2609a444ea"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"transactionToken","isCollection":false,"declaredType":"dataObject","id":"2056.ae4ef42e-6838-499f-8338-f04a2a8fed43"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"userID","isCollection":false,"declaredType":"dataObject","id":"2056.18720da3-80af-4b64-8392-d316eb7afaf0"}],"laneSet":[{"id":"6384c030-5920-4542-96ea-ef283e86cd04","lane":[{"flowNodeRef":["a5bfc600-565d-441b-a58b-fb9802794859","166b2b3d-b507-4c3e-8d38-0f202a377430","cd288e3a-34c7-4520-9593-f984fe0a05cf"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"c686c073-57ba-45af-be36-80632c47c885","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get Applied Charges","declaredType":"process","id":"1.71c9670f-4700-465f-a303-3053d933a16e","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.e2d6ac5b-c06f-408d-ad96-b9ba6d8ca8f5"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.33cc42c0-4286-4d6e-a79e-ac25d7506dc1"}],"inputSet":[{}],"outputSet":[{}],"dataInput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.d39413b8-c768-4d37-a72b-cd98c2637fad"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d39413b8-c768-4d37-a72b-cd98c2637fad</processParameterId>
            <processId>1.71c9670f-4700-465f-a303-3053d933a16e</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>db186ff0-f0e9-46c5-b2c6-e3c0b52d72f2</guid>
            <versionId>78159f02-2409-4a49-b15b-bcd67872f69f</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.e2d6ac5b-c06f-408d-ad96-b9ba6d8ca8f5</processParameterId>
            <processId>1.71c9670f-4700-465f-a303-3053d933a16e</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>1d0350db-7673-4fd3-b940-16f600f568d2</guid>
            <versionId>132216a5-e2fa-4639-8cef-d381fec23a17</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.33cc42c0-4286-4d6e-a79e-ac25d7506dc1</processParameterId>
            <processId>1.71c9670f-4700-465f-a303-3053d933a16e</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a7aa7a72-4c00-41a3-9861-414c5ce9201f</guid>
            <versionId>5fda0479-2ffc-4c0a-9ed7-1711e755cb32</versionId>
        </processParameter>
        <processVariable name="event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.efe244c2-cc73-444d-809a-9f2609a444ea</processVariableId>
            <description isNull="true" />
            <processId>1.71c9670f-4700-465f-a303-3053d933a16e</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>14289417-b832-447d-8b27-0c1ae7b9b526</guid>
            <versionId>0341fbcd-a8fd-4315-a035-d75c9b4b25b1</versionId>
        </processVariable>
        <processVariable name="transactionToken">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ae4ef42e-6838-499f-8338-f04a2a8fed43</processVariableId>
            <description isNull="true" />
            <processId>1.71c9670f-4700-465f-a303-3053d933a16e</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>611b7af5-8114-483e-ab43-7d3785abb9fc</guid>
            <versionId>e9d21478-8c06-43a5-8498-de53a41edbf5</versionId>
        </processVariable>
        <processVariable name="userID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.18720da3-80af-4b64-8392-d316eb7afaf0</processVariableId>
            <description isNull="true" />
            <processId>1.71c9670f-4700-465f-a303-3053d933a16e</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>31628a90-b32a-4c56-b139-8f27de269060</guid>
            <versionId>e228c771-6b29-461b-9420-8b3a69669443</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.166b2b3d-b507-4c3e-8d38-0f202a377430</processItemId>
            <processId>1.71c9670f-4700-465f-a303-3053d933a16e</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.01ca653e-e82c-4000-b500-1be324745e26</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5ae0</guid>
            <versionId>5844c701-264f-4e20-bd2b-96887177ea99</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.01ca653e-e82c-4000-b500-1be324745e26</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>5f1008e1-eca0-41ce-9dbe-bc4f8ff9a5ff</guid>
                <versionId>14c251a8-47df-4c6b-932c-3f285056aea9</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.cd288e3a-34c7-4520-9593-f984fe0a05cf</processItemId>
            <processId>1.71c9670f-4700-465f-a303-3053d933a16e</processId>
            <name>Linked Service Flow</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.fa84d9e0-4e0f-4ff2-857b-21805d7c9c31</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5ae1</guid>
            <versionId>75986e10-8df4-43d1-b6b9-b9f3a550ee18</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="193" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.fa84d9e0-4e0f-4ff2-857b-21805d7c9c31</subProcessId>
                <attachedProcessRef>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/1.510b8c22-c137-4e39-baf6-e66105ef0be6</attachedProcessRef>
                <guid>88e80cc6-1ae5-4fff-af44-082da6a303b0</guid>
                <versionId>e3fb5c14-49d6-4f1f-8046-2b0527ee68a5</versionId>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.24effd63-07e7-4c76-a4b6-db46742eda3f</parameterMappingId>
                    <processParameterId>2055.50ff3ae2-8020-40ab-8fca-f81e81a849bc</processParameterId>
                    <parameterMappingParentId>3012.fa84d9e0-4e0f-4ff2-857b-21805d7c9c31</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>ee49d042-46fa-4b22-af31-05de6a1d3f67</guid>
                    <versionId>098b40cb-50cb-4efb-8fe1-2fd46a00074b</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.5921cde3-4dc3-45a3-a5fe-f3b005ab44ad</parameterMappingId>
                    <processParameterId>2055.80362a32-09d0-4d3f-8185-67f0895155e2</processParameterId>
                    <parameterMappingParentId>3012.fa84d9e0-4e0f-4ff2-857b-21805d7c9c31</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>f6d063f6-79ef-4880-9fde-5c0c608216ed</guid>
                    <versionId>2b08590d-825f-416f-b544-78bea004c5f8</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6a68f3b2-27f1-41a1-abe2-ad7bfba6e4d7</parameterMappingId>
                    <processParameterId>2055.14265890-c52a-4d39-8c9f-d75b6ac76ba6</processParameterId>
                    <parameterMappingParentId>3012.fa84d9e0-4e0f-4ff2-857b-21805d7c9c31</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.userID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>ad176ce3-03d9-4ba8-b812-97fcfe670703</guid>
                    <versionId>31199491-030f-4b27-8e91-a265c1e01ddc</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.dc952e83-af1a-4e83-9533-3387407dab62</parameterMappingId>
                    <processParameterId>2055.d72a05cb-ce36-4fe0-854a-3c5812660dd2</processParameterId>
                    <parameterMappingParentId>3012.fa84d9e0-4e0f-4ff2-857b-21805d7c9c31</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>54fc1804-1865-4ede-9640-e854f7227052</guid>
                    <versionId>414499b7-04dc-4286-84e7-01e6e1f2365c</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="transactionToken">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.22b71d79-11c7-47a2-b3d7-2572ae80c0ca</parameterMappingId>
                    <processParameterId>2055.274c7620-28df-4c70-8530-e935d5491cd1</processParameterId>
                    <parameterMappingParentId>3012.fa84d9e0-4e0f-4ff2-857b-21805d7c9c31</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.transactionToken</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>1a1f91a0-b63c-4d1e-92bb-310d767100ca</guid>
                    <versionId>6e56c8db-1801-4375-ab5c-90a953dc3cf8</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="event">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.56cc5ce5-347a-4da6-adc5-80e12507a607</parameterMappingId>
                    <processParameterId>2055.0b8de2d5-0b2a-4763-8ee6-c3fc2ce831b2</processParameterId>
                    <parameterMappingParentId>3012.fa84d9e0-4e0f-4ff2-857b-21805d7c9c31</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.event</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>2cee7821-5ea5-43f3-bd4a-f75f14ddf8bb</guid>
                    <versionId>777a3558-e7db-4fdf-8343-b71f648c25a8</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="commissionAndInterestAndChargesList">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b0957518-8c0a-488b-846d-d34af4e8e7d1</parameterMappingId>
                    <processParameterId>2055.2ffaf1e9-7f1f-477c-88e1-a490e3189109</processParameterId>
                    <parameterMappingParentId>3012.fa84d9e0-4e0f-4ff2-857b-21805d7c9c31</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>/12.f88e06db-6be1-4cbe-814e-e6401b35e875</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>029226a4-9f25-4d5e-aedd-80d6fa5f972d</guid>
                    <versionId>987156b3-a621-4248-9be6-93be679f74e9</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="xmlRequest">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3702b831-89bd-4aef-b139-8811fa1d5dad</parameterMappingId>
                    <processParameterId>2055.ea39b1a7-83ea-4850-8b5b-3de57a2a92c5</processParameterId>
                    <parameterMappingParentId>3012.fa84d9e0-4e0f-4ff2-857b-21805d7c9c31</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>cb405514-15ae-4dfa-b8cb-c52bb22b67ed</guid>
                    <versionId>c10e2c34-2253-4c65-8de9-72dcce6d96e5</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="productCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f34aa3f0-1647-4a89-9b30-b4100f375db6</parameterMappingId>
                    <processParameterId>2055.7bbc2788-10b1-4788-8e16-d7ea34195175</processParameterId>
                    <parameterMappingParentId>3012.fa84d9e0-4e0f-4ff2-857b-21805d7c9c31</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.data</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>73baab57-901a-4ea3-a35b-5c44cf9bac51</guid>
                    <versionId>d3e5235d-2f3a-47a7-9f19-731a8014ab93</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="xmlResponse">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b62845d6-99b6-4a37-8e69-335fd64cb29e</parameterMappingId>
                    <processParameterId>2055.a258da75-90f1-461f-8df4-abbe6beb5a2d</processParameterId>
                    <parameterMappingParentId>3012.fa84d9e0-4e0f-4ff2-857b-21805d7c9c31</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>4753dd49-7d8f-4ba3-b00b-a4779976fb46</guid>
                    <versionId>e48c8ddb-a17c-478e-9d63-baa207293ceb</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.cd288e3a-34c7-4520-9593-f984fe0a05cf</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get Applied Charges" id="1.71c9670f-4700-465f-a303-3053d933a16e" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.d39413b8-c768-4d37-a72b-cd98c2637fad" />
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.e2d6ac5b-c06f-408d-ad96-b9ba6d8ca8f5" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.33cc42c0-4286-4d6e-a79e-ac25d7506dc1" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="6384c030-5920-4542-96ea-ef283e86cd04">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="c686c073-57ba-45af-be36-80632c47c885" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>a5bfc600-565d-441b-a58b-fb9802794859</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>166b2b3d-b507-4c3e-8d38-0f202a377430</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>cd288e3a-34c7-4520-9593-f984fe0a05cf</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="a5bfc600-565d-441b-a58b-fb9802794859">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.eca4de6a-54e2-41e9-847f-28d7c8f1b58e</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="166b2b3d-b507-4c3e-8d38-0f202a377430">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-5ae0</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>4e9ba234-68db-4c29-87d8-df431e3b0fe2</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="a5bfc600-565d-441b-a58b-fb9802794859" targetRef="cd288e3a-34c7-4520-9593-f984fe0a05cf" name="To Linked Service Flow" id="2027.eca4de6a-54e2-41e9-847f-28d7c8f1b58e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.510b8c22-c137-4e39-baf6-e66105ef0be6" name="Linked Service Flow" id="cd288e3a-34c7-4520-9593-f984fe0a05cf">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="193" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.eca4de6a-54e2-41e9-847f-28d7c8f1b58e</ns16:incoming>
                        
                        
                        <ns16:outgoing>4e9ba234-68db-4c29-87d8-df431e3b0fe2</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.14265890-c52a-4d39-8c9f-d75b6ac76ba6</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.userID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.274c7620-28df-4c70-8530-e935d5491cd1</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.transactionToken</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.0b8de2d5-0b2a-4763-8ee6-c3fc2ce831b2</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.event</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.7bbc2788-10b1-4788-8e16-d7ea34195175</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.data</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="cd288e3a-34c7-4520-9593-f984fe0a05cf" targetRef="166b2b3d-b507-4c3e-8d38-0f202a377430" name="To End" id="4e9ba234-68db-4c29-87d8-df431e3b0fe2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:45353b16cdfc415c:-1fa23e4a:188b088e34d:-5dcb</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="event" id="2056.efe244c2-cc73-444d-809a-9f2609a444ea" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="transactionToken" id="2056.ae4ef42e-6838-499f-8338-f04a2a8fed43" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="userID" id="2056.18720da3-80af-4b64-8392-d316eb7afaf0" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.4e9ba234-68db-4c29-87d8-df431e3b0fe2</processLinkId>
            <processId>1.71c9670f-4700-465f-a303-3053d933a16e</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.cd288e3a-34c7-4520-9593-f984fe0a05cf</fromProcessItemId>
            <endStateId>guid:45353b16cdfc415c:-1fa23e4a:188b088e34d:-5dcb</endStateId>
            <toProcessItemId>2025.166b2b3d-b507-4c3e-8d38-0f202a377430</toProcessItemId>
            <guid>f3bceebe-d723-4e5a-9c2f-173aa9502cd3</guid>
            <versionId>7a2f6469-eb8d-43ca-b277-e29c850bc90f</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.cd288e3a-34c7-4520-9593-f984fe0a05cf</fromProcessItemId>
            <toProcessItemId>2025.166b2b3d-b507-4c3e-8d38-0f202a377430</toProcessItemId>
        </link>
    </process>
</teamworks>

