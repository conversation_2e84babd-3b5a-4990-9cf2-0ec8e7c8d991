<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.ab2ea11b-7c5e-4835-9ed4-18708eee21be" name="Advance Payments Used">
        <lastModified>1692725846181</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <coachViewId>64.ab2ea11b-7c5e-4835-9ed4-18708eee21be</coachViewId>
        <isTemplate>false</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;159c3fe5-52ac-4cf2-8ab0-b69b8c0e99a8&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Horizontal_Layout6&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3f278573-3336-4300-88be-30a97b1eb821&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Horizontal layout 6&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9ff31178-5650-4d65-8d4f-834923743030&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f46ad174-362b-4bc2-8b03-623260852f1b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c3dfa6ed-3e03-41c0-8cb2-7622279f19ea&lt;/ns2:id&gt;&lt;ns2:optionName&gt;layoutFlow&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"V"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c0bfd417-bc50-4ab6-8992-734a00b158ca&lt;/ns2:id&gt;&lt;ns2:optionName&gt;hAlignment&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"J"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7f5c531c-6a28-44db-8f73-aaf4cda8d991&lt;/ns2:id&gt;&lt;ns2:optionName&gt;width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;89422f99-8f79-4e76-8222-6368a9d8fb29&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@overflow&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"visible"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;180eabc2-ece7-4128-8ecc-e9d4c14dfc66&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;92aedefd-c640-4ef5-8a6d-91dd6cf95221&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Advance_Payments_Used&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4babb13a-3f3a-4430-8a01-a9bdffeb4ed5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Advance Payments Used&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a5cfc3e0-6ce9-4c6d-8d71-a7c899b677b0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;75b33b82-5cc0-4b61-8616-aabe37c7943a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7226e8c9-1269-4995-844f-818859c85ddf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6f8880f8-c534-4d08-8a2d-7b8196e05841&lt;/ns2:id&gt;&lt;ns2:optionName&gt;width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0abf62ab-05ff-47e6-8047-5e3e8021b32b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorfulBody&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2021fd47-22c7-40ac-8b7a-997375ef37d8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8c2809da-b04f-4b43-8fe9-5847300e113f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.checkerVis();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;&lt;/ns2:binding&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;3924bbf7-f351-4e1c-8f55-38eae7b04f57&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;1d963953-d1b3-4201-8a95-940d4db47ead&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;retrive_data&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bf5f8270-c61d-4a69-8726-851c1810bc52&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;retrive data layout &lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3d59f7a4-27ca-4205-8645-20f9a924fdd1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f34dee78-59cf-4007-86a3-e958c41b3d36&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8ad0d5f9-d74f-43a0-8d6c-1d2154842e9d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;layoutFlow&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"P"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3629355f-dfb3-4d7a-8ee0-b54e6bf5cbf5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;hAlignment&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"L"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;f6d923c6-85f4-4c21-87bd-7809982ec4d4&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;f5aa6f51-393b-4909-8e93-8764cecc5b12&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;84e1e469-f93d-4366-8d1b-b341beab0735&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6ac5ca12-e1b7-466b-8af0-23c328823b47&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6d07e25e-c412-4703-867c-fbbe639409a3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;89de8eb2-5358-4eda-8e8b-4228dd7c3a81&lt;/ns2:id&gt;&lt;ns2:optionName&gt;layoutFlow&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"V"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;0631a973-3239-4afa-8d4e-6234c270a8ab&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;ab2c4fe8-c161-4a70-8496-b5bd39c55c98&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;AdvancePaymentRequestNumber&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b020d53d-3980-401d-8b8b-8b1f7ae7b1ab&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9343b83c-af90-480a-847f-c01187787239&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.AdvancePaymentRequestNumber&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c0d61ee7-5c64-4037-8bf0-d96eeb683939&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;57509d2f-ce3c-48e4-84be-b09f429a13c3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;203d488b-7a3c-4cea-806b-b334d3065cd2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"D"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5bf61182-83f2-4407-8fc2-a98f3288915d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d4d43a3a-abc9-4e4b-897c-f1ec9401a16d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//view.resetAdvancePayment();
if(me.getData() != "" &amp;amp;&amp;amp; me.getData() != null &amp;amp;&amp;amp; me.getData() != undefined){
${Retrieve}.setEnabled(true);
}else{
	${Retrieve}.setEnabled(false);
}
//${addbutton}.setEnabled(false);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;03f9304e-336b-4791-84f2-5b68b28b100a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.tmpUsedAdvancePayment.advancePaymentRequestNumber&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;3d8da1b9-0b32-404b-8ed6-c350d2e6ca8e&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0d763082-dfeb-4e00-818f-cc4668327e8c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;*************-47fd-8418-fe05a284604b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f42be985-56f7-4000-80bb-356469582e5e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;b1010b49-5b5e-44c9-8a44-0a602c47d55a&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;ac89ffc8-6aca-4591-8834-c49cea3f1c11&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Output_Text1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e754970a-2f1a-4844-8718-5fbe6dc82eab&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt; &lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;aa51d1a1-7b7a-4579-83f5-f645686c9bc1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;18b48106-6b58-4437-871e-44e98cb5dfc1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;93bd5a0d-00e1-4ef9-81ac-72a59e4ac6be&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"M"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;60f08329-5358-452e-88af-453686e02efa&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"EDITABLE"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.f634f22e-7800-4bd7-9f1e-87177acfb3bc&lt;/ns2:viewUUID&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;ea5a5b0f-38d6-497a-87ea-e00a3e7897a2&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Retrieve&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d8ef9862-ba39-42b9-81b6-91460d3ebaed&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.Retrieve&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;13a78fbe-da18-4b89-8e26-afefaf40b096&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a6b9106f-298d-4c40-8db5-6c05eaaaee9d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;92ba7f5c-f0d8-4eee-83f5-4932c984bb99&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e15d4736-ce39-43cd-878c-5231130327d5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"D"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;12df670f-cd07-4a5a-8e2e-e580f6abf34f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;81632702-7b28-4b24-8e85-9949f80173bb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;shapeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;R&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5512fbb3-3e8c-4b96-89cf-9fc40255e76a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CLICK&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//${retrieveAdvancePayment}.execute(${AdvancePaymentRequestNumber}.getData());
view.getAdvancePayment();
//${retrieveAdvancePayment}.execute('5-CIF-22');
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;041147df-64db-4634-8a5c-d055c4f59fd4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;me.setEnabled(false);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ad3328cf-492b-492f-8a14-bafc29a36df5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;preventMultipleClicks&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;c0fb3edf-00d0-4d06-828f-68e5b0a36e05&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;retrieveAdvancePayment&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ba633570-1da1-462e-8731-04b3420196b1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;retrieve advance payment&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cfd496f5-36c9-4776-8a94-82472c28c780&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6d605937-5acb-4437-87c9-9f20dcfc7a43&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a4eee2b7-4c51-425e-8bee-3626fb871e98&lt;/ns2:id&gt;&lt;ns2:optionName&gt;attachedService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.1d264c59-eafd-4f05-a029-411f466237ce&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1f723ee7-8702-48fa-898d-d5a3e7a6e852&lt;/ns2:id&gt;&lt;ns2:optionName&gt;busyIndicator&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;02a1ce2d-aaeb-4b9c-8c5b-fb68c2efe4e8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.AjaxErrorHandling(error.errorText);
//${AdvancePaymentRequestNumber}.setValid(false,"can not retrive this advance payment");&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fb5e2c52-291b-4bdd-8a28-5bf039491474&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCRESULT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(me.getData().advancePaymentRequestNumber==null || me.getData().advancePaymentRequestNumber== undefined || me.getData().advancePaymentRequestNumber== ""){
	${AdvancePaymentRequestNumber}.setValid(false,"can not retrive this advance payment");
}else{
	${AdvancePaymentRequestNumber}.setValid(true);
}
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.tmpUsedAdvancePayment&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;5db46687-399a-4481-8ed4-6ef5fc66fdb6&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Horizontal_Layout3&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d6fc8e68-40fc-46d4-8d20-4c02648f00fe&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Horizontal layout 3&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1b33f3c5-0e92-46e2-842b-d9c346a17578&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6bdf91b2-5197-48f9-856e-637e8c7c96b1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fe2e958a-de9b-4a3d-867d-44ed773dc3ff&lt;/ns2:id&gt;&lt;ns2:optionName&gt;hAlignment&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"J"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;ace83a19-dcdb-484d-8786-bbefa4cbafff&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;0f21d8f2-3106-4ca1-8c08-3da4d2e1921f&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout3&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b9d1870e-98f3-43e7-8def-393973ca2ead&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 3&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fc91b865-f3c7-4ef9-8004-cd7e1383caa0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;670a3d89-3cdd-4490-86a4-6b5a56fb1339&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;0a507e70-5fc0-4b85-8cd6-ab322bbf7971&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;b5051120-0dd1-47fc-8a96-7804860a2c62&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;BeneficiaryName&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1a7da423-0ed5-4602-a9f5-f4d731328e6b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9a04374d-8268-453c-8616-de7c3960df3a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.BeneficiaryName&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cc7e1fa6-9e2a-46d6-847f-d06c7c26bf0c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6a79d4e4-1ed9-4e11-8d65-3ab3b1b13304&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6e7dadaf-a750-488f-80e2-00f36fde00f0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.tmpUsedAdvancePayment.beneficiaryName&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;0001aa84-c5cb-4913-8620-86470e151a5b&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;DocumentAmount1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;078249df-64c8-48da-8c2f-aad5b0d5a8f2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.DocumentAmount1&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5b8908ae-05b8-4bdd-8486-f5556299d527&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c9b102bf-33d5-4731-8f45-0a8ad04e3ab9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;14fff378-300a-4fa6-8ebe-8f7407705f53&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.tmpUsedAdvancePayment.documentAmount&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;cf2870a3-931b-4b1d-8465-6230ed7de074&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;AmountAllocatedfortheCurrentRequest&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d4769ee1-cee6-418d-9ee2-03c3521b5ddd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0600835e-9ed7-44eb-8d89-82ea934c71c0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.AmountAllocatedfortheCurrentRequest&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6229de39-c8f8-4473-8240-d6f1b2cc1550&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1da150fa-02ba-475b-85f1-3b95670ea160&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0c0bced5-a447-48c8-8066-9fb0b21700cc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;REQUIRED&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b51965c9-535a-41f7-8adf-0ff95dfe9a28&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if (me.getData() &amp;lt; 0 || me.getData() &amp;gt; ${OutstandingAmount}.getData()){
	me.setData(0.0);
	me.setValid(false,"must be more than 0 and less or equal Outstanding Amount");
}
else{
	me.setValid(true);
}
if(me.getData() != null &amp;amp;&amp;amp; me.getData() != undefined &amp;amp;&amp;amp; me.getData() &amp;gt; 0){
	${converttorequestcurrency}.setEnabled(true);
}else{
	${converttorequestcurrency}.setEnabled(false);
}
${addbutton}.hide();
if(me.getData() != null){
${AllocatedAmountinRequestCurrency}.setData(0.0);
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e5eb6f4d-41de-42e5-8cf7-ac310abd71ef&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_BLUR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;55031c61-20ea-43d3-8afc-c01eea4e8960&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.tmpUsedAdvancePayment.AmountAllocated&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;65a780b1-0356-46be-83f2-97ead6d1828c&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout4&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4d004e10-12d2-4312-8347-e0d92ae89c16&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 4&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1d5f4a09-83e9-4168-80cd-c63c9f075938&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d4749797-9e11-49aa-8390-bbf3450b4cd1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;fdf4f22e-1674-4730-8113-64f5098c771d&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;00e650f2-269b-4566-8e45-997053b11dd6&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;InvoiceNumber&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1f5191a5-e4da-4750-826c-2133bbc07e7a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.InvoiceNumber&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6fcdc16c-e265-466c-8e0a-b5a7feed1685&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2d6e051b-bba6-42d0-840b-e845de8247a2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5a37f0e5-56ef-4500-87d9-1af6e29ecf87&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.0e61869e-73fd-4401-b156-8c11adaec3f8&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.tmpUsedAdvancePayment.invoiceNumber&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;3a261381-5796-4cd7-8404-91d3a37d92e1&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;PaidAmount&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a4d51507-f278-43eb-8fa6-9812ba104c2f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.PaidAmount&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;15e379e0-54c0-4ff6-8d1f-0d182f2af56d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ff81ee3b-364b-4dcd-8173-fe91852a1864&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7ad67f7f-52ce-4d12-8622-eddcd61de485&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.tmpUsedAdvancePayment.paidAmount&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;3f799bfb-e0fa-487d-837f-0eb940612c47&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Output_Text2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;454a170c-e180-47c9-8958-503bdde5010a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt; &lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c66afd45-7656-44db-8ec2-3f1dfe90836c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;152117aa-e768-4219-8856-f7b4b26ebafa&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;536fc3b7-fea5-4007-827e-221ff7301d57&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"HIDDEN"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3eff0fec-2fc0-4b16-8148-7b602c6b4345&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"M"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.f634f22e-7800-4bd7-9f1e-87177acfb3bc&lt;/ns2:viewUUID&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;197ca5fd-588d-499f-86da-f8ce3c36ba84&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;converttorequestcurrency&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;06779439-d729-49da-87ee-fd4d01e3c342&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Convert to request currency&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3366108a-b957-4b17-84a8-453d91d39e78&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;89980019-5a50-4484-8efb-6f1ad0da57a8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cc963241-4a0c-4807-8f6b-07f3be81cb17&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e66bba4f-e61d-477d-8d39-cddad30a1fb0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;shapeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;R&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1ae060d9-bbd4-487b-8046-98e2173178f9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;358b16f6-0773-41f8-8a67-5c88981e4d8b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CLICK&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.convert();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;18fff3fd-1131-40c2-8577-bc39a274df4c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;me.setEnabled(false);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d2cd36c6-0040-422e-823e-cba1e876efd4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;preventMultipleClicks&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1742bcb7-8db9-4b51-8ccf-938ce4e3266b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;outline&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8a8a7322-45ed-4858-85be-a1b5a06a91e0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;4fabe118-8f9a-497a-8025-b2a0b186dd5c&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout5&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8647774f-6d88-4ab4-8ae7-2f82fe463d72&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 5&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;97cf4486-e04f-4713-8832-65df14e3f0d9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9a35c42d-9d0a-4c27-8784-944158adedc4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;f8719399-b734-451c-8d6e-70c4c86d7aa4&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;140f7ce3-250f-47d1-83ba-af650bac535a&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Currency&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5def5bab-6ecc-4271-8b05-8c104f318a35&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.Currency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;33abe48f-26b2-44b4-869a-87478f82e03f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;26a9d43f-2ffb-490d-8322-52d47f62be67&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2a36b089-6f24-48de-8bf1-559d4947ad5b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;57e0c55a-37b9-44e2-8005-37e0d42c64e7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0d99b566-0d94-43c6-8d7d-373a0a918f34&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.9545825f-4a3f-43f2-82b0-a6b75c82df6f&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6522b516-0262-4a30-8248-4eaa559a1ccb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.Lookups.IDC_Currency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;323b4729-2a88-499b-8f4f-268f1c2d6ed3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"code","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b79a21e9-c2ac-4ed2-8104-6a483abded52&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"code","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3ad935b8-f3c5-41d6-899b-35416ea067e9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.tmpUsedAdvancePayment.invoiceCurrency.code&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;08991932-ae30-4db1-87e2-0ee2630b8035&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;OutstandingAmount&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d98cbcda-02ac-4770-8915-9ac4ebf428a0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.OutstandingAmount&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9ba83192-8766-4bee-84a8-102743cb7509&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8c16e856-dd6f-4625-8de6-bc5984300249&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;596614a2-bd5f-47cd-894b-703a998e3fbb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.tmpUsedAdvancePayment.outstandingAmount&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;3bb7d4d5-5844-4891-87e8-4a4825a34c74&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;AllocatedAmountinRequestCurrency&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1f429681-3d55-4853-8886-dad4316ac2a5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;72eb3c21-f722-4d40-864d-53a7509053e3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.AllocatedAmountinRequestCurrency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b1483b6f-1b94-49f4-8706-75fb99beb3aa&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e309465b-511b-4a57-8ce5-1bbde3a193bc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2faef11b-b540-42b6-8d8f-805ccf69a973&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f518eeaa-5b01-4c3d-869c-11917002ee9b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(me.getData() != null &amp;amp;&amp;amp; me.getData() != undefined &amp;amp;&amp;amp; me.getData() != 0.0){
	${addbutton}.show();
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.tmpUsedAdvancePayment.AllocatedAmountinRequestCurrency&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;5cf5f350-335b-40c6-819e-32bcdfa357ba&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;convertCurrency&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;54096174-32e7-4b72-8b4c-7a7bc8b2d342&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;convert currency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9fe3b218-84bf-4da7-8f3e-5c285ad115a2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2d24f4f1-01c8-4aec-8c80-ac00a9212108&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;03b77682-b721-4177-803d-9f88a5140c33&lt;/ns2:id&gt;&lt;ns2:optionName&gt;attachedService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.4e624381-ed2d-4716-836c-494f4210ce96&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;35105c4f-8727-4353-839f-1fc31c5ca991&lt;/ns2:id&gt;&lt;ns2:optionName&gt;busyIndicator&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;59a54d79-11a6-4ae8-86c4-1d1db4a3dec7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCRESULT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;40e23121-d819-4dd7-8e69-1a4dd359d96a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCINVOKE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3921b6eb-ac3c-47d2-8614-e0a626070c98&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.tmpUsedAdvancePayment.AllocatedAmountinRequestCurrency&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;ced84751-5b60-47b0-86a7-1786726d8cff&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Horizontal_Layout4&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b491ff20-e494-4044-8499-3a491035cd00&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Horizontal layout 4&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5d98e872-5308-403d-89b1-09b92e4fbd64&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3b7cc3d9-2d0b-4c70-8a3b-b7d8e9b02e7d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cd612cd2-103c-4314-86a1-6cf77974579f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;hAlignment&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"R"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;84e2904c-deee-45fb-8868-3c84577a9b23&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;9bee37f4-d32b-4ec5-87ff-5edea74cd8a7&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Reset_1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;93c02e26-b515-4b03-812a-2a01415c29db&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Reset&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2bf11a57-1dcb-42ee-8e15-4c90234eae81&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7846ea4a-8857-47a6-8edd-dcbda728c903&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1916f905-ce53-4e28-80fd-128f3ca882c4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;931f560d-f187-4090-8db5-0c5ec73644f6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CLICK&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.resetAdvancePayment();
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;82db8164-ea0c-4942-89d4-d202d405e0af&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;W&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b801180b-9628-41f4-8357-565abbf7645d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;shapeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;R&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;09457d63-c82d-4cf5-8c92-293b53d36f8e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;outline&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;e5cca388-47d4-4f98-852a-75eed26d9f81&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;addbutton&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bd9f8075-ad29-4585-88cc-0110a42f2f23&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Add&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7c39b281-25ca-49fb-8770-8cbd1d6db07f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3c36ca4c-144b-486a-82ef-f315cc0ac8f5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4b9c3021-ac83-4c14-8011-f74e3d35e9a3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CLICK&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.addAdvancePayment();
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7d22b35f-0820-4a32-8ae1-4f3fd5f7b06e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;B&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cec025e3-7ac8-44c3-8cdb-22f196c5ae41&lt;/ns2:id&gt;&lt;ns2:optionName&gt;shapeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;R&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ce4b5a19-ce2d-46ef-8e3e-f01c43dc9907&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//me.setEnabled(false);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;71c97188-b4a0-40f6-8eeb-f597f5c7389f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;preventMultipleClicks&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d11f6a37-8cc1-4375-884d-2eebf4274aa1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;ghostMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d5ee43e6-da90-4cef-89ce-e02da0e0d2c7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"HIDDEN"}]}&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;384a59d1-70c6-4537-8110-2c53ed6f45f9&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;AddAdvancePaymentCall&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;94fd5c31-5b17-4045-8de8-78841ea3dd90&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Add Advance Payment&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;420cb7a2-325f-4add-84b3-bd29550c6307&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8403746b-abe2-4ef5-8c79-a8072818195f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0d0a1136-e704-42a8-8186-b8770091d26c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;attachedService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.c042b0b3-9130-4489-8642-a4a851c1b331&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bf0008ae-31d9-4fa0-8f87-f2e28cbc0285&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCRESULT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.addtoTable();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0217f291-ed67-4606-8268-9f3a926a481a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.tmpUsedAdvancePayment.outstandingAmount&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;125cc6da-9203-47f5-8950-4d324456679e&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;UpdateAdvancePaymentCall&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8b9ea3ad-0c81-4586-8bce-ff39d3670588&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Update Advance Payment&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c02ca411-efcd-41f9-88f9-85eec7ab3fe1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;16cca013-1475-478a-8ffe-075a2e32e32b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bd367d81-47be-4ac3-84ba-f19cdc3abd5e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;attachedService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.33e9489c-6751-46e8-bc5c-74b573bdd856&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f464fe04-6fb7-40af-8fe8-d6c897b6c00f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCRESULT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.updateinTable();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1d2e5d4c-d4fb-4eaa-8ad7-77a681a3009f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.tmpUsedAdvancePayment.outstandingAmount&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;4a49dbaa-970d-4edd-810b-b7d84f3557d2&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Horizontal_Layout1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;22aeb9a3-b3be-4aed-8a24-0096535a8075&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Horizontal layout&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;657983d8-4007-48ec-82ec-5b8fe05bc085&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;525a78a1-8a43-4270-850e-5afa0038ba5e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;423e13ce-0c41-4e9c-83c4-6bdb25aba314&lt;/ns2:id&gt;&lt;ns2:optionName&gt;layoutFlow&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"H"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;792ec3fe-257a-42f3-8383-70bf572490e3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;hAlignment&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"L"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;31be30ca-4a73-4459-8e04-05ebd341922e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@overflow&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"visible"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a7cdbb85-c184-493e-8239-25c859ae9e34&lt;/ns2:id&gt;&lt;ns2:optionName&gt;deferLoad&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b25c6970-c7bf-4f1c-8a7f-181fffcf2e84&lt;/ns2:id&gt;&lt;ns2:optionName&gt;width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;b36bc59a-4e14-4164-8e7b-4b46e5ee27eb&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;2ca6f401-2dbe-45b6-8356-73049932e66f&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;advancePaymentTable&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6ae83fdc-e3f8-4563-86d7-18768cc93b00&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Advance Payment&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7be0f9c3-c8ea-427f-8a98-1033a566df8e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d02387bf-3aaa-489f-882a-98059aab3a90&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c317e163-89bd-45a0-8161-1637651bd37b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@overflow&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"visible"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3b09441e-8013-4fbc-8c4c-1cae5da4ba63&lt;/ns2:id&gt;&lt;ns2:optionName&gt;width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"150%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2b1c7735-2ecf-4eb0-81bb-e2c84f93e001&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showDeleteButton&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2d72a806-c67f-4549-8456-cb9e5906db76&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":""}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;db8dd74d-9590-482e-8596-d5376e5ed45c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;tableStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;H&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;31b62714-06da-46b2-84ed-510715c9054f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;06cab34f-c060-45a6-8481-eb8424aea1c0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showAddButton&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;953ecb6a-8878-4673-8d82-5061b815d03b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"EDITABLE"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ac559873-d6cd-4785-81d6-aab635b90385&lt;/ns2:id&gt;&lt;ns2:optionName&gt;expandableRowRenderAs&lt;/ns2:optionName&gt;&lt;ns2:value&gt;V&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fca965b2-7656-4b02-8e4d-2229d250b474&lt;/ns2:id&gt;&lt;ns2:optionName&gt;selectionMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8e63b597-303c-4354-8f33-97427a86eaab&lt;/ns2:id&gt;&lt;ns2:optionName&gt;allowWrap&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c18d17aa-c5db-4668-89af-bafbab32e82e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;highlightSel&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e0e8f625-e27c-43c7-83d4-a462c4a2702a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9b06fed8-db2c-4109-8189-02435a9843c0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_ADDREC&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;97c6d359-d8a5-4a2b-8fda-1fa158005c3e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_DELREC&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.updateBTNDisable(item);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0143a377-fabf-4d03-83de-7950b3746634&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_ROWSLOADED&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ae03489d-04bc-4633-887e-e6a57518eb13&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_COLUMNDROP&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.f515b79f-fe61-4bd3-8e26-72f00155d139&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.usedAdvancePayment[]&lt;/ns2:binding&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;78f11ec5-1224-4fe7-8660-e99987af0667&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;2d60e619-9a80-4e60-87d1-a5e0b33022dc&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Display_text4&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ae3690b5-5953-4488-87fb-e0e14de92726&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.AdvancePaymentRequestNumber&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c526921e-a9e8-4442-8e2c-712559201a1d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9044c46e-6f36-434c-8b59-521563cc01b7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;HIDE&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;df948a4e-bf04-4a06-8663-10edbe41842e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b85a2e5d-ee5f-4651-8ab0-2cd9c8982838&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"D"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.usedAdvancePayment.currentItem.advancePaymentRequestNumber&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;67c6a59f-c705-4c3d-8070-1ce102e6082b&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Display_text5&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9d332a9f-8b99-4228-844b-0dc53c59ae87&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.BeneficiaryName&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4bf69307-afb6-4fb0-8905-c3faf368be8b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3f1385aa-5ced-4d21-8830-83d7b14540cb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;HIDE&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d0100bd2-e36f-4b04-823b-1a820baf9535&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.usedAdvancePayment.currentItem.beneficiaryName&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;87f191fc-4f2e-4f86-8a32-43a5306540ca&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Display_text6&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f494f3bd-fb54-407f-8d0b-11a56ff31493&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.InvoiceNumber&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;04bb196c-3b11-4ea0-8d2b-03e50fef91f3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b655f0e9-0150-4d71-8ee5-0c3303dcf158&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;HIDE&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;440585d2-a609-43c3-89f5-6a570c066cc7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.0e61869e-73fd-4401-b156-8c11adaec3f8&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.usedAdvancePayment.currentItem.invoiceNumber&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;915bdca9-8a54-462e-808a-c9773cdf922c&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Display_text7&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e4c0eef1-ee05-446c-8003-5f55eecf8741&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.Currency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;451e9617-61a9-4f94-8957-ea7b9c663bc9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e91294d8-1a57-46de-8f9a-f5793e3e0ed7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;HIDE&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fc91971a-bd61-40ff-84c8-3e7b1dd53a95&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;dace276f-3f42-4304-8236-d9af5c8b38a3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d09e6ed8-a9da-428d-8bcb-e3a5c872a594&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.9545825f-4a3f-43f2-82b0-a6b75c82df6f&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;49d728a0-cb36-4c0c-8ba7-a51268f42f50&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.Lookups.IDC_Currency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;62f717f7-d822-4880-81ca-357676167b5f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"code","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e0b9112e-8e8b-4a30-85df-c9b7ee9779ab&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"code","optionDisplayProperty":"englishdescription"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;16cc6ee1-efd4-47e6-8ee7-524c94e3a4d8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//@AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.usedAdvancePayment.currentItem.invoiceCurrency&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;e10c0490-dbd3-4a4e-80b1-4df497a7b919&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Display_text8&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b5aad5c5-b34f-4322-8f17-a3f30707aa96&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.DocumentAmount1&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7c2ac108-9508-43dc-84d0-89bbac9af01a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;061cd53c-5e14-49e9-8c0f-89681e3ac558&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;HIDE&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3afe8391-cecd-47d4-8d69-38b282209bc3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.usedAdvancePayment.currentItem.documentAmount&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;*************-4a85-809e-c5e09c841bf0&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Display_text10&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0b6c454e-b760-4441-8783-86d7595b5e73&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.OutstandingAmount&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e11776df-0e9b-436d-8e2e-23237e25581d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;38fe3871-0d55-4497-8d35-dc71e85d7bd0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;HIDE&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8aa668df-a652-4589-8186-3eda6ce7f716&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.usedAdvancePayment.currentItem.outstandingAmount&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;35118230-fb76-4f45-826d-e678611e28d0&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Display_text11&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;079a8929-59f2-467e-8116-e31e3f23436f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.AmountAllocatedfortheCurrentRequest&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;71d3073f-2ee3-4cc0-83f9-f35915088d90&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ad5d7a97-8018-4e70-8bc0-26ad37cbcb98&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;HIDE&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f3c6a77f-1e93-4fb5-8b7d-474c98a2f59a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a9e421d0-439b-4098-8ac8-e5413d53bc68&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;@sumAllocated();

&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e5d85bdb-faef-4b0e-81e7-415d95560bbc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;@sumAllocated();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.usedAdvancePayment.currentItem.AmountAllocated&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;b2864cc2-2307-4c12-87ab-ceb4c1176995&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Plain_text1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ed92f8f6-2e74-4605-8b94-0eb0472c2be5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e22fa07d-79c5-47b9-8592-a4dc388efd69&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.AllocatedAmountinRequestCurrency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5a6e3b2e-76f0-4428-8a81-9fccbefd2805&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ba2bc59c-ede9-45d2-8474-9847868dea3e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;HIDE&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1bdbf7d2-b081-4a5b-8657-3c9afe7bc351&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ffcfb400-13a3-4dd6-8d3f-fc2292932ef8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.usedAdvancePayment.currentItem.AllocatedAmountinRequestCurrency&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;fead5d73-7156-4af2-8d09-fb45c18ec73b&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;DeleteAdvancePaymentCall&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ef376207-0891-484c-87eb-8400807e1ede&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Delete Advance Payment&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;dd071f75-8e94-47a7-8ff5-2338b3684de4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d42fed51-5ee2-4273-87bf-1a7aeddccc09&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ef89cd6a-5cff-405d-8dfa-ca5d0682eb8f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;attachedService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.68698fcd-008a-4312-8428-1b0aee0e67c8&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f66bd9dc-8a21-4c32-8a6e-1dac290aca12&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9&lt;/ns2:viewUUID&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;518da43a-1f53-4708-83db-981aface4d4c&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;update_Layout&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e7dd98bd-bc1d-4542-81ec-57f4621e96ff&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;update layout&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3df425fc-876e-49b1-8ec3-f0282a0a5e07&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;40104589-535f-4872-8873-f276eb27df20&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;781f1bd0-c8e2-4bb0-8dba-7f7a09ec0399&lt;/ns2:id&gt;&lt;ns2:optionName&gt;hAlignment&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"R"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;df9a646d-1c19-4d47-8d08-253dbc51f9fa&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;a0e03971-e1bf-477e-8887-40d318a109a3&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;updateButton&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c12adde2-c5f0-4838-80f1-57286638a266&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Update Selected &lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1ad681d8-64cb-4630-8bf1-f3cb8312f4f8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7932f8c3-d0b6-4e55-8c3d-ab4bf976be4b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ac71a2b2-6a8e-4e7f-8aea-417891327302&lt;/ns2:id&gt;&lt;ns2:optionName&gt;shapeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;R&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;23025f91-f3b3-4283-85ac-39aa60b7c52d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;ghostMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;818fbbc9-1a97-4ff7-8ba4-9501c2b6df87&lt;/ns2:id&gt;&lt;ns2:optionName&gt;outline&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6af49b67-46d4-4ddd-83d5-8b9f29e56d19&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;312879f6-a277-4575-8648-712a5352b1ee&lt;/ns2:id&gt;&lt;ns2:optionName&gt;width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0f2fb8b4-3aaa-4ccd-8c1e-29133885f57c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;iconLocation&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c94559e6-0643-40a0-861a-3ef25f65c5de&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"EDITABLE"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;af053813-4906-433a-8853-ac7c23c12d65&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;me.setEnabled(false);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;445a3675-41d4-44ef-82ff-b74e6ab04b13&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CLICK&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.updateAdvancePayment();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;3ef0bc5c-f1d9-47c4-846a-f8561d5dac11&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Horizontal_Layout5&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a42256ba-e89f-44a3-8159-075b52733eb7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Horizontal layout 5&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9652964a-0054-4588-8fa3-cd53ee5cdfa3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;387df1d3-145c-4384-80a7-39b9468f6b44&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f51ef16c-aecd-41e1-8546-2b9d2a9e0422&lt;/ns2:id&gt;&lt;ns2:optionName&gt;hAlignment&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"L"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;ecc12673-0697-4e41-8fb6-51eb1cb3220b&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;b2ed4232-1117-4c9f-851f-2cad54577360&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;TotalAllocatedAmountinRequestCurrency&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fa3e5671-ea7b-467b-945d-5f970bc75257&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ecbf2c9b-629d-49b4-86eb-a15170a4d269&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.TotalAllocatedAmountinRequestCurrency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e3ddd96b-770a-41c8-85e5-7fa2d61335c9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;67e733e8-ebb8-4ab4-8c5e-5640935e33a9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cd12df2d-a37f-468a-8138-e85e504d2364&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b04a621d-f815-499f-8531-953ac92ba3c8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7202372b-88ee-4bf0-8ce7-468cfb1e159e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9c2d2a39-cb9e-4a57-8912-9d5d61432aa7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//${addbutton}.setEnabled(false);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.TotalAllocatedAmountinRequestCurrency&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>true</isMobileReady>
        <loadJsFunction isNull="true" />
        <unloadJsFunction isNull="true" />
        <viewJsFunction isNull="true" />
        <changeJsFunction isNull="true" />
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction isNull="true" />
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>0a6db489-c287-49bd-870c-27fb173d46f5</guid>
        <versionId>9bbe4e1d-2915-4f99-9ebc-c9ed04293d85</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <bindingType name="usedAdvancePayment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewBindingTypeId>65.cc6d32f0-aac5-4996-9cf6-119637c0cf8c</coachViewBindingTypeId>
            <coachViewId>64.ab2ea11b-7c5e-4835-9ed4-18708eee21be</coachViewId>
            <isList>true</isList>
            <classId>/12.fc23c5c1-8fd3-4584-89be-8352a606da67</classId>
            <seq>0</seq>
            <description isNull="true" />
            <guid>5e97e109-bb96-4274-8285-89f85ba4be34</guid>
            <versionId>01fe8b94-bc6c-4475-9267-4758a3e1cc32</versionId>
        </bindingType>
        <configOption name="tmpUsedAdvancePayment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.44542061-b7f2-4824-8e82-c34b7beed156</coachViewConfigOptionId>
            <coachViewId>64.ab2ea11b-7c5e-4835-9ed4-18708eee21be</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>/12.fc23c5c1-8fd3-4584-89be-8352a606da67</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>0</seq>
            <description></description>
            <groupName></groupName>
            <guid>898d15d8-d8f5-41bf-a6c2-7b9961efa17e</guid>
            <versionId>7356bff7-4ca0-4431-9808-fa097a5ea671</versionId>
        </configOption>
        <configOption name="TotalAllocatedAmountinRequestCurrency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.8a893d7a-b2c9-4ccc-8acc-1d9191378cdb</coachViewConfigOptionId>
            <coachViewId>64.ab2ea11b-7c5e-4835-9ed4-18708eee21be</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>1</seq>
            <description></description>
            <groupName></groupName>
            <guid>b6352126-f9af-40e8-a0df-5c59c385002c</guid>
            <versionId>e662db8d-561c-47a2-ae28-24efe00efbd9</versionId>
        </configOption>
        <configOption name="requestCurrency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.7d947b3d-c2f6-4889-8a31-10a507115238</coachViewConfigOptionId>
            <coachViewId>64.ab2ea11b-7c5e-4835-9ed4-18708eee21be</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>2</seq>
            <description></description>
            <groupName></groupName>
            <guid>dc987101-ffa3-4f60-85ef-1d57297f463f</guid>
            <versionId>78e4ba35-f8c0-4798-b881-bb9cf07ed878</versionId>
        </configOption>
        <configOption name="addBtn">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.b7435489-60cb-401b-9283-4176b482eb96</coachViewConfigOptionId>
            <coachViewId>64.ab2ea11b-7c5e-4835-9ed4-18708eee21be</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>3</seq>
            <description></description>
            <groupName></groupName>
            <guid>bf41ebc1-3ba5-4e6e-a1da-8144076e46bb</guid>
            <versionId>eea937df-c873-4aac-a967-a7a1df6f8442</versionId>
        </configOption>
        <configOption name="isChecker">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.977d8aaa-279b-4625-a604-07aef348c42e</coachViewConfigOptionId>
            <coachViewId>64.ab2ea11b-7c5e-4835-9ed4-18708eee21be</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>4</seq>
            <description></description>
            <groupName></groupName>
            <guid>f63cdb9f-7ab4-4d9b-ba36-58894f20808f</guid>
            <versionId>f39b5da2-b85e-45cb-8a8f-026ecd1af656</versionId>
        </configOption>
        <configOption name="currencyVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.97fdc6c5-e91e-4773-82ec-6f36e24b7618</coachViewConfigOptionId>
            <coachViewId>64.ab2ea11b-7c5e-4835-9ed4-18708eee21be</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>5</seq>
            <description></description>
            <groupName></groupName>
            <guid>a076d00b-8369-4596-a5bd-2503fbf77424</guid>
            <versionId>7b4b7e5d-351a-4523-9c87-f09c106f7511</versionId>
        </configOption>
        <configOption name="currncy">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.cb8b5adf-6fb9-4bb6-8787-61b85e00a75b</coachViewConfigOptionId>
            <coachViewId>64.ab2ea11b-7c5e-4835-9ed4-18708eee21be</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>6</seq>
            <description></description>
            <groupName></groupName>
            <guid>2ffe6098-393b-42e7-9723-f161cdf09fbf</guid>
            <versionId>ab1a62ac-f705-49cc-9e82-1f086eafad01</versionId>
        </configOption>
        <configOption name="requestID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.ca95ec31-f4ef-408d-acd3-f44400e8ec07</coachViewConfigOptionId>
            <coachViewId>64.ab2ea11b-7c5e-4835-9ed4-18708eee21be</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>7</seq>
            <description></description>
            <groupName></groupName>
            <guid>1ad60110-72d4-43ee-925f-7b5f2e97f9a2</guid>
            <versionId>fa1b7a33-e135-4a66-9227-72554e6ff70a</versionId>
        </configOption>
        <configOption name="CIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.6fe5a82e-c0b9-44d9-a0c9-a564a972bc89</coachViewConfigOptionId>
            <coachViewId>64.ab2ea11b-7c5e-4835-9ed4-18708eee21be</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>8</seq>
            <description></description>
            <groupName></groupName>
            <guid>8ca12a45-6e53-4a8e-9f8d-106c0cec74aa</guid>
            <versionId>33fb724d-648b-4ac5-8f28-3a8889e92d8c</versionId>
        </configOption>
        <configOption name="alertMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.ed4cc9a7-18ac-4897-82a6-ae04ffa08d82</coachViewConfigOptionId>
            <coachViewId>64.ab2ea11b-7c5e-4835-9ed4-18708eee21be</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>9</seq>
            <description></description>
            <groupName></groupName>
            <guid>c881fe3c-aa13-4457-b3fd-f83fb8baa7c8</guid>
            <versionId>683f4566-a74a-4e58-8265-bc55b92e8ca0</versionId>
        </configOption>
        <configOption name="errorVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.60a50e18-86e2-4ffa-a8c9-61d7f86b1535</coachViewConfigOptionId>
            <coachViewId>64.ab2ea11b-7c5e-4835-9ed4-18708eee21be</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>10</seq>
            <description></description>
            <groupName></groupName>
            <guid>980196bb-8c30-47e0-9c8b-0e0c474576bf</guid>
            <versionId>f74c60f8-02b2-4d32-8efa-54fbf5c281b9</versionId>
        </configOption>
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.99f9d8df-9251-4282-868e-05f5eae762d2</coachViewInlineScriptId>
            <coachViewId>64.ab2ea11b-7c5e-4835-9ed4-18708eee21be</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock>var isUpdate = false;&#xD;
var item = {};&#xD;
this.addAdvancePayment = function  () {&#xD;
	this.context.options.currencyVis.set("value", "READONLY");&#xD;
	item = this.context.options.tmpUsedAdvancePayment.get("value");	&#xD;
	if (isUpdate &amp;&amp; item.get("AllocatedAmountinRequestCurrency")!=undefined &amp;&amp; item.get("AllocatedAmountinRequestCurrency")!=null &amp;&amp; this.context.options.tmpUsedAdvancePayment.get("value").get("AmountAllocated") &gt; 0 &amp;&amp; this.context.options.tmpUsedAdvancePayment.get("value").get("AmountAllocated") &lt; this.context.options.tmpUsedAdvancePayment.get("value").get("outstandingAmount") &amp;&amp; this.context.options.tmpUsedAdvancePayment.get("value").get("AllocatedAmountinRequestCurrency") &gt; 0 ){&#xD;
		var input = this.context.options.requestID.get("value") +"-"+ this.context.options.tmpUsedAdvancePayment.get("value").get("AllocatedAmountinRequestCurrency") +"-"+ this.context.options.tmpUsedAdvancePayment.get("value").get("AmountAllocated") +"-"+ this.context.options.tmpUsedAdvancePayment.get("value").get("DBID");&#xD;
			&#xD;
		this.ui.get("UpdateAdvancePaymentCall").execute(input);&#xD;
		&#xD;
	}&#xD;
	else{&#xD;
		if ( (item.get("AmountAllocated")!=undefined &amp;&amp; item.get("AmountAllocated")!=null &amp;&amp; item.get("AmountAllocated") &gt; 0 &amp;&amp; item.get("AmountAllocated") &lt;= item.get("outstandingAmount") ) &amp;&amp; (item.get("beneficiaryName")!=null &amp;&amp; item.get("beneficiaryName")!= "" &amp;&amp; item.get("beneficiaryName")!= undefined) &amp;&amp; this.context.options.tmpUsedAdvancePayment.get("value").get("AllocatedAmountinRequestCurrency") &gt; 0) {&#xD;
//			this.ui.get("advancePaymentTable").appendElement(item);&#xD;
			var input = this.context.options.requestID.get("value") +"-"+ this.context.options.tmpUsedAdvancePayment.get("value").get("AllocatedAmountinRequestCurrency") +"-"+ this.context.options.tmpUsedAdvancePayment.get("value").get("AmountAllocated") +"-"+ this.context.options.tmpUsedAdvancePayment.get("value").get("DBID");&#xD;
			&#xD;
			this.ui.get("AddAdvancePaymentCall").execute(input);&#xD;
			&#xD;
&#xD;
		}else{&#xD;
			alert("can not add");&#xD;
		}&#xD;
	}&#xD;
&#xD;
//	this.sumAllocated();&#xD;
}&#xD;
this.addtoTable = function name () {&#xD;
	this.ui.get("updateButton").setEnabled(true);&#xD;
	this.context.binding.get("value").add(item);&#xD;
	this.context.options.tmpUsedAdvancePayment.set("value", {});&#xD;
	this.context.options.tmpUsedAdvancePayment.get("value").set("invoiceCurrency", {});&#xD;
}&#xD;
this.updateinTable = function () {&#xD;
	&#xD;
	var selectedItem= this.ui.get("advancePaymentTable").getSelectedIndex();&#xD;
//	----------------------------------------------------------------------&#xD;
	this.ui.get("advancePaymentTable").removeRecord(selectedItem);&#xD;
	this.ui.get("advancePaymentTable").appendElement(item);&#xD;
//	--------------------------------------------------------------------			&#xD;
	this.context.options.tmpUsedAdvancePayment.set("value", {});&#xD;
	this.context.options.tmpUsedAdvancePayment.get("value").set("invoiceCurrency", {});&#xD;
	isUpdate = false;&#xD;
}&#xD;
&#xD;
this.getAdvancePayment = function () {&#xD;
	var input  = this.ui.get("AdvancePaymentRequestNumber").getData()+"-"+this.context.options.CIF.get("value")+"-"+this.context.options.requestID.get("value");&#xD;
	&#xD;
	this.ui.get("retrieveAdvancePayment").execute(input);&#xD;
}&#xD;
&#xD;
this.updateBTNDisable = function  (rowSelected) {&#xD;
	&#xD;
	var input = this.context.options.requestID.get("value") +"-"+ rowSelected.DBID;&#xD;
			&#xD;
	this.ui.get("DeleteAdvancePaymentCall").execute(input);&#xD;
	&#xD;
	var amount = this.context.options.TotalAllocatedAmountinRequestCurrency.get("value") - rowSelected.AllocatedAmountinRequestCurrency&#xD;
	if (this.context.binding.get("value").length() == 1) {&#xD;
		this.ui.get("updateButton").setEnabled(false);&#xD;
		&#xD;
		if (this.context.options.currncy.get("value") == true) {&#xD;
			this.context.options.currencyVis.set("value", "DEFAULT");&#xD;
		}&#xD;
	}&#xD;
	this.context.options.TotalAllocatedAmountinRequestCurrency.set("value", amount);&#xD;
&#xD;
}&#xD;
this.sumAllocated = function  () {&#xD;
	var sum = 0.0;&#xD;
	&#xD;
//	alert("sum");&#xD;
	this.context.options.TotalAllocatedAmountinRequestCurrency.set("value", sum);&#xD;
&#xD;
	for (var i=0; i&lt;this.context.binding.get("value").length(); i++) {&#xD;
		sum+=this.context.binding.get("value").get(i).get("AllocatedAmountinRequestCurrency");&#xD;
	}&#xD;
//	this.context.options.addBtn.set("value", false);&#xD;
//	this.context.options.vis.set("value", "EDITABLE");	&#xD;
//	this.context.options.vis.set("value", "READONLY");&#xD;
	this.context.options.TotalAllocatedAmountinRequestCurrency.set("value", sum);&#xD;
//	this.ui.get("addbutton").setEnabled(true);&#xD;
//	this.ui.get("addbutton").setEnabled(false);&#xD;
	this.ui.get("addbutton").hide();&#xD;
	&#xD;
}&#xD;
this.resetAdvancePayment = function  () {&#xD;
	isUpdate = false;&#xD;
	&#xD;
	this.context.options.tmpUsedAdvancePayment.set("value", {});&#xD;
	this.context.options.tmpUsedAdvancePayment.get("value").set("invoiceCurrency", {});&#xD;
//	this.context.options.vis.set("value", "EDITABLE");	&#xD;
//	this.context.options.vis.set("value", "READONLY");&#xD;
//	this.ui.get("addbutton").setEnabled(true);&#xD;
//	this.ui.get("addbutton").setEnabled(false);&#xD;
	this.ui.get("addbutton").hide();&#xD;
	&#xD;
}&#xD;
this.updateAdvancePayment = function  () {&#xD;
	isUpdate = true;&#xD;
	var selectedItem= this.ui.get("advancePaymentTable").getSelectedRecord();&#xD;
	this.context.options.tmpUsedAdvancePayment.set("value", selectedItem);&#xD;
&#xD;
}&#xD;
this.convert = function  () {&#xD;
&#xD;
	if(this.context.options.requestCurrency.get("value") !="" &amp;&amp; this.context.options.requestCurrency.get("value") !=null &amp;&amp; this.context.options.requestCurrency.get("value") !=undefined){&#xD;
			var amount = this.context.options.tmpUsedAdvancePayment.get("value").get("AmountAllocated");&#xD;
			var fromCurrency = this.context.options.tmpUsedAdvancePayment.get("value").get("invoiceCurrency").get("code");&#xD;
			var toCurrency = this.context.options.requestCurrency.get("value");&#xD;
			this.ui.get("convertCurrency").execute(amount+"-"+fromCurrency+"-"+toCurrency);&#xD;
//			this.context.options.vis.set("value", "EDITABLE");&#xD;
//			this.ui.get("addbutton").setEnabled(true);&#xD;
//			this.ui.get("addbutton").show();&#xD;
			&#xD;
	}else{&#xD;
		alert("please select Request Currency");&#xD;
	}&#xD;
}&#xD;
&#xD;
this.checkerVis = function  () {&#xD;
	if (this.context.options.isChecker.get("value") == true) {&#xD;
		this.ui.get("Horizontal_Layout3").setVisible(false,true);&#xD;
		this.ui.get("Horizontal_Layout4").setVisible(false,true);&#xD;
		this.ui.get("retrive_data").setVisible(false,true);&#xD;
		this.ui.get("update_Layout").setVisible(false,true);&#xD;
		this.ui.get("advancePaymentTable").setEnabled(false);&#xD;
	}&#xD;
}&#xD;
&#xD;
//----------------------------------------------------------------&#xD;
//function to view alert in case of get customer info error&#xD;
this.AjaxErrorHandling = function(errorMSG)&#xD;
{&#xD;
	this.context.options.alertMessage.set("value", errorMSG);&#xD;
	this.context.options.errorVis.set("value", "EDITABLE")&#xD;
}</scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>e939761f-cb14-4248-8609-d8ff6060cb7f</guid>
            <versionId>e1dd1185-9b60-4689-a51c-82c9d27715f8</versionId>
        </inlineScript>
        <localization>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewLocalResId>69.a7887ecf-dc21-4e80-8f20-8fc847a703d6</coachViewLocalResId>
            <coachViewId>64.ab2ea11b-7c5e-4835-9ed4-18708eee21be</coachViewId>
            <resourceBundleGroupId>/50.95ec9bf9-2d5a-4fc5-8dd1-e6d6a55a836c</resourceBundleGroupId>
            <seq>0</seq>
            <guid>f0ed459d-d1c2-471f-9e81-82b08649c37c</guid>
            <versionId>d96490f8-ea80-42dd-abe9-4d944bdb64e2</versionId>
        </localization>
        <localization>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewLocalResId>69.24562d5d-e475-4c9a-afaf-849310a53c7e</coachViewLocalResId>
            <coachViewId>64.ab2ea11b-7c5e-4835-9ed4-18708eee21be</coachViewId>
            <resourceBundleGroupId>/50.d37ebe05-41d3-47ac-9237-53de467d6a4a</resourceBundleGroupId>
            <seq>1</seq>
            <guid>470d9b92-2640-415f-9576-14df4f18d5b0</guid>
            <versionId>c0dd9a62-ce68-4ad7-93ec-5bd3a116e332</versionId>
        </localization>
    </coachView>
</teamworks>

