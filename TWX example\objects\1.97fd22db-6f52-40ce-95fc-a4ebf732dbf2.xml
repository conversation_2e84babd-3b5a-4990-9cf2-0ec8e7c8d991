<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.97fd22db-6f52-40ce-95fc-a4ebf732dbf2" name="CAD filter service">
        <lastModified>1689499125593</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.97fd22db-6f52-40ce-95fc-a4ebf732dbf2</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.cb93ddcb-0c61-4c70-8c1c-27777465be08</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:2a54785d0a22f63a:4ad54ef7:18949d884fe:-4bf2</guid>
        <versionId>8e12ecd0-e7fc-4851-bcd1-6e3046f5bb9b</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:5f469e3208239075:50b7be5e:1895df7903d:-5b92" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.7ccea82f-c281-4acb-82e0-5c590bde9eb7"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"9080e559-11e5-4cf1-817f-c2c06538cd2b"},{"targetRef":"cb93ddcb-0c61-4c70-8c1c-27777465be08","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exclusive Gateway","declaredType":"sequenceFlow","id":"2027.7ccea82f-c281-4acb-82e0-5c590bde9eb7","sourceRef":"9080e559-11e5-4cf1-817f-c2c06538cd2b"},{"startQuantity":1,"outgoing":["8cfb8509-0725-49ff-8c1c-22cfc88f0a47"],"incoming":["2027.7ccea82f-c281-4acb-82e0-5c590bde9eb7"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":305,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Get CAD team","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"cb93ddcb-0c61-4c70-8c1c-27777465be08","scriptFormat":"text\/x-javascript","script":{"content":["\/\/tw.local.originalTeam = new tw.object.Team();\r\n\/\/tw.local.originalTeam.members = new tw.object.listOf.String();\r\n\/\/tw.local.originalTeam.members[0] = \"idchubexemkr01\";\r\n\/\/tw.local.originalTeam.members[1] = \"bpmlrgexemkr11\";\r\n\/\/tw.local.originalTeam.members[2] = \"idchubexemkr59\";\r\n\/\/tw.local.originalTeam.managerTeam = \"Managers\";\r\n\/\/tw.local.originalTeam.name = \"IDC EXE HUB\";\r\n\r\ntw.local.filteredTeam = new tw.object.Team();\r\ntw.local.filteredTeam.members = new tw.object.listOf.String();\r\ntw.local.filteredTeam.managerTeam = tw.local.originalTeam.managerTeam;\r\ntw.local.filteredTeam.name = tw.local.originalTeam.name;\r\n\r\nvar users = tw.system.org.findRoleByName(tw.local.groupName).allUsers;\r\n\t\r\nlog.info(\"users::: \"+ users);\r\n\tfor (var i = 0; i &lt; users.listLength ; i++)\r\n\t{\r\n\t\ttw.local.filteredTeam.members[i] = users[i].name;\r\n\t}"]}},{"incoming":["8cfb8509-0725-49ff-8c1c-22cfc88f0a47"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:2a54785d0a22f63a:4ad54ef7:18949d884fe:-4bf0"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"d586b0a3-1397-4d46-8087-9ceddbb37af1"},{"targetRef":"d586b0a3-1397-4d46-8087-9ceddbb37af1","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"8cfb8509-0725-49ff-8c1c-22cfc88f0a47","sourceRef":"cb93ddcb-0c61-4c70-8c1c-27777465be08"}],"laneSet":[{"id":"c1a2e70a-c909-42c7-8883-c280f4dab0ea","lane":[{"flowNodeRef":["9080e559-11e5-4cf1-817f-c2c06538cd2b","d586b0a3-1397-4d46-8087-9ceddbb37af1","cb93ddcb-0c61-4c70-8c1c-27777465be08"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"4d2537e2-5b80-4fc4-85c3-7fa6f99344cf","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"CAD filter service","declaredType":"process","id":"1.97fd22db-6f52-40ce-95fc-a4ebf732dbf2","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0","name":"filteredTeam","isCollection":false,"id":"2055.c29dfa37-a7ad-4ebb-896f-8ab8974c0013","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnly":"true"}}],"inputSet":[{"dataInputRefs":["2055.bde0605f-cb9f-4d09-81f8-7f2f38ba7425","2055.7a91bc03-0cd0-44f7-84f2-8ce59885ef21"]}],"outputSet":[{}],"otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnlyOutputs":"true"},"dataInput":[{"itemSubjectRef":"itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0","name":"originalTeam","isCollection":false,"id":"2055.bde0605f-cb9f-4d09-81f8-7f2f38ba7425","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnly":"true"}},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"BPM_CA_LRG_CORP_EXE_MKR\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"groupName","isCollection":false,"id":"2055.7a91bc03-0cd0-44f7-84f2-8ce59885ef21"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="originalTeam">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.bde0605f-cb9f-4d09-81f8-7f2f38ba7425</processParameterId>
            <processId>1.97fd22db-6f52-40ce-95fc-a4ebf732dbf2</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a010a142-35d6-43b1-88ac-26d04a891ecc</guid>
            <versionId>f207031d-9193-47b1-acd8-ac54fc8174e4</versionId>
        </processParameter>
        <processParameter name="groupName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.7a91bc03-0cd0-44f7-84f2-8ce59885ef21</processParameterId>
            <processId>1.97fd22db-6f52-40ce-95fc-a4ebf732dbf2</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"BPM_CA_LRG_CORP_EXE_MKR"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>1d94f858-a5d1-4d4b-9f7d-113403cfb7af</guid>
            <versionId>2a2f0127-1876-409e-871a-8fe66e8fb4f2</versionId>
        </processParameter>
        <processParameter name="filteredTeam">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c29dfa37-a7ad-4ebb-896f-8ab8974c0013</processParameterId>
            <processId>1.97fd22db-6f52-40ce-95fc-a4ebf732dbf2</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>4636e88f-ec23-462e-8a36-aa70f53fd929</guid>
            <versionId>9fd48e06-f4ca-45db-8b6e-c74715ed5463</versionId>
        </processParameter>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.cb93ddcb-0c61-4c70-8c1c-27777465be08</processItemId>
            <processId>1.97fd22db-6f52-40ce-95fc-a4ebf732dbf2</processId>
            <name>Get CAD team</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.7ec5ffd7-361e-450b-b0cc-bfc78e132278</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:2a54785d0a22f63a:4ad54ef7:18949d884fe:-4be9</guid>
            <versionId>524ad79e-6a44-4941-a414-e59e58fe70e7</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="305" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.7ec5ffd7-361e-450b-b0cc-bfc78e132278</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>//tw.local.originalTeam = new tw.object.Team();&#xD;
//tw.local.originalTeam.members = new tw.object.listOf.String();&#xD;
//tw.local.originalTeam.members[0] = "idchubexemkr01";&#xD;
//tw.local.originalTeam.members[1] = "bpmlrgexemkr11";&#xD;
//tw.local.originalTeam.members[2] = "idchubexemkr59";&#xD;
//tw.local.originalTeam.managerTeam = "Managers";&#xD;
//tw.local.originalTeam.name = "IDC EXE HUB";&#xD;
&#xD;
tw.local.filteredTeam = new tw.object.Team();&#xD;
tw.local.filteredTeam.members = new tw.object.listOf.String();&#xD;
tw.local.filteredTeam.managerTeam = tw.local.originalTeam.managerTeam;&#xD;
tw.local.filteredTeam.name = tw.local.originalTeam.name;&#xD;
&#xD;
var users = tw.system.org.findRoleByName(tw.local.groupName).allUsers;&#xD;
	&#xD;
log.info("users::: "+ users);&#xD;
	for (var i = 0; i &lt; users.listLength ; i++)&#xD;
	{&#xD;
		tw.local.filteredTeam.members[i] = users[i].name;&#xD;
	}</script>
                <isRule>false</isRule>
                <guid>7b4189c2-cf3f-4df4-953a-68ac28ad0210</guid>
                <versionId>34874e41-fbda-4d7f-91bd-bb030be7febd</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.d586b0a3-1397-4d46-8087-9ceddbb37af1</processItemId>
            <processId>1.97fd22db-6f52-40ce-95fc-a4ebf732dbf2</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.e4c7d28a-4194-4c41-baf7-69588aa46bdd</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:2a54785d0a22f63a:4ad54ef7:18949d884fe:-4bf0</guid>
            <versionId>67096a8b-398b-4d98-923f-5282742ff219</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.e4c7d28a-4194-4c41-baf7-69588aa46bdd</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>2bb1eabd-9d8f-437d-bb26-fb31b4b7088e</guid>
                <versionId>cb48ee96-6ee3-45a2-aca8-b5f2176c5b6b</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.cb93ddcb-0c61-4c70-8c1c-27777465be08</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="CAD filter service" id="1.97fd22db-6f52-40ce-95fc-a4ebf732dbf2" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification ns3:readOnlyOutputs="true">
                        
                        
                        <ns16:dataInput name="originalTeam" itemSubjectRef="itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0" isCollection="false" id="2055.bde0605f-cb9f-4d09-81f8-7f2f38ba7425" ns3:readOnly="true" />
                        
                        
                        <ns16:dataInput name="groupName" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.7a91bc03-0cd0-44f7-84f2-8ce59885ef21">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"BPM_CA_LRG_CORP_EXE_MKR"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="filteredTeam" itemSubjectRef="itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0" isCollection="false" id="2055.c29dfa37-a7ad-4ebb-896f-8ab8974c0013" ns3:readOnly="true" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.bde0605f-cb9f-4d09-81f8-7f2f38ba7425</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.7a91bc03-0cd0-44f7-84f2-8ce59885ef21</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="c1a2e70a-c909-42c7-8883-c280f4dab0ea">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="4d2537e2-5b80-4fc4-85c3-7fa6f99344cf" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>9080e559-11e5-4cf1-817f-c2c06538cd2b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>d586b0a3-1397-4d46-8087-9ceddbb37af1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>cb93ddcb-0c61-4c70-8c1c-27777465be08</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="9080e559-11e5-4cf1-817f-c2c06538cd2b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.7ccea82f-c281-4acb-82e0-5c590bde9eb7</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="9080e559-11e5-4cf1-817f-c2c06538cd2b" targetRef="cb93ddcb-0c61-4c70-8c1c-27777465be08" name="To Exclusive Gateway" id="2027.7ccea82f-c281-4acb-82e0-5c590bde9eb7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Get CAD team" id="cb93ddcb-0c61-4c70-8c1c-27777465be08">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="305" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.7ccea82f-c281-4acb-82e0-5c590bde9eb7</ns16:incoming>
                        
                        
                        <ns16:outgoing>8cfb8509-0725-49ff-8c1c-22cfc88f0a47</ns16:outgoing>
                        
                        
                        <ns16:script>//tw.local.originalTeam = new tw.object.Team();&#xD;
//tw.local.originalTeam.members = new tw.object.listOf.String();&#xD;
//tw.local.originalTeam.members[0] = "idchubexemkr01";&#xD;
//tw.local.originalTeam.members[1] = "bpmlrgexemkr11";&#xD;
//tw.local.originalTeam.members[2] = "idchubexemkr59";&#xD;
//tw.local.originalTeam.managerTeam = "Managers";&#xD;
//tw.local.originalTeam.name = "IDC EXE HUB";&#xD;
&#xD;
tw.local.filteredTeam = new tw.object.Team();&#xD;
tw.local.filteredTeam.members = new tw.object.listOf.String();&#xD;
tw.local.filteredTeam.managerTeam = tw.local.originalTeam.managerTeam;&#xD;
tw.local.filteredTeam.name = tw.local.originalTeam.name;&#xD;
&#xD;
var users = tw.system.org.findRoleByName(tw.local.groupName).allUsers;&#xD;
	&#xD;
log.info("users::: "+ users);&#xD;
	for (var i = 0; i &lt; users.listLength ; i++)&#xD;
	{&#xD;
		tw.local.filteredTeam.members[i] = users[i].name;&#xD;
	}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:endEvent name="End" id="d586b0a3-1397-4d46-8087-9ceddbb37af1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns3:endStateId>guid:2a54785d0a22f63a:4ad54ef7:18949d884fe:-4bf0</ns3:endStateId>
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>8cfb8509-0725-49ff-8c1c-22cfc88f0a47</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="cb93ddcb-0c61-4c70-8c1c-27777465be08" targetRef="d586b0a3-1397-4d46-8087-9ceddbb37af1" name="To End" id="8cfb8509-0725-49ff-8c1c-22cfc88f0a47">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.8cfb8509-0725-49ff-8c1c-22cfc88f0a47</processLinkId>
            <processId>1.97fd22db-6f52-40ce-95fc-a4ebf732dbf2</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.cb93ddcb-0c61-4c70-8c1c-27777465be08</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.d586b0a3-1397-4d46-8087-9ceddbb37af1</toProcessItemId>
            <guid>2a6d8929-f171-4c21-8af5-a8aef084236c</guid>
            <versionId>84a8c587-3dbe-4d45-96b8-5fbf24b17fef</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.cb93ddcb-0c61-4c70-8c1c-27777465be08</fromProcessItemId>
            <toProcessItemId>2025.d586b0a3-1397-4d46-8087-9ceddbb37af1</toProcessItemId>
        </link>
    </process>
</teamworks>

