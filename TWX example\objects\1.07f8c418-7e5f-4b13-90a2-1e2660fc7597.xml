<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.07f8c418-7e5f-4b13-90a2-1e2660fc7597" name="Attachment">
        <lastModified>1691050617644</lastModified>
        <lastModifiedBy>Nancy</lastModifiedBy>
        <processId>1.07f8c418-7e5f-4b13-90a2-1e2660fc7597</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.c6fe1983-fabb-425a-ae2c-7e3f6d770f6a</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>true</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>bc5bff4b-12e0-4cf2-a7cb-1d50f70549b6</guid>
        <versionId>dc656848-3a27-4cf3-bc0f-be7095712a50</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"mobileReady":[true],"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.10c1f782-ae1c-425d-8ee7-93d45dc764d1"],"isInterrupting":true,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":50,"y":200,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"14e5dfea-cc2d-41d1-9a31-f776a119d1b1"},{"incoming":["2027.966c2f3a-23c6-4258-9f9e-bc4c9437933d"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":700,"y":200,"declaredType":"TNodeVisualInfo","height":44}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"End","declaredType":"endEvent","id":"9c8ec8a4-0cf1-45ea-90d0-a1b968153e53"},{"targetRef":"2025.8492e6cb-8396-430a-b856-4af49145ab1d","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Attachment","declaredType":"sequenceFlow","id":"2027.10c1f782-ae1c-425d-8ee7-93d45dc764d1","sourceRef":"14e5dfea-cc2d-41d1-9a31-f776a119d1b1"},{"itemSubjectRef":"itm.12.b0e1b82e-1f8e-4ef7-b5da-2e07effdf361","name":"doc","isCollection":false,"declaredType":"dataObject","id":"2056.5b7ffc31-312f-4f50-9fa5-54154e5ad66a"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = [];\nautoObject[0] = {};\nautoObject[0].name = \"Customer Request\";\nautoObject[0].description = \"Customer Request\";\r\nautoObject[0].arabicName = \"\u0637\u0644\u0628 \u0627\u0644\u0639\u0645\u064a\u0644\" ;\r\n\r\nautoObject[1] = {};\r\nautoObject[1].name = \"Correspondent cover letter\";\r\nautoObject[1].description = \"Correspondent cover letter\";\r\nautoObject[1].arabicName = \"\u062e\u0637\u0627\u0628 \u0627\u0644\u0645\u0631\u0627\u0633\u0644\" ;\r\n\r\nautoObject[2] = {};\r\nautoObject[2].name = \"Invoice\";\r\nautoObject[2].description = \"Invoice\";\r\nautoObject[2].arabicName = \"\u0641\u0627\u062a\u0648\u0631\u0629\" ;\r\n\r\nautoObject[3] = {};\r\nautoObject[3].name = \"Transport document\";\r\nautoObject[3].description = \"Transport document\";\r\nautoObject[3].arabicName = \"\u0645\u0633\u062a\u0646\u062f \u0627\u0644\u0646\u0642\u0644\" ;\r\n\r\nautoObject[4] = {};\r\nautoObject[4].name = \"Packing list\";\r\nautoObject[4].description = \"Packing list\";\r\nautoObject[4].arabicName = \"\u0642\u0627\u0626\u0645\u0629 \u0627\u0644\u062a\u0639\u0628\u0626\u0629\" ;\r\n\r\nautoObject[5] = {};\r\nautoObject[5].name = \"Weight list\";\r\nautoObject[5].description = \"Weight list\"\r\nautoObject[5].arabicName = \"\u0642\u0627\u0626\u0645\u0629 \u0627\u0644\u0627\u0648\u0632\u0627\u0646\" ;\r\n\r\nautoObject[6] = {};\r\nautoObject[6].name = \"Certificate of origin\";\r\nautoObject[6].description = \"Certificate of origin\";\r\nautoObject[6].arabicName = \"\u0634\u0647\u0627\u062f\u0629 \u0627\u0644\u0645\u0646\u0634\u0623\" ;\r\n\r\nautoObject[7] = {};\r\nautoObject[7].name = \"Certificate of analysis\";\r\nautoObject[7].description = \"Certificate of analysis\";\r\nautoObject[7].arabicName = \"\u0634\u0647\u0627\u062f\u0629 \u0627\u0644\u062a\u062d\u0644\u064a\u0644\" ;\r\n\r\nautoObject[8] = {};\r\nautoObject[8].name = \"Inspection certificate\";\r\nautoObject[8].description = \"Inspection certificate\";\r\nautoObject[8].arabicName = \"\u0634\u0647\u0627\u062f\u0629 \u0627\u0644\u062a\u0641\u062a\u064a\u0634\" ;\r\n\r\nautoObject[9] = {};\r\nautoObject[9].name = \"Insurance policy\/certificate\";\r\nautoObject[9].description = \"Insurance policy\/certificate\";\r\nautoObject[9].arabicName = \"\u0634\u0647\u0627\u062f\u0629\/\u0628\u0648\u0644\u064a\u0635\u0629 \u0627\u0644\u062a\u0623\u0645\u064a\u0646\" ;\r\n\r\nautoObject[10] = {};\r\nautoObject[10].name = \"Bill of exchange\/draft\";\r\nautoObject[10].description = \"Bill of exchange\/draft\";\r\nautoObject[10].arabicName = \"\u0627\u0644\u0643\u0645\u0628\u064a\u0627\u0644\u0629\" ;\r\n\r\nautoObject[11] = {};\r\nautoObject[11].name = \"Compliance ticket\";\r\nautoObject[11].description = \"Compliance ticket\";\r\nautoObject[11].arabicName = \"\u0645\u0648\u0627\u0641\u0642\u0629 \u0627\u0644\u0625\u0644\u062a\u0632\u0627\u0645\" ;\r\n\r\nautoObject[12] = {};\r\nautoObject[12].name = \"Form 4\";\r\nautoObject[12].description = \"Form 4\";\r\nautoObject[12].arabicName = \"\u0646\u0645\u0648\u0630\u062c 4 \u0644\u0644\u0645\u0633\u062a\u0648\u0631\u062f\u064a\u0646\" ;\r\n\r\nautoObject[13] = {};\r\nautoObject[13].name = \"Customs Letter\";\r\nautoObject[13].description = \"Customs Letter\";\r\nautoObject[13].arabicName = \"\u062e\u0637\u0627\u0628 \u0627\u0644\u062c\u0645\u0627\u0631\u0643\" ;\nautoObject"}]},"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"attachment","isCollection":true,"declaredType":"dataObject","id":"2056.9ce49713-e3b7-4819-9238-d35ffcadcfef"},{"outgoing":["2027.966c2f3a-23c6-4258-9f9e-bc4c9437933d","2027.5f636d63-83ae-44d4-9132-144d70e76b79"],"incoming":["2027.10c1f782-ae1c-425d-8ee7-93d45dc764d1","2027.81771828-0cdb-4628-bbc1-da1a5e1d0dd4"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":352,"y":177,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"contentBoxContrib":[{"contributions":[{"layoutItemId":"attach1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d9a2f1eb-8538-4cbb-8357-42bf7cb38ff5","optionName":"@label","value":"attach"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9f5379c1-3610-49ab-87e1-177dc233abc7","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1726ad85-ccc4-4433-8bcb-c6e05a591c7a","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3a43ee17-d876-49a5-8389-3463efb485c8","optionName":"canUpdate","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"477fc9c3-175e-4f29-88b1-34116e31de43","optionName":"canCreate","value":"true"}],"viewUUID":"64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7","binding":"tw.local.attachment[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"82cce0c3-4b11-445c-8d2e-bdc9014f06ce","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"7bbd3f14-7bb6-44b4-8205-33a44eb4a5ce"}],"layoutItemId":"DC_Templete1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"33313a93-4908-487c-8ed8-47a96aefc91a","optionName":"@label","value":"DC Templete"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f09cc469-d9f8-4887-896c-e7c0b9362353","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e28fe725-5b7d-4e01-8b8c-5ac444ad7588","optionName":"@labelVisibility","value":"SHOW"}],"viewUUID":"64.f9e6899d-e7d7-4296-ba71-268fcd57e296","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"27ab3c8a-d7b0-4c84-8ea9-a99d447f895a","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"Attachment","isForCompensation":false,"completionQuantity":1,"id":"2025.8492e6cb-8396-430a-b856-4af49145ab1d"},{"targetRef":"9c8ec8a4-0cf1-45ea-90d0-a1b968153e53","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"8995401a-a42b-4957-894f-0839c94c9396","coachEventPath":"DC_Templete1\/submit"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.966c2f3a-23c6-4258-9f9e-bc4c9437933d","sourceRef":"2025.8492e6cb-8396-430a-b856-4af49145ab1d"},{"outgoing":["2027.81771828-0cdb-4628-bbc1-da1a5e1d0dd4"],"incoming":["2027.5f636d63-83ae-44d4-9132-144d70e76b79"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition"}],"extensionElements":{"default":["2027.81771828-0cdb-4628-bbc1-da1a5e1d0dd4"],"nodeVisualInfo":[{"width":24,"x":338,"y":70,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page","declaredType":"intermediateThrowEvent","id":"2025.06e22552-ca2d-4529-a6f1-0f4090bdec06"},{"targetRef":"2025.06e22552-ca2d-4529-a6f1-0f4090bdec06","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"67978774-6862-4f54-82f4-b6eba2db55f7","coachEventPath":"DC_Templete1\/saveState"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topLeft","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To Stay on page","declaredType":"sequenceFlow","id":"2027.5f636d63-83ae-44d4-9132-144d70e76b79","sourceRef":"2025.8492e6cb-8396-430a-b856-4af49145ab1d"},{"targetRef":"2025.8492e6cb-8396-430a-b856-4af49145ab1d","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Attachment","declaredType":"sequenceFlow","id":"2027.81771828-0cdb-4628-bbc1-da1a5e1d0dd4","sourceRef":"2025.06e22552-ca2d-4529-a6f1-0f4090bdec06"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"095a67b7-6246-4b09-8a7a-adf0f5f04d4a","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"dd756b1c-2f43-4546-984c-6d49bdc5ad66","processType":"None"}],"exposedAs":["NotExposed"]},"implementation":"##unspecified","documentation":[{"textFormat":"text\/plain"}],"name":"Attachment","declaredType":"globalUserTask","id":"1.07f8c418-7e5f-4b13-90a2-1e2660fc7597","ioSpecification":{"inputSet":[{"id":"257e7e25-66e5-405c-b99d-263cee4846e0"}],"outputSet":[{"id":"c44afbd6-182e-480d-9e4a-da27355077f0"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"bb0fcbc8-9fb5-4e07-a65e-2d51abc7986d"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processVariable name="doc">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5b7ffc31-312f-4f50-9fa5-54154e5ad66a</processVariableId>
            <description isNull="true" />
            <processId>1.07f8c418-7e5f-4b13-90a2-1e2660fc7597</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.b0e1b82e-1f8e-4ef7-b5da-2e07effdf361</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5c16e945-0c07-4e05-9403-1a21899008c2</guid>
            <versionId>207ece67-b861-4727-ae03-03ffad2848dc</versionId>
        </processVariable>
        <processVariable name="attachment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9ce49713-e3b7-4819-9238-d35ffcadcfef</processVariableId>
            <description isNull="true" />
            <processId>1.07f8c418-7e5f-4b13-90a2-1e2660fc7597</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b680afda-5850-4bbf-8100-f3598ce7ba89</guid>
            <versionId>03a3380e-74f9-4b92-8383-59d07e7b3155</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c6fe1983-fabb-425a-ae2c-7e3f6d770f6a</processItemId>
            <processId>1.07f8c418-7e5f-4b13-90a2-1e2660fc7597</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.67df0531-db1f-46b6-9d60-f82d17f201c0</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-624a</guid>
            <versionId>394fb985-7506-4512-9a3d-5e7b0c4e1938</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.62d0f221-92a6-4281-ab12-2bbace2d3f7f</processItemId>
            <processId>1.07f8c418-7e5f-4b13-90a2-1e2660fc7597</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.78a79d25-dff5-4ed1-a6ca-92f630e55c05</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-624b</guid>
            <versionId>ba1febe0-7199-4e8a-a7a0-f1269de1ed43</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.78a79d25-dff5-4ed1-a6ca-92f630e55c05</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>bba2f1ae-f780-49cd-bd17-3638f8da5b39</guid>
                <versionId>193da6ad-836b-4325-a6f0-da89cf145144</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.c6fe1983-fabb-425a-ae2c-7e3f6d770f6a</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="bb0fcbc8-9fb5-4e07-a65e-2d51abc7986d" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript">
                
                
                <ns16:globalUserTask name="Attachment" id="1.07f8c418-7e5f-4b13-90a2-1e2660fc7597">
                    
                    
                    <ns16:documentation />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:userTaskImplementation id="dd756b1c-2f43-4546-984c-6d49bdc5ad66">
                            
                            
                            <ns16:startEvent name="Start" id="14e5dfea-cc2d-41d1-9a31-f776a119d1b1">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="50" y="200" width="24" height="24" color="#F8F8F8" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.10c1f782-ae1c-425d-8ee7-93d45dc764d1</ns16:outgoing>
                                
                            
                            </ns16:startEvent>
                            
                            
                            <ns16:endEvent name="End" id="9c8ec8a4-0cf1-45ea-90d0-a1b968153e53">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="700" y="200" width="24" height="44" color="#F8F8F8" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.966c2f3a-23c6-4258-9f9e-bc4c9437933d</ns16:incoming>
                                
                            
                            </ns16:endEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="14e5dfea-cc2d-41d1-9a31-f776a119d1b1" targetRef="2025.8492e6cb-8396-430a-b856-4af49145ab1d" name="To Attachment" id="2027.10c1f782-ae1c-425d-8ee7-93d45dc764d1">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.b0e1b82e-1f8e-4ef7-b5da-2e07effdf361" isCollection="false" name="doc" id="2056.5b7ffc31-312f-4f50-9fa5-54154e5ad66a" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" name="attachment" id="2056.9ce49713-e3b7-4819-9238-d35ffcadcfef">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">var autoObject = [];
autoObject[0] = {};
autoObject[0].name = "Customer Request";
autoObject[0].description = "Customer Request";&#xD;
autoObject[0].arabicName = "طلب العميل" ;&#xD;
&#xD;
autoObject[1] = {};&#xD;
autoObject[1].name = "Correspondent cover letter";&#xD;
autoObject[1].description = "Correspondent cover letter";&#xD;
autoObject[1].arabicName = "خطاب المراسل" ;&#xD;
&#xD;
autoObject[2] = {};&#xD;
autoObject[2].name = "Invoice";&#xD;
autoObject[2].description = "Invoice";&#xD;
autoObject[2].arabicName = "فاتورة" ;&#xD;
&#xD;
autoObject[3] = {};&#xD;
autoObject[3].name = "Transport document";&#xD;
autoObject[3].description = "Transport document";&#xD;
autoObject[3].arabicName = "مستند النقل" ;&#xD;
&#xD;
autoObject[4] = {};&#xD;
autoObject[4].name = "Packing list";&#xD;
autoObject[4].description = "Packing list";&#xD;
autoObject[4].arabicName = "قائمة التعبئة" ;&#xD;
&#xD;
autoObject[5] = {};&#xD;
autoObject[5].name = "Weight list";&#xD;
autoObject[5].description = "Weight list"&#xD;
autoObject[5].arabicName = "قائمة الاوزان" ;&#xD;
&#xD;
autoObject[6] = {};&#xD;
autoObject[6].name = "Certificate of origin";&#xD;
autoObject[6].description = "Certificate of origin";&#xD;
autoObject[6].arabicName = "شهادة المنشأ" ;&#xD;
&#xD;
autoObject[7] = {};&#xD;
autoObject[7].name = "Certificate of analysis";&#xD;
autoObject[7].description = "Certificate of analysis";&#xD;
autoObject[7].arabicName = "شهادة التحليل" ;&#xD;
&#xD;
autoObject[8] = {};&#xD;
autoObject[8].name = "Inspection certificate";&#xD;
autoObject[8].description = "Inspection certificate";&#xD;
autoObject[8].arabicName = "شهادة التفتيش" ;&#xD;
&#xD;
autoObject[9] = {};&#xD;
autoObject[9].name = "Insurance policy/certificate";&#xD;
autoObject[9].description = "Insurance policy/certificate";&#xD;
autoObject[9].arabicName = "شهادة/بوليصة التأمين" ;&#xD;
&#xD;
autoObject[10] = {};&#xD;
autoObject[10].name = "Bill of exchange/draft";&#xD;
autoObject[10].description = "Bill of exchange/draft";&#xD;
autoObject[10].arabicName = "الكمبيالة" ;&#xD;
&#xD;
autoObject[11] = {};&#xD;
autoObject[11].name = "Compliance ticket";&#xD;
autoObject[11].description = "Compliance ticket";&#xD;
autoObject[11].arabicName = "موافقة الإلتزام" ;&#xD;
&#xD;
autoObject[12] = {};&#xD;
autoObject[12].name = "Form 4";&#xD;
autoObject[12].description = "Form 4";&#xD;
autoObject[12].arabicName = "نموذج 4 للمستوردين" ;&#xD;
&#xD;
autoObject[13] = {};&#xD;
autoObject[13].name = "Customs Letter";&#xD;
autoObject[13].description = "Customs Letter";&#xD;
autoObject[13].arabicName = "خطاب الجمارك" ;
autoObject</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns3:formTask name="Attachment" id="2025.8492e6cb-8396-430a-b856-4af49145ab1d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="352" y="177" width="95" height="70" />
                                    
                                    
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.10c1f782-ae1c-425d-8ee7-93d45dc764d1</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.81771828-0cdb-4628-bbc1-da1a5e1d0dd4</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.966c2f3a-23c6-4258-9f9e-bc4c9437933d</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.5f636d63-83ae-44d4-9132-144d70e76b79</ns16:outgoing>
                                
                                
                                <ns3:formDefinition>
                                    
                                    
                                    <ns19:coachDefinition>
                                        
                                        
                                        <ns19:layout>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>27ab3c8a-d7b0-4c84-8ea9-a99d447f895a</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>DC_Templete1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>33313a93-4908-487c-8ed8-47a96aefc91a</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>DC Templete</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>f09cc469-d9f8-4887-896c-e7c0b9362353</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>e28fe725-5b7d-4e01-8b8c-5ac444ad7588</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>SHOW</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</ns19:viewUUID>
                                                
                                                
                                                <ns19:contentBoxContrib>
                                                    
                                                    
                                                    <ns19:id>7bbd3f14-7bb6-44b4-8205-33a44eb4a5ce</ns19:id>
                                                    
                                                    
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    
                                                    
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        
                                                        
                                                        <ns19:id>82cce0c3-4b11-445c-8d2e-bdc9014f06ce</ns19:id>
                                                        
                                                        
                                                        <ns19:layoutItemId>attach1</ns19:layoutItemId>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>d9a2f1eb-8538-4cbb-8357-42bf7cb38ff5</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>attach</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>9f5379c1-3610-49ab-87e1-177dc233abc7</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value />
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>1726ad85-ccc4-4433-8bcb-c6e05a591c7a</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>SHOW</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>3a43ee17-d876-49a5-8389-3463efb485c8</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>canUpdate</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>true</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>477fc9c3-175e-4f29-88b1-34116e31de43</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>canCreate</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>true</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:viewUUID>64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7</ns19:viewUUID>
                                                        
                                                        
                                                        <ns19:binding>tw.local.attachment[]</ns19:binding>
                                                        
                                                    
                                                    </ns19:contributions>
                                                    
                                                
                                                </ns19:contentBoxContrib>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                        
                                        </ns19:layout>
                                        
                                    
                                    </ns19:coachDefinition>
                                    
                                
                                </ns3:formDefinition>
                                
                            
                            </ns3:formTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.8492e6cb-8396-430a-b856-4af49145ab1d" targetRef="9c8ec8a4-0cf1-45ea-90d0-a1b968153e53" name="To End" id="2027.966c2f3a-23c6-4258-9f9e-bc4c9437933d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="8995401a-a42b-4957-894f-0839c94c9396">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/submit</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page" id="2025.06e22552-ca2d-4529-a6f1-0f4090bdec06">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="338" y="70" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                    
                                    <ns3:default>2027.81771828-0cdb-4628-bbc1-da1a5e1d0dd4</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.5f636d63-83ae-44d4-9132-144d70e76b79</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.81771828-0cdb-4628-bbc1-da1a5e1d0dd4</ns16:outgoing>
                                
                                
                                <ns3:postponeTaskEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.8492e6cb-8396-430a-b856-4af49145ab1d" targetRef="2025.06e22552-ca2d-4529-a6f1-0f4090bdec06" name="To Stay on page" id="2027.5f636d63-83ae-44d4-9132-144d70e76b79">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topLeft</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="67978774-6862-4f54-82f4-b6eba2db55f7">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/saveState</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.06e22552-ca2d-4529-a6f1-0f4090bdec06" targetRef="2025.8492e6cb-8396-430a-b856-4af49145ab1d" name="To Attachment" id="2027.81771828-0cdb-4628-bbc1-da1a5e1d0dd4">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns3:htmlHeaderTag id="095a67b7-6246-4b09-8a7a-adf0f5f04d4a">
                                
                                
                                <ns3:tagName>viewport</ns3:tagName>
                                
                                
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                
                                
                                <ns3:enabled>true</ns3:enabled>
                                
                            
                            </ns3:htmlHeaderTag>
                            
                        
                        </ns3:userTaskImplementation>
                        
                        
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                        
                        
                        <ns3:mobileReady>true</ns3:mobileReady>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:inputSet id="257e7e25-66e5-405c-b99d-263cee4846e0" />
                        
                        
                        <ns16:outputSet id="c44afbd6-182e-480d-9e4a-da27355077f0" />
                        
                    
                    </ns16:ioSpecification>
                    
                
                </ns16:globalUserTask>
                
            
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.3d744cfa-f6ec-4567-8daf-de643b956059</processLinkId>
            <processId>1.07f8c418-7e5f-4b13-90a2-1e2660fc7597</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.c6fe1983-fabb-425a-ae2c-7e3f6d770f6a</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.62d0f221-92a6-4281-ab12-2bbace2d3f7f</toProcessItemId>
            <guid>6c05efef-1b67-48df-956f-8b952ccd144a</guid>
            <versionId>7d1d6fd8-85a0-4497-9a56-982f55e34d1a</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.c6fe1983-fabb-425a-ae2c-7e3f6d770f6a</fromProcessItemId>
            <toProcessItemId>2025.62d0f221-92a6-4281-ab12-2bbace2d3f7f</toProcessItemId>
        </link>
    </process>
</teamworks>

