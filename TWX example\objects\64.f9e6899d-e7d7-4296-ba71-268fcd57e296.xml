<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.f9e6899d-e7d7-4296-ba71-268fcd57e296" name="DC Templete">
        <lastModified>1714999316505</lastModified>
        <lastModifiedBy>bawadmin</lastModifiedBy>
        <coachViewId>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</coachViewId>
        <isTemplate>true</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;bd85072d-7c74-4d39-8ce1-037a06009e0d&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Header_View1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9b5f4bc3-8427-4832-852a-c8f48faf98dd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Header View&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;de5c6869-6947-4e71-8962-b6bcabb8da57&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;716e5467-6ca2-436c-8542-6799aa48ff5f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.55afafe5-9321-40fd-9b9a-cbbd556a8005&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.appinfo&lt;/ns2:binding&gt;&lt;/ns2:layoutItem&gt;&lt;ns2:layoutItem xsi:type="ns2:GridLayoutContainer" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;fe6673cd-c9c8-4352-8f2b-2a116a7db739&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer4&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1cf5e062-c534-4392-8835-84a350a1a43b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;e3ed836d-8431-481b-80ec-fdae3f7cc017&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer5&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ae7c2394-eca4-4d90-8d79-8cff10979f82&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;HORIZONTAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;124b173b-a571-4d10-8953-301c7d6cd4c2&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell4&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;049efd81-5559-4859-843d-93cc8c9f55b1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":1},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;396d58d9-b954-46d4-81c7-c0432739407f&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1e0aad31-6390-44b5-8568-10911f4e9a93&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":10},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;51501f53-9c8b-4682-8ad8-c8eedf9afbf3&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell5&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c7706d44-f248-41d5-882a-d79a986a3324&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":10},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ContentBox" version="8550"&gt;&lt;ns2:id&gt;65288874-0c0e-4639-8826-1e82d0ad9be9&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;ContentBox1&lt;/ns2:layoutItemId&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;818132d7-6df1-4a6f-8c51-d4c06ff13f1c&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer10&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3785c241-3eee-4e5c-883d-08c2a03cafed&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":10}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;HORIZONTAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;7e05f33b-3438-424b-8c35-6d4c05136fba&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell10&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7d6bd728-83b0-4ff7-817d-fcff3ad02e70&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":5},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;562562a2-cd02-4d7d-8674-82ba8619211a&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;validationTabs&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a4a8c1c9-2b70-4813-893a-23cd0393e283&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Please check these Tabs&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cb5d0b43-92fe-4eb4-82da-c6166e89655d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;99452d4a-ca5b-4e7b-850e-b1ec8696fdd3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;HIDE&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4b82c3c3-b50e-4822-81ef-917ed15f204b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;allowHTML&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4b652db7-9186-4e17-8ef4-154d5b54d290&lt;/ns2:id&gt;&lt;ns2:optionName&gt;textAlignment&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"L"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1cf78e52-e08b-454e-83f6-ec1f76c11acb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ceb72097-6929-49a2-8562-48c50ad8df4d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"L"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.f634f22e-7800-4bd7-9f1e-87177acfb3bc&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.successMessage&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;edd64fc6-86f0-4442-81d9-e0b5fe752092&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Single_Select1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4fe9f100-6e4a-4dbf-8288-e8a6b5ba7f52&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Action&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;76d3fb63-3d19-47fc-8351-d791142f7354&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;819aac2d-543d-4f83-80bf-67cac93a235b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a9998b2c-779a-4b6d-8336-981a8b8fa65e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;B&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;db9f7924-823a-4005-8074-0973556d946e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.action[]&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8d4f54b6-80a2-42f7-8915-2e8e3de139d4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.showReturnReason();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;569361a0-4aea-4262-8ce0-dc46166e89d2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.showAction();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7adb835a-3d1b-4b82-86e3-a7c2636dcff1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;REQUIRED&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.selectedAction&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;13fadc07-8b84-4822-8639-c33cebcff6cc&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;ReturnReason&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6b20c52d-822e-494a-a71a-53f096c8626f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2a09518f-8ada-4e96-8f5a-41bf217d07a9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Return Reason&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5b22360a-ac69-41ac-8257-a40a6eeb3a61&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;86ed6434-1b34-4fe6-8472-e173bd22d304&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0c67a33c-f7de-44d4-891d-fc058ebaf700&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.showReturnReason();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6f82fa2d-478f-495b-85c7-efbbc22dfce0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length &amp;gt; 80)
{
	me.setValid(false , "max lenght is 80 characters");
	return false;
}
else
{
	me.setValid(true);
	return true;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.stepLog.returnReason&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;be25980b-695e-47db-8af9-89dba9aacff4&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell11&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e6e7a8eb-3bd7-4ae9-806f-6bda271b6c59&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":5},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;2e8e65d6-3296-455d-8ff0-25479ec37027&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Text1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;294b0b85-655f-4b3a-ad05-d48c1287713c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;aba44115-a7ad-4b25-85da-5c5395beac2f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Comment&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3445cd08-f5df-42a8-8378-5b0535b0c506&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;17d5d4c2-5bf3-40f3-8f6b-2ea6258972c2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a5d8a98e-5074-4881-8f35-99556f29c6c0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;printOverflow&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;808b4d61-24fd-4b96-88c8-39f9c18a097b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"D"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7ee911cf-0aad-4b6b-8c3d-8950b5836cc6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;height&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.0e61869e-73fd-4401-b156-8c11adaec3f8&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.stepLog.comment&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;b114a2fb-dcde-4edd-8bec-7b8c3f52c290&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;approvalsLayout&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1ae4951c-f060-49ab-8e79-7209462eb8b0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Approvals layout&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ea78be84-f765-42b6-84cb-de13433fc541&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;06f2e2a2-79a0-4278-8a7d-cb2a29d55b74&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e83e16d3-f99e-42ee-8052-0455574ceea3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.showApprovals();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;&lt;/ns2:binding&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;f9716740-b4b0-4263-8fd8-392468750e06&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;d0587aeb-8851-4b44-806d-c43018eec3a1&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Check_box1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e0fe3770-e5c1-423b-8054-eaaa0903eb79&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;CAD&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2e828aef-45f3-4ac4-8104-b374911ba40b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b61f07ec-db62-45f8-8506-bb517c58db8f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c10b913c-d87d-4e77-8476-cafafc159280&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fffd1628-baee-44e8-b7ca-5ae48644b0be&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.approvals.CAD&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;21d47f21-36eb-4e08-8f20-1bdd30ca9a0d&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Check_box2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;14080757-c572-45c3-800a-ad02b5473040&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Treasury&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bb06eebc-ea67-4bc8-82b6-866726f0875c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1c67f2b0-c522-41ff-8345-523419d5bc3f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;284b0116-7e97-4d8a-860c-37c1016ead93&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fffd1628-baee-44e8-b7ca-5ae48644b0be&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.approvals.treasury&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;6b1e4648-ff54-4ad0-8675-aee4e5ac3bf9&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Check_box3&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;419eda84-b749-4826-8566-82e72cd41e3d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Compliance&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3271ef37-3f4c-4c43-8170-48184f1fdce2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9a2bd5a8-5285-450f-8bda-65c660d77c33&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7f5028b2-9dab-40bc-8eee-7878812eaf84&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fffd1628-baee-44e8-b7ca-5ae48644b0be&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.approvals.compliance&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;21047ee9-60eb-49a9-8193-62009fe71b7d&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell3&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b4afb8d5-ef4c-4788-8c01-748ff769fa08&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":10},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;4fedd362-60df-4612-826b-321a7561626f&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Validation_Helper1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c7ba6b21-a4bb-4e2c-8e76-529a2946fd47&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Validation Helper&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5a0442ba-1ee1-46db-88c1-37b8e43400c4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8cd925e0-9089-47f0-88d5-2d891a6ac4a4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5c06e58b-32f8-4462-8b6d-0d6b57429f7d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;runTimeValid&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5b493bae-43c6-489f-8fbb-b20ea1fb6ffb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;stop&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;89f1266f-1016-4a70-827f-b93417447ba0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;disableSubmit&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d1c416ef-66f2-46ef-8f7c-e1b00b34f605&lt;/ns2:id&gt;&lt;ns2:optionName&gt;panelLabel&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Validation Section&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e3a63476-379e-4c93-a654-3f160c4ac76d&lt;/ns2:viewUUID&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;be2cafb6-c223-4fc7-809e-fd7938dc0e74&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;91f558d3-ef2c-468c-8781-b6b2369b19fc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":10},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a2839375-fe5c-4188-8888-5b88dc366d75&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalAlignment&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"RIGHT"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;HORIZONTAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;623252a0-d638-4d98-8cb3-40d132d9ae70&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;saveState&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;09b4a9cf-d33f-4bd0-86fa-5818b1067b69&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Save&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6566ca05-0e69-43b4-8d60-9b4cfd4b067c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a6d1fb2d-845e-43bf-8e71-b99980d9f9d5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cb790568-a5b5-4a3d-82ad-c4385dd41715&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;B&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;95aba975-8cb9-4e89-84dd-ae1b5a5a6184&lt;/ns2:id&gt;&lt;ns2:optionName&gt;shapeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;R&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;55cc9e36-a88d-4b4e-850f-7bd0f62ef497&lt;/ns2:id&gt;&lt;ns2:optionName&gt;iconLocation&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;068aa8c7-137f-4a31-8978-2d79d1f87cd7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;icon&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1e318248-2bb5-4c28-8b97-e39cd86ccc10&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"D"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;75c6ea30-9f81-48b5-8ec1-bde91fc9433d&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;checkPendingCasesBtn&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bc765f7a-9082-4c66-8148-37cf1776c234&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Check pending cases under Facilities&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c992a2bf-a778-4ef3-86bd-c790694266d1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8e641083-e55f-4604-8437-02fe93f44cc5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d6396aa3-e4a0-4342-8565-e36fff19f33d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;shapeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;R&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;866fb7f8-7230-4016-83f3-9a4f3679252f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;613a3b09-513d-4c0c-8d06-0f3a57007556&lt;/ns2:id&gt;&lt;ns2:optionName&gt;outline&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7bdcff03-76b9-4ab4-8143-aeb81dc39074&lt;/ns2:id&gt;&lt;ns2:optionName&gt;ghostMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7ee1b264-f546-4f2c-8a4e-72a3fd805da5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;iconLocation&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"R"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8b8d7d58-7f39-4412-89fc-2c1a0d3d1b17&lt;/ns2:id&gt;&lt;ns2:optionName&gt;icon&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":""}]}&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;88ea2af8-9768-4825-8c74-e8c7d3b61a71&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"D"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;95976b0d-dca4-48a1-87a2-0efdb0d75eb7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CLICK&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//view.showBookedFacility(8);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;351de0b7-6c37-4db3-88cf-cd2393baae6e&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;submit&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b8165fc6-2bef-4600-8d17-cbbd14a62ae4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.buttonName&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7dde2648-1619-40fe-8eb1-a86ab6de6ffc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;54a08579-6fcb-451b-8857-3c06eac16d7a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8846fc24-88ff-470c-88f7-d9102a70ec54&lt;/ns2:id&gt;&lt;ns2:optionName&gt;shapeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;R&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0c1af7d4-71cb-46f6-8a29-1729f3979264&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a0537c1a-6bf4-4e22-8b43-24c1b67b92ed&lt;/ns2:id&gt;&lt;ns2:optionName&gt;outline&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3b870918-00ef-45a2-8f58-5a68bd769365&lt;/ns2:id&gt;&lt;ns2:optionName&gt;ghostMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;71d8c93f-ecd5-4ffe-8d1e-9b40040ab7ab&lt;/ns2:id&gt;&lt;ns2:optionName&gt;iconLocation&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"R"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;38404cbc-eb83-496f-89e4-4914602bbfa4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;icon&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":""}]}&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cc104e69-5831-4c95-8451-1eae4fec1e35&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"D"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;e176fd02-0527-40f8-8cae-8af1abb3ca64&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell6&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e6ffa7a3-900c-49ba-8ed0-6b308af71be8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":1},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>true</isMobileReady>
        <loadJsFunction>//if(this.context.options.isCAD.get("value") == false){&#xD;
//	this.ui.get("checkPendingCasesBtn").setVisible(false,true);&#xD;
//}else{&#xD;
//	this.ui.get("checkPendingCasesBtn").setVisible(true,true);&#xD;
//}&#xD;
&#xD;
//alert(this.context.viewid);&#xD;
&#xD;
</loadJsFunction>
        <unloadJsFunction isNull="true" />
        <viewJsFunction isNull="true" />
        <changeJsFunction>//clear();&#xD;
//this.context.options.invalidTabs.get("value").items.forEach( function(index){&#xD;
//	validateTab(index);&#xD;
//});</changeJsFunction>
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction isNull="true" />
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>4a874502-f4d4-431d-b9f9-ff5f299d97a8</guid>
        <versionId>a1e6cd50-2ba5-41c9-988d-5798953e1273</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <bindingType name="appinfo">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewBindingTypeId>65.cc17c024-5345-40a5-8615-40cc12e6ff21</coachViewBindingTypeId>
            <coachViewId>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</coachViewId>
            <isList>false</isList>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.e7100a5a-462f-4f06-a0ea-5f9c5b209bd6</classId>
            <seq>0</seq>
            <description isNull="true" />
            <guid>d7a01bc6-89f1-4ca3-b31d-7c69cb7eba3c</guid>
            <versionId>52a34d2e-c1e7-4171-ad77-f7cda75b22b5</versionId>
        </bindingType>
        <configOption name="buttonName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.62106567-d723-4e3e-b6bf-a403be80e8f6</coachViewConfigOptionId>
            <coachViewId>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>0</seq>
            <description></description>
            <groupName></groupName>
            <guid>ccb04285-b37c-4952-8575-92cbbf2d32e5</guid>
            <versionId>5fcf8281-ec2e-446b-a483-3b664362c7e6</versionId>
        </configOption>
        <configOption name="hasApprovals">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.489f1bdd-d5e4-41a9-bd53-d886df2534b4</coachViewConfigOptionId>
            <coachViewId>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>1</seq>
            <description></description>
            <groupName></groupName>
            <guid>6f04c250-a598-4ecc-957b-be9c9026b5c3</guid>
            <versionId>c3df5b94-3d6f-4ffd-ada4-2b8b1b008ea2</versionId>
        </configOption>
        <configOption name="hasReturnReason">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.aeb1048f-d826-4002-bfb9-************</coachViewConfigOptionId>
            <coachViewId>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>2</seq>
            <description></description>
            <groupName></groupName>
            <guid>fad94f4a-**************-413ef165316d</guid>
            <versionId>73abcef1-4d26-4ef7-af54-d3d535fd88e3</versionId>
        </configOption>
        <configOption name="approvals">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.dbb1991a-c233-4118-8e71-b14b0ebe9b34</coachViewConfigOptionId>
            <coachViewId>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>/12.e9e590bc-d1fd-48b5-9733-9a6240af3e5c</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>3</seq>
            <description></description>
            <groupName></groupName>
            <guid>4f368ffe-92ac-45b2-9dd0-7a41763f7b37</guid>
            <versionId>3da3a9f4-4a19-4640-aed8-2a2986dd0388</versionId>
        </configOption>
        <configOption name="stepLog">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.2fb4f4fb-631c-4083-994c-3150764ff06b</coachViewConfigOptionId>
            <coachViewId>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>4</seq>
            <description></description>
            <groupName></groupName>
            <guid>0de287fd-1d77-49f5-a8d8-4c25f0c08bcf</guid>
            <versionId>45dee8f8-3180-47ab-b850-5f82a576b7de</versionId>
        </configOption>
        <configOption name="action">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.dafd4f5f-3e9c-412f-a936-86797c698038</coachViewConfigOptionId>
            <coachViewId>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</coachViewId>
            <isList>true</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>5</seq>
            <description></description>
            <groupName></groupName>
            <guid>51e6d9bb-7c53-455f-b31f-c10a1094839a</guid>
            <versionId>bfcc5ac4-aec6-41d8-876b-055f267730ca</versionId>
        </configOption>
        <configOption name="selectedAction">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.30cbac0c-d619-4581-88c6-641c9bade5c7</coachViewConfigOptionId>
            <coachViewId>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>6</seq>
            <description></description>
            <groupName></groupName>
            <guid>bea296cc-fd6c-42ee-b9c6-5f70b1a9ed8f</guid>
            <versionId>846fb0a8-6c4d-4005-8eed-c697812fd113</versionId>
        </configOption>
        <configOption name="approvalsReadOnly">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.753d2352-af64-449c-922c-7ff7543d4ca1</coachViewConfigOptionId>
            <coachViewId>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>7</seq>
            <description></description>
            <groupName></groupName>
            <guid>62fa5566-c73a-4fd1-8369-229689b21d7d</guid>
            <versionId>91d0bc63-973d-4955-b08e-3e904a1afa97</versionId>
        </configOption>
        <configOption name="invalidTabs">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.bbb7e259-dac6-48a6-b6ff-4c3a68f07179</coachViewConfigOptionId>
            <coachViewId>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</coachViewId>
            <isList>true</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>8</seq>
            <description></description>
            <groupName></groupName>
            <guid>b3776566-e48d-40d2-8c02-87379f43045a</guid>
            <versionId>92149252-d7a4-4064-a8ca-2d6f78ddfec2</versionId>
        </configOption>
        <configOption name="validationMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.8ab25ab6-07f4-4eeb-8c5e-d9ca37611ece</coachViewConfigOptionId>
            <coachViewId>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>9</seq>
            <description></description>
            <groupName></groupName>
            <guid>9ecccda6-5e8f-4752-af39-f1cb84386833</guid>
            <versionId>898d143e-703b-4d35-8274-1f2d403516e7</versionId>
        </configOption>
        <configOption name="isCAD">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.c9c65ffe-726d-452a-9045-8f7b728c7e50</coachViewConfigOptionId>
            <coachViewId>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>10</seq>
            <description></description>
            <groupName></groupName>
            <guid>bd7200bd-e3cd-4ea3-87bd-084fd5f62169</guid>
            <versionId>b60cd65f-a197-447c-b1b1-d34d4cce7246</versionId>
        </configOption>
        <configOption name="successMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.ee5f43b9-4de2-4652-8412-399321a34075</coachViewConfigOptionId>
            <coachViewId>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>11</seq>
            <description></description>
            <groupName></groupName>
            <guid>72850626-739f-427d-8b4b-b6556b66f544</guid>
            <versionId>a05c536e-aeab-4615-a94a-776f6c8589be</versionId>
        </configOption>
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.c6aeff37-290a-4092-967b-cf8c1a650d1e</coachViewInlineScriptId>
            <coachViewId>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock>this.showApprovals = function  () {&#xD;
	if (this.context.options.hasApprovals.get("value")) {&#xD;
		this.ui.get("approvalsLayout").setVisible(true,true);&#xD;
		if (this.context.options.approvalsReadOnly.get("value")) {&#xD;
			this.ui.get("approvalsLayout").setEnabled(false);&#xD;
		}else{&#xD;
			this.ui.get("approvalsLayout").setEnabled(true);&#xD;
		}&#xD;
&#xD;
	}else{&#xD;
&#xD;
		this.ui.get("approvalsLayout").setVisible(false,true);&#xD;
	}&#xD;
	&#xD;
}&#xD;
&#xD;
this.showReturnReason = function  () {&#xD;
	if (this.context.options.hasReturnReason.get("value") &amp;&amp; ((this.context.options.selectedAction.get("value") == "Return To Trade FO") || (this.context.options.selectedAction.get("value") == "Return to Initiator") || (this.context.options.selectedAction.get("value") == "Return To Maker"))) {&#xD;
		this.ui.get("ReturnReason").setVisible(true,true);&#xD;
	}else{&#xD;
		this.ui.get("ReturnReason").setVisible(false,true);&#xD;
	}&#xD;
}&#xD;
&#xD;
this.showAction = function  () {&#xD;
	if (this.context.binding.get("value").get("subStatus") == "") {&#xD;
		this.ui.get("Single_Select1").setVisible(false,true);&#xD;
		this.ui.get("Text1").setVisible(false,true);&#xD;
	}&#xD;
}&#xD;
&#xD;
this.showBookedFacility = function(tabId){&#xD;
	  // Remove the "active" class from all tabs&#xD;
//  var tabs = document.getElementsByClassName("tab");&#xD;
//  for (var i = 0; i &lt; tabs.length; i++) {&#xD;
//    tabs[i].classList.remove("active");&#xD;
//  }&#xD;
  &#xD;
  // Add the "active" class to the selected tab&#xD;
  var selectedTab = document.getElementById(tabId);&#xD;
  selectedTab.classList.add("active");&#xD;
}&#xD;
</scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>e2f80039-9214-4353-b1b7-9c5a63e39038</guid>
            <versionId>72b739d4-bfce-44c2-81d2-0acbc2c9b571</versionId>
        </inlineScript>
    </coachView>
</teamworks>

