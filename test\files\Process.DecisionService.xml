<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.bdd3fc6b-5583-46c6-9ddf-91408b409b0f" name="Decision1">
        <lastModified>1569969383907</lastModified>
        <lastModifiedBy>t99kmg07</lastModifiedBy>
        <processId>1.bdd3fc6b-5583-46c6-9ddf-91408b409b0f</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId isNull="true" />
        <isRootProcess>false</isRootProcess>
        <processType>1</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType isNull="true" />
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:7903df9268eede95:-3c68ec8a:16d897292b6:-7ff1</guid>
        <versionId>64a4e2ac-e566-4ae6-9cf4-751b218e0ca2</versionId>
        <dependencySummary isNull="true" />
        <jsonData isNull="true" />
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.e5e12801-e860-44ff-928b-ac5644d4697a</processItemId>
            <processId>1.bdd3fc6b-5583-46c6-9ddf-91408b409b0f</processId>
            <name>Terminar</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.d1f2d63b-413e-4c53-b472-42a9a9e0d72d</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7903df9268eede95:-3c68ec8a:16d897292b6:-7ff0</guid>
            <versionId>8b836529-3649-4e31-9802-2a6e64a4f25d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="700" y="100">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.d1f2d63b-413e-4c53-b472-42a9a9e0d72d</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>b2158444-e616-4367-9ce6-284d3a1a84c0</guid>
                <versionId>b46d35ec-201a-4447-9560-4b31518c0fd0</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId isNull="true" />
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="20" y="100">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
    </process>
</teamworks>

