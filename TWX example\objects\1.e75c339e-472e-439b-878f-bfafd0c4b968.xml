<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.e75c339e-472e-439b-878f-bfafd0c4b968" name="Update IDC Request">
        <lastModified>1692714190009</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.e75c339e-472e-439b-878f-bfafd0c4b968</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.08188f42-8534-47c7-83a5-bf60b20530c5</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:e5873b94e72cc4db:-66d3a9fb:189cfb43e32:-7714</guid>
        <versionId>6719e525-cf7b-433e-bdeb-8fac927dd57f</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:46ddb3fbf22991c0:-266dfb7a:18a1cd30310:763f" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.466e771e-c0a3-4f39-8638-7921cf440833"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"1c48126d-f842-4cc4-88b7-e379c0afbac0"},{"incoming":["01020eac-7ccd-4b18-81f0-2e98c68bc117"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":1190,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:e5873b94e72cc4db:-66d3a9fb:189cfb43e32:-7712"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"360f095c-c838-407d-8326-f25a1a115a02"},{"targetRef":"08188f42-8534-47c7-83a5-bf60b20530c5","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To UPDATE IDC Request Q","declaredType":"sequenceFlow","id":"2027.466e771e-c0a3-4f39-8638-7921cf440833","sourceRef":"1c48126d-f842-4cc4-88b7-e379c0afbac0"},{"startQuantity":1,"outgoing":["c2c0641c-c534-46ee-821f-928b01692f0c"],"incoming":["3f70aa03-14cd-4098-811e-ef15c12abae9"],"extensionElements":{"nodeVisualInfo":[{"color":"#FFC875","width":95,"x":260,"y":56,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"UPDATE IDC Request","dataInputAssociation":[{"targetRef":"2055.a634f531-c979-476c-91db-a17bf5e55c90","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE"]}}]},{"targetRef":"2055.9695b715-db44-410b-8a74-65cf1d31d8ab","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","declaredType":"TFormalExpression","content":["tw.local.sqlStatements"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"485c1cfa-b21c-4f9b-8c38-408c70d91afd","calledElement":"1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7"},{"targetRef":"8c791fc3-3fec-4c45-8b19-58d174daab20","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To DELETE Q","declaredType":"sequenceFlow","id":"c2c0641c-c534-46ee-821f-928b01692f0c","sourceRef":"485c1cfa-b21c-4f9b-8c38-408c70d91afd"},{"startQuantity":1,"outgoing":["26923925-0f09-4da5-8564-ee592a52a840"],"incoming":["1a3ded6b-baee-47a8-856f-acab7c1c6667"],"extensionElements":{"nodeVisualInfo":[{"color":"#FFC875","width":95,"x":576,"y":56,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"DELETE Invoices &amp; Bills","dataInputAssociation":[{"targetRef":"2055.a634f531-c979-476c-91db-a17bf5e55c90","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE"]}}]},{"targetRef":"2055.9695b715-db44-410b-8a74-65cf1d31d8ab","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","declaredType":"TFormalExpression","content":["tw.local.sqlStatements"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"a8fb1bfb-58c7-4ce7-8f29-137efb1dc5d9","calledElement":"1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7"},{"targetRef":"6322d481-5d75-47ff-8b5f-56cada0079b8","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To INSERT Q","declaredType":"sequenceFlow","id":"26923925-0f09-4da5-8564-ee592a52a840","sourceRef":"a8fb1bfb-58c7-4ce7-8f29-137efb1dc5d9"},{"startQuantity":1,"outgoing":["01020eac-7ccd-4b18-81f0-2e98c68bc117"],"incoming":["02052b06-180c-4b80-86e4-2c397f656ac3"],"extensionElements":{"nodeVisualInfo":[{"color":"#FFC875","width":95,"x":929,"y":56,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"INSERT Invoices &amp; Bills","dataInputAssociation":[{"targetRef":"2055.a634f531-c979-476c-91db-a17bf5e55c90","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE"]}}]},{"targetRef":"2055.9695b715-db44-410b-8a74-65cf1d31d8ab","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","declaredType":"TFormalExpression","content":["tw.local.sqlStatements"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"e66fcbcf-ea21-45ab-86e1-f555f1a2bb7a","calledElement":"1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7"},{"targetRef":"360f095c-c838-407d-8326-f25a1a115a02","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"01020eac-7ccd-4b18-81f0-2e98c68bc117","sourceRef":"e66fcbcf-ea21-45ab-86e1-f555f1a2bb7a"},{"startQuantity":1,"outgoing":["1a3ded6b-baee-47a8-856f-acab7c1c6667"],"incoming":["c2c0641c-c534-46ee-821f-928b01692f0c"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":427,"y":55,"declaredType":"TNodeVisualInfo","height":70}]},"name":"DELETE Q","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"8c791fc3-3fec-4c45-8b19-58d174daab20","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\n\r\n\/\/---------------------------------Invoices------------------------------------------------------\r\ntw.local.sqlStatements[0] = {};\r\ntw.local.sqlStatements[0].parameters = new tw.object.listOf.SQLParameter();\r\ntw.local.sqlStatements[0].parameters[0] = {};\r\ntw.local.sqlStatements[0].parameters[0].value = tw.local.IDCRequest.DBID;\r\ntw.local.sqlStatements[0].sql = \"DELETE FROM BPM.IDC_REQUEST_INVOICES WHERE IDC_REQUEST_ID = ?\"\r\n\r\n\/\/---------------------------------Bills-----------------------------------------------------------\r\ntw.local.sqlStatements[1] = {};\r\ntw.local.sqlStatements[1].parameters = new tw.object.listOf.SQLParameter();\r\ntw.local.sqlStatements[1].parameters[0] = {};\r\ntw.local.sqlStatements[1].parameters[0].value = tw.local.IDCRequest.DBID;\r\ntw.local.sqlStatements[1].sql = \"DELETE FROM BPM.IDC_REQUEST_BILLS WHERE IDC_REQUEST_ID = ?\"\r\n\/\/--------------------------------------------------------------------------------------------------\r\ntw.local.sqlStatements[2] = {};\r\ntw.local.sqlStatements[2].parameters = new tw.object.listOf.SQLParameter();\r\ntw.local.sqlStatements[2].parameters[0] = {};\r\ntw.local.sqlStatements[2].parameters[0].value = tw.local.IDCRequest.DBID;\r\ntw.local.sqlStatements[2].sql = \"DELETE FROM BPM.IDC_FACILITY_DETAILS WHERE IDC_REQUEST_ID = ?\""]}},{"targetRef":"a8fb1bfb-58c7-4ce7-8f29-137efb1dc5d9","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To DELETE Invoices &amp; Bills","declaredType":"sequenceFlow","id":"1a3ded6b-baee-47a8-856f-acab7c1c6667","sourceRef":"8c791fc3-3fec-4c45-8b19-58d174daab20"},{"startQuantity":1,"outgoing":["02052b06-180c-4b80-86e4-2c397f656ac3"],"incoming":["26923925-0f09-4da5-8564-ee592a52a840"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":758,"y":55,"declaredType":"TNodeVisualInfo","height":70}]},"name":"INSERT Q","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"6322d481-5d75-47ff-8b5f-56cada0079b8","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\nvar j = 0\r\nfunction paramInit (type,value) {\r\n\ttw.local.sqlStatements[j].parameters[tw.local.i] = new tw.object.SQLParameter();\r\n\ttw.local.sqlStatements[j].parameters[tw.local.i].type = type;\r\n\ttw.local.sqlStatements[j].parameters[tw.local.i].value = value;\r\n\ttw.local.i= tw.local.i+1;\r\n}\r\n\/\/-----------------------------invoice--------------------------------------------------\r\n\r\nfor (j=0; j&lt;tw.local.IDCRequest.invoices.listLength; j++) {\r\n\ttw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\n\ttw.local.sqlStatements[j].sql = \"INSERT INTO BPM.IDC_REQUEST_INVOICES (IDC_REQUEST_ID, INVOICE_NUMBER, INVOICE_DATE) VALUES (?,?,?) ;\";\r\n\t\r\n\ttw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\n\ttw.local.i = 0;\r\n\t\r\n\tparamInit(\"INTEGER\",tw.local.IDCRequest.DBID);\r\n\tparamInit(\"VARCHAR\",tw.local.IDCRequest.invoices[j].number);\r\n\tparamInit(\"DATE\",tw.local.IDCRequest.invoices[j].date);\r\n}\r\n\r\n\/\/\/\/-----------------------------bill-----------------------------------------------------\r\nvar n = 0;\r\n\/\/ j++;\r\nfor (; j&lt;(tw.local.IDCRequest.billOfLading.listLength + tw.local.IDCRequest.invoices.listLength); j++) {\r\n\ttw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\n\ttw.local.sqlStatements[j].sql = \"INSERT INTO BPM.IDC_REQUEST_BILLS (IDC_REQUEST_ID, BILL_OF_LADING_REF, BILL_OF_LADING_DATE) VALUES (?,?,?) ;\";\r\n\t\r\n\ttw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\n\ttw.local.i = 0;\r\n\t\r\n\tparamInit(\"INTEGER\",tw.local.IDCRequest.DBID);\r\n\tparamInit(\"VARCHAR\",tw.local.IDCRequest.billOfLading[n].number);\r\n\tparamInit(\"DATE\",tw.local.IDCRequest.billOfLading[n].date);\r\n\tn+=1;\r\n}\r\n\r\n\/\/\/\/-----------------------------UPDATE customer---------------------------------------------------\r\n\/\/ j+=1;\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\ntw.local.sqlStatements[j].sql = \"UPDATE BPM.IDC_CUSTOMER_INFORMATION SET CIF = ?, CUSTOMER_NAME = ?, CUSTOMER_SECTOR = ?, CUSTOMER_TYPE = ?, CUSTOMER_NUMBER_AT_CBE = ?, FACILITY_TYPE_ID = ?, COMMERCIAL_REGISTRATION_NUMBER = ?, COMMERCIAL_REGISTRATION_OFFICE = ?, TAX_CARD_NUMBER = ?, IMPORT_CARD_NUMBER = ? WHERE IDC_REQUEST_ID = ?;\";\r\n\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\ntw.local.i = 0;\r\n\r\nparamInit(\"VARCHAR\",tw.local.IDCRequest.customerInformation.CIFNumber);\r\nparamInit(\"VARCHAR\",tw.local.IDCRequest.customerInformation.customerName);\r\nparamInit(\"VARCHAR\",tw.local.IDCRequest.customerInformation.customerSector);\/\/can be update later &lt;----------------------------------------------------\r\nparamInit(\"VARCHAR\",tw.local.IDCRequest.customerInformation.customerType);\r\nparamInit(\"VARCHAR\",tw.local.IDCRequest.customerInformation.CBENumber);\r\nparamInit(\"INTEGER\",tw.local.IDCRequest.customerInformation.facilityType.id);\r\nparamInit(\"VARCHAR\",tw.local.IDCRequest.customerInformation.commercialRegistrationNumber);\r\nparamInit(\"VARCHAR\",tw.local.IDCRequest.customerInformation.commercialRegistrationOffice);\r\nparamInit(\"VARCHAR\",tw.local.IDCRequest.customerInformation.taxCardNumber);\r\nparamInit(\"VARCHAR\",tw.local.IDCRequest.customerInformation.importCardNumber);\r\n\r\nparamInit(\"INTEGER\",tw.local.IDCRequest.DBID);\r\n\/\/-----------------------------------------facility-----------------------------------------------------------\r\n\/\/if (tw.local.idcContract.facilities != null &amp;&amp; tw.local.idcContract.facilities != undefined) {\r\n\tif (tw.local.idcContract.facilities.listLength &gt; 0) {\r\n\t\tj++;\r\n\t\tfor (var m_f_len=0; m_f_len &lt; tw.local.idcContract.facilities.listLength; m_f_len++) {\r\n\t\t\tfor (var s_f_len=0; s_f_len &lt; tw.local.idcContract.facilities[m_f_len].facilityLines.listLength; s_f_len++) {\r\n\t\t\t\ttw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\n\t\t\t\ttw.local.sqlStatements[j].sql = \"INSERT INTO BPM.IDC_FACILITY_DETAILS (IDC_REQUEST_ID, FACILITY_ID, CURRENCY, LINE_SERIAL, LINE_CODE, FACILITY_PERCENTAGE_TO_BOOK) VALUES (?,?,?,?,?,?) ;\";\r\n\t\t\t\t\r\n\t\t\t\ttw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\n\t\t\t\ttw.local.i = 0;\r\n\t\t\t\tparamInit(\"INTEGER\",tw.local.IDCRequest.DBID);\r\n\t\t\t\tparamInit(\"VARCHAR\",tw.local.idcContract.facilities[m_f_len].facilityID);\r\n\t\t\t\tparamInit(\"VARCHAR\",tw.local.idcContract.facilities[m_f_len].facilityCurrency.value);\r\n\t\t\t\tparamInit(\"VARCHAR\",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].lineSerialNumber);\r\n\t\t\t\tparamInit(\"VARCHAR\",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].lineCode);\r\n\t\t\t\tparamInit(\"DECIMAL\",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].facilityPercentageToBook);\r\n\t\t\t\tj++;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\r\n\/\/}\r\n"]}},{"targetRef":"e66fcbcf-ea21-45ab-86e1-f555f1a2bb7a","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To INSERT Invoices &amp; Bills","declaredType":"sequenceFlow","id":"02052b06-180c-4b80-86e4-2c397f656ac3","sourceRef":"6322d481-5d75-47ff-8b5f-56cada0079b8"},{"startQuantity":1,"outgoing":["3f70aa03-14cd-4098-811e-ef15c12abae9"],"incoming":["2027.466e771e-c0a3-4f39-8638-7921cf440833"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":118,"y":55,"declaredType":"TNodeVisualInfo","height":70}]},"name":"UPDATE IDC Request Q","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"08188f42-8534-47c7-83a5-bf60b20530c5","scriptFormat":"text\/x-javascript","script":{"content":["\r\ntw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\ntw.local.sqlStatements[0] = new tw.object.SQLStatement();\r\ntw.local.sqlStatements[0].sql = \"UPDATE BPM.IDC_REQUEST_DETAILS SET REQUEST_NUMBER = ?, PARENT_REQUEST_NUMBER = ?, BPM_INSTANCE_NUMBER = ?, REQUEST_INITIATOR = ?, REQUEST_BRANCH_HUB = ?, REQUEST_NATURE_ID = ?, REQUEST_TYPE_ID = ?, REQUEST_DATE = ?, REQUEST_STATE = ?, REQUEST_STAGE = ?, REQUEST_STATUS = ?, REQUEST_SUB_STATUS = ?, FLEX_CUBE_CONTRACT_NUMBER = ?, IS_WITHDRAWEN = ?, IMPORT_PURPOSE_ID = ?, PAYMENT_TERMS_ID = ?, DOCUMENTS_SOURCE_ID = ?, PRODUCT_CATEGORY_ID = ?, COMMODITY_DESCRIPTION = ?, ADVANCE_PAYMENT_OUTSTANDING_AMOUNT = ?, DOCUMENT_AMOUNT = ?, CURRENCY = ?, CHARGES_ACCOUNT = ?, SOURCE_OF_FOREIGN_CURRENCY_ID = ?, SOURCE_OF_FUNDS_ID = ?, PAYMENT_ACCOUNT = ?, CASH_AMOUNT_DOCUMENT_CURRENCY = ?, CASH_AMOUNT_NO_CURRENCY = ?, FACILITY_AMOUNT_DOCUMENT_CURRENCY = ?, FACILITY_AMOUNT_NO_CURRENCY = ?, DISCOUNT = ?, AMOUNT_ADVANCED = ?, AMOUNT_PAID_BY_OTHER_BANKS = ?, AMOUNT_SIGHT = ?, AMOUNT_DEFERRED_NO_AVALIZATION = ?, AMOUNT_DEFERRED_AVALIZATION = ?, AMOUNT_PAYABLE_BY_NBE = ?, FIRST_INSTALLEMENT_MATURITY_DATE = ?, NO_OF_DAYS_TILL_MATURITY = ?, BENEFICIARY_NAME = ?, BENEFICIARY_BANK = ?, BENEFICIARY_IBAN = ?, BENEFICIARY_COUNTRY_CODE = ?, CORRESPONDENT_REF_NUM = ?, TRADE_FO_REFERENCE_NUMBER = ?, TRADE_FU_APPROVAL_NO = ?, EXECUTION_HUB_CODE = ?, EXECUTION_HUB_NAME = ?, SHIPPING_DATE = ?, INCOTERMS = ?, SHIPMENT_METHOD_ID = ?, DESTINATION_PORT = ?, CBE_COMMODITY_CLASSIFICATION_ID = ?, HS_CODE = ?, HS_DESCRIPTION = ?, ACID = ?, COUNTRY_OF_ORIGIN_CODE = ? WHERE ID = ?\";\r\n\r\ntw.local.sqlStatements[0].parameters = new tw.object.listOf.SQLParameter();\r\ntw.local.i = 0;\r\nfunction paramInit (value) {\r\n\ttw.local.sqlStatements[0].parameters[tw.local.i] = new tw.object.SQLParameter();\r\n\/\/\ttw.local.sqlStatements[0].parameters[tw.local.i].type = type;\r\n\ttw.local.sqlStatements[0].parameters[tw.local.i].value = value;\r\n\/\/\ttw.local.sqlStatements[0].parameters[tw.local.i].mode = \"IN\";\r\n\ttw.local.i= tw.local.i+1;\r\n\/\/\treturn tw.local.parameter;\r\n}\r\nparamInit (tw.local.IDCRequest.appInfo.instanceID);\r\nparamInit (tw.local.IDCRequest.ParentIDCRequestNumber);\r\nparamInit (tw.local.IDCRequest.appInfo.appID);\r\nparamInit (tw.local.IDCRequest.appInfo.initiator);\r\nparamInit (tw.local.IDCRequest.appInfo.branch.name);\r\nparamInit (tw.local.IDCRequest.IDCRequestNature.id);\r\nparamInit (tw.local.IDCRequest.IDCRequestType.id);\r\nparamInit (tw.local.IDCRequest.requestDate);\r\nparamInit (tw.local.IDCRequest.IDCRequestState);\r\nparamInit (tw.local.IDCRequest.IDCRequestStage);\r\nparamInit (tw.local.IDCRequest.appInfo.status);\r\nparamInit (tw.local.IDCRequest.appInfo.subStatus);\r\nparamInit (tw.local.IDCRequest.FCContractNumber);\r\nif (tw.local.IDCRequest.isIDCWithdrawn == true) {\r\n\tparamInit (1);\r\n\t\r\n}else{\r\n\tparamInit (0);\r\n\t\r\n}\r\nparamInit (tw.local.IDCRequest.importPurpose.id);\r\nparamInit (tw.local.IDCRequest.paymentTerms.id);\r\nparamInit (tw.local.IDCRequest.documentsSource.id);\r\nparamInit (tw.local.IDCRequest.productCategory.id);\r\nparamInit (tw.local.IDCRequest.commodityDescription);\r\nif (tw.local.IDCRequest.IDCRequestType.englishdescription == \"Advance Payment\") {\r\n\tparamInit (tw.local.IDCRequest.financialDetails.amtPayableByNBE);\r\n\t\r\n}else{\r\n\tparamInit (0.0);\r\n\t\r\n}\r\nparamInit (tw.local.IDCRequest.financialDetails.documentAmount);\r\nparamInit (tw.local.IDCRequest.financialDetails.documentCurrency.code);\r\nparamInit (tw.local.IDCRequest.financialDetails.chargesAccount);\r\nparamInit (tw.local.IDCRequest.financialDetails.sourceOfForeignCurrency.id);\r\nparamInit (tw.local.IDCRequest.financialDetails.sourceOfFunds.id);\r\nparamInit (tw.local.IDCRequest.financialDetails.paymentAccount);\r\nparamInit (tw.local.IDCRequest.financialDetails.cashAmtInDocCurrency);\r\nparamInit (tw.local.IDCRequest.financialDetails.CashAmtWithNoCurrency);\r\nparamInit (tw.local.IDCRequest.financialDetails.facilityAmtInDocCurrency);\r\nparamInit (tw.local.IDCRequest.financialDetails.facilityAmtWithNoCurrency);\r\nparamInit (tw.local.IDCRequest.financialDetails.discountAmt);\r\nparamInit (tw.local.IDCRequest.financialDetails.amountAdvanced);\r\nparamInit (tw.local.IDCRequest.financialDetails.amtPaidbyOtherBanks);\r\nparamInit (tw.local.IDCRequest.financialDetails.amtSight);\r\nparamInit (tw.local.IDCRequest.financialDetails.amtDeferredNoAvalized);\r\nparamInit (tw.local.IDCRequest.financialDetails.amtDeferredAvalized);\r\nparamInit (tw.local.IDCRequest.financialDetails.amtPayableByNBE);\r\nparamInit (tw.local.IDCRequest.financialDetails.firstInstallementMaturityDate);\r\nparamInit (tw.local.IDCRequest.financialDetails.daysTillMaturity);\r\nparamInit (tw.local.IDCRequest.financialDetails.beneficiaryDetails.name);\r\nparamInit (tw.local.IDCRequest.financialDetails.beneficiaryDetails.bank);\r\nparamInit (tw.local.IDCRequest.financialDetails.beneficiaryDetails.account);\r\nparamInit (tw.local.IDCRequest.financialDetails.beneficiaryDetails.country.code);\r\nparamInit (tw.local.IDCRequest.financialDetails.beneficiaryDetails.correspondentRefNum);\r\nparamInit (tw.local.IDCRequest.financialDetails.tradeFOReferenceNumber);\r\nparamInit (tw.local.IDCRequest.financialDetails.tradeFinanceApprovalNumber);\r\nparamInit (tw.local.IDCRequest.financialDetails.executionHub.code);\r\nparamInit (tw.local.IDCRequest.financialDetails.executionHub.arabicdescription);\r\nparamInit (tw.local.IDCRequest.productsDetails.shippingDate);\r\nif (tw.local.IDCRequest.productsDetails.incoterms.id == 0) {\r\n\tparamInit (null);\r\n} else {\r\n\tparamInit (tw.local.IDCRequest.productsDetails.incoterms.id);\r\n}\r\n\r\nif (tw.local.IDCRequest.productsDetails.shipmentMethod.id == 0) {\r\n\tparamInit (null);\r\n} else {\r\n\tparamInit (tw.local.IDCRequest.productsDetails.shipmentMethod.id);\r\n}\r\n\r\nparamInit (tw.local.IDCRequest.productsDetails.destinationPort);\r\nif (tw.local.IDCRequest.productsDetails.CBECommodityClassification.id == 0) {\r\n\tparamInit (null);\r\n} else {\r\n\tparamInit (tw.local.IDCRequest.productsDetails.CBECommodityClassification.id);\r\n}\r\n\r\nparamInit (tw.local.IDCRequest.productsDetails.HSProduct.code);\r\nparamInit (tw.local.IDCRequest.productsDetails.HSProduct.englishdescription);\r\nparamInit (tw.local.IDCRequest.productsDetails.ACID);\r\nparamInit (tw.local.IDCRequest.countryOfOrigin.code);\r\n\r\nparamInit (tw.local.IDCRequest.DBID);\r\n\/\/-------------------------------------------------------------------------------------------\r\n"]}},{"itemSubjectRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","name":"sqlStatements","isCollection":true,"declaredType":"dataObject","id":"2056.b9206c57-ebfa-44da-80f2-45a474a00a3f"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"0"}]},"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"i","isCollection":false,"declaredType":"dataObject","id":"2056.48790657-e9c4-4474-8390-840e19f82662"},{"targetRef":"485c1cfa-b21c-4f9b-8c38-408c70d91afd","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To UPDATE IDC Request","declaredType":"sequenceFlow","id":"3f70aa03-14cd-4098-811e-ef15c12abae9","sourceRef":"08188f42-8534-47c7-83a5-bf60b20530c5"},{"parallelMultiple":false,"outgoing":["b19a024b-42ba-4014-86e3-2bcb9271fb8c"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"5b0321ed-d002-4c05-8f56-98483e229482"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"08f92dc3-2aa2-4607-84c5-1eadc4baab35","otherAttributes":{"eventImplId":"7c722598-0f7c-476f-84c7-94cf6e0a58eb"}}],"attachedToRef":"08188f42-8534-47c7-83a5-bf60b20530c5","extensionElements":{"nodeVisualInfo":[{"width":24,"x":153,"y":113,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"fe41e509-5f0c-459e-847c-d70a899d3be5","outputSet":{}},{"parallelMultiple":false,"outgoing":["5e65cdb5-7600-4574-8e98-e52d8c393a89"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2320fd86-a886-4b59-8eeb-212a322b8508"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"ffc8b9da-6ce9-43d5-838e-a2c1a68b4e73","otherAttributes":{"eventImplId":"5097d80b-ef39-4032-8a31-a3eb7a4c8fbe"}}],"attachedToRef":"485c1cfa-b21c-4f9b-8c38-408c70d91afd","extensionElements":{"nodeVisualInfo":[{"width":24,"x":295,"y":114,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"21552222-2fdb-4ae9-875c-b9a82761b60b","outputSet":{}},{"parallelMultiple":false,"outgoing":["a3091323-ea29-418a-8f55-3aace96a0238"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"62a620e0-829b-4c0d-8cae-a759c2943819"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"0cc3898b-f654-4ca4-8c2b-f38df1446876","otherAttributes":{"eventImplId":"3b81d5ed-621c-4551-83e0-19744dc00f98"}}],"attachedToRef":"8c791fc3-3fec-4c45-8b19-58d174daab20","extensionElements":{"nodeVisualInfo":[{"width":24,"x":462,"y":113,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"68b88d8d-4d56-42d0-8463-31a3e6d422bf","outputSet":{}},{"parallelMultiple":false,"outgoing":["ae9760b1-3e54-4af3-8c8f-84d8bff70cfa"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"7870779c-7fa5-4591-8e70-dad36fd4eafc"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"7e6df494-0949-4314-8e84-e413883057ff","otherAttributes":{"eventImplId":"d833445d-97e8-418f-8f93-ce40e9d24623"}}],"attachedToRef":"a8fb1bfb-58c7-4ce7-8f29-137efb1dc5d9","extensionElements":{"nodeVisualInfo":[{"width":24,"x":611,"y":114,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error3","declaredType":"boundaryEvent","id":"babb4479-8247-49a2-896c-2883cf18c868","outputSet":{}},{"parallelMultiple":false,"outgoing":["c2b72103-6d45-4620-8545-2995c7bb499f"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"79dd141c-c3ab-43b4-8d18-16aec5ebcbd6"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"da114bbf-fd66-4c4c-80a8-232fbbe7cdec","otherAttributes":{"eventImplId":"a2ac3353-4350-44b4-832d-6f458199cf03"}}],"attachedToRef":"6322d481-5d75-47ff-8b5f-56cada0079b8","extensionElements":{"nodeVisualInfo":[{"width":24,"x":793,"y":113,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error4","declaredType":"boundaryEvent","id":"6943fb57-7d25-492b-87e9-e6076031a215","outputSet":{}},{"parallelMultiple":false,"outgoing":["b75c322b-4188-4e62-82ed-82e85714a328"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"673954b5-1c0a-4820-8771-f85837fe4ef9"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"8eba4d11-7ac0-41e5-8494-27c136a964b1","otherAttributes":{"eventImplId":"6a034290-267a-4ef5-86d6-67d531b78962"}}],"attachedToRef":"e66fcbcf-ea21-45ab-86e1-f555f1a2bb7a","extensionElements":{"nodeVisualInfo":[{"width":24,"x":964,"y":114,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error5","declaredType":"boundaryEvent","id":"220efb6a-ffba-4dd8-86f0-9e556b4573c0","outputSet":{}},{"incoming":["b19a024b-42ba-4014-86e3-2bcb9271fb8c","5e65cdb5-7600-4574-8e98-e52d8c393a89","a3091323-ea29-418a-8f55-3aace96a0238","ae9760b1-3e54-4af3-8c8f-84d8bff70cfa","c2b72103-6d45-4620-8545-2995c7bb499f","b75c322b-4188-4e62-82ed-82e85714a328"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"6b9a105e-e992-47a4-8891-8315c1a41c41","otherAttributes":{"eventImplId":"fe2ffa6e-23f0-4a38-862c-41f7ccf0a792"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":531,"y":190,"declaredType":"TNodeVisualInfo","height":24}],"preAssignmentScript":["log.info(\"*============ IDC =============*\");\r\nlog.info(\"[Update IDC Request -&gt; Log Error ]- start\");\r\nlog.info(\"ProcessInstance :\" +tw.system.currentProcessInstanceID + \" Error : \" + tw.system.error.toString(true));\r\n\r\ntw.local.error = new tw.object.AjaxError();\r\nvar attribute = String(tw.system.error.getAttribute(\"type\"));\r\nvar element = String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\ntw.local.errorMSG = attribute + \",\" + element;\r\n\/\/tw.local.errorMSG =String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\nlog.info(\"[Update IDC Request -&gt; Log Error ]- END\");\r\nlog.info(\"*============================================*\");\r\n\r\ntw.local.error.errorText = tw.local.errorMSG;"]},"name":"End Event","dataInputAssociation":[{"assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}]}],"declaredType":"endEvent","id":"e89eb804-d511-49e2-8947-ba49fc792260"},{"targetRef":"e89eb804-d511-49e2-8947-ba49fc792260","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"b19a024b-42ba-4014-86e3-2bcb9271fb8c","sourceRef":"fe41e509-5f0c-459e-847c-d70a899d3be5"},{"targetRef":"e89eb804-d511-49e2-8947-ba49fc792260","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"5e65cdb5-7600-4574-8e98-e52d8c393a89","sourceRef":"21552222-2fdb-4ae9-875c-b9a82761b60b"},{"targetRef":"e89eb804-d511-49e2-8947-ba49fc792260","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"a3091323-ea29-418a-8f55-3aace96a0238","sourceRef":"68b88d8d-4d56-42d0-8463-31a3e6d422bf"},{"targetRef":"e89eb804-d511-49e2-8947-ba49fc792260","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"ae9760b1-3e54-4af3-8c8f-84d8bff70cfa","sourceRef":"babb4479-8247-49a2-896c-2883cf18c868"},{"targetRef":"e89eb804-d511-49e2-8947-ba49fc792260","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"c2b72103-6d45-4620-8545-2995c7bb499f","sourceRef":"6943fb57-7d25-492b-87e9-e6076031a215"},{"targetRef":"e89eb804-d511-49e2-8947-ba49fc792260","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"b75c322b-4188-4e62-82ed-82e85714a328","sourceRef":"220efb6a-ffba-4dd8-86f0-9e556b4573c0"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.5c0a8709-3c23-4859-89f0-37d37667fd87"}],"laneSet":[{"id":"83537b6a-332d-4186-8d20-e9fa33ad269b","lane":[{"flowNodeRef":["1c48126d-f842-4cc4-88b7-e379c0afbac0","360f095c-c838-407d-8326-f25a1a115a02","485c1cfa-b21c-4f9b-8c38-408c70d91afd","a8fb1bfb-58c7-4ce7-8f29-137efb1dc5d9","e66fcbcf-ea21-45ab-86e1-f555f1a2bb7a","8c791fc3-3fec-4c45-8b19-58d174daab20","6322d481-5d75-47ff-8b5f-56cada0079b8","08188f42-8534-47c7-83a5-bf60b20530c5","fe41e509-5f0c-459e-847c-d70a899d3be5","21552222-2fdb-4ae9-875c-b9a82761b60b","68b88d8d-4d56-42d0-8463-31a3e6d422bf","babb4479-8247-49a2-896c-2883cf18c868","6943fb57-7d25-492b-87e9-e6076031a215","220efb6a-ffba-4dd8-86f0-9e556b4573c0","e89eb804-d511-49e2-8947-ba49fc792260"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"14e0e4d4-93f0-4369-8cf6-f45786228172","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Update IDC Request","declaredType":"process","id":"1.e75c339e-472e-439b-878f-bfafd0c4b968","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.6d23c29f-dcc7-4d77-81e8-3c00f3db633d"}],"inputSet":[{"dataInputRefs":["2055.1cb68f60-25be-43eb-8983-faa1eebbd945","2055.8dc5d463-89eb-4f51-8d45-5412d38d92ae"]}],"outputSet":[{"dataOutputRefs":["2055.6d23c29f-dcc7-4d77-81e8-3c00f3db633d"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = new tw.object.IDCRequest();\r\nautoObject.IDCRequestState = \"Final\";\r\nautoObject.commodityDescription = \"commo\";\r\nautoObject.countryOfOrigin = new tw.object.DBLookup();\r\nautoObject.countryOfOrigin.id = 1;\r\nautoObject.countryOfOrigin.code = \"EG\";\r\nautoObject.countryOfOrigin.arabicdescription = \"\u0645\u0635\u0631\";\r\nautoObject.countryOfOrigin.englishdescription = \"\u064fEgypt\";\r\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\nautoObject.appInfo.requestDate = \"12-08-2023\";\r\nautoObject.appInfo.status = \"status\";\r\nautoObject.appInfo.subStatus = \"subStatus\";\r\nautoObject.appInfo.initiator = \"initiator\";\r\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.appInfo.branch.name = \"001\";\r\nautoObject.appInfo.branch.value = \"001\";\r\nautoObject.appInfo.requestName = \"ICAP\";\r\nautoObject.appInfo.requestType = \"ICAP\";\r\nautoObject.appInfo.stepName = \"stepName\";\r\nautoObject.appInfo.appRef = \"appRef\";\r\nautoObject.appInfo.appID = \"85634\";\r\nautoObject.appInfo.instanceID = \"00102230000220\";\r\nautoObject.productsDetails = new tw.object.ProductsDetails();\r\nautoObject.productsDetails.destinationPort = \"destinationPort\";\r\nautoObject.productsDetails.shippingDate = new TWDate();\r\nautoObject.productsDetails.HSProduct = new tw.object.DBLookup();\r\nautoObject.productsDetails.HSProduct.id = 1;\r\nautoObject.productsDetails.HSProduct.code = \"0101 \u2013 0106\";\r\nautoObject.productsDetails.HSProduct.arabicdescription = \"arabic\";\r\nautoObject.productsDetails.HSProduct.englishdescription = \"Live animals\";\r\nautoObject.productsDetails.incoterms = new tw.object.DBLookup();\r\nautoObject.productsDetails.incoterms.id = 2;\r\nautoObject.productsDetails.incoterms.code = \"FCA\";\r\nautoObject.productsDetails.incoterms.arabicdescription = \"arabicdon\";\r\nautoObject.productsDetails.incoterms.englishdescription = \"Free to Carrier\";\r\nautoObject.productsDetails.ACID = \"ACID\";\r\nautoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();\r\nautoObject.productsDetails.CBECommodityClassification.id = 3;\r\nautoObject.productsDetails.CBECommodityClassification.code = \"003\";\r\nautoObject.productsDetails.CBECommodityClassification.arabicdescription = \"\u0627\u0644\u0627\u0644\u0627\u062a \u0648 \u0627\u0644\u0645\u0639\u062f\u0627\u062a\";\r\nautoObject.productsDetails.CBECommodityClassification.englishdescription = \"english\";\r\nautoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();\r\nautoObject.productsDetails.shipmentMethod.id = 4;\r\nautoObject.productsDetails.shipmentMethod.code = \"004\";\r\nautoObject.productsDetails.shipmentMethod.arabicdescription = \"\u0628\u0631\u064a\u062f \u0633\u0631\u064a\u0639\";\r\nautoObject.productsDetails.shipmentMethod.englishdescription = \"english\";\r\nautoObject.financialDetails = new tw.object.FinancialDetails();\r\nautoObject.financialDetails.isAdvancePaymentsUsed = false;\r\nautoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();\r\nautoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();\r\nautoObject.financialDetails.paymentTerms[0].installmentAmount = 1.1;\r\nautoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();\r\nautoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();\r\nautoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();\r\nautoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency =1.0;\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id =1;\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = \"EGP\";\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = \"\u062c\u0646\u064a\u0647 \u0645\u0635\u0631\u0649\";\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = \"Egyptian Pound\";\r\nautoObject.financialDetails.usedAdvancePayment[0].AmountAllocated =1.0;\r\nautoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = \"advance\";\r\nautoObject.financialDetails.usedAdvancePayment[0].outstandingAmount =1.0;\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = \"invoicer\";\r\nautoObject.financialDetails.usedAdvancePayment[0].paidAmount =1.0;\r\nautoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = \"benefic\";\r\nautoObject.financialDetails.usedAdvancePayment[0].documentAmount =1.0;\r\nautoObject.financialDetails.discountAmt =77.0;\r\nautoObject.financialDetails.firstInstallementMaturityDate = new TWDate();\r\nautoObject.financialDetails.facilityAmtInDocCurrency =1.0;\r\nautoObject.financialDetails.amtSight =1.0;\r\nautoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();\r\nautoObject.financialDetails.beneficiaryDetails.account = \"account\";\r\nautoObject.financialDetails.beneficiaryDetails.bank = \"bank\";\r\nautoObject.financialDetails.beneficiaryDetails.correspondentRefNum = \"corresp\";\r\nautoObject.financialDetails.beneficiaryDetails.name = \"beneficiary\";\r\nautoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();\r\nautoObject.financialDetails.beneficiaryDetails.country.id =1;\r\nautoObject.financialDetails.beneficiaryDetails.country.code = \"EG\";\r\nautoObject.financialDetails.beneficiaryDetails.country.arabicdescription = \"\u0645\u0635\u0631\";\r\nautoObject.financialDetails.beneficiaryDetails.country.englishdescription = \"Egypt\";\r\nautoObject.financialDetails.amtDeferredNoAvalized =1.0;\r\nautoObject.financialDetails.amtPayableByNBE =1.0;\r\nautoObject.financialDetails.executionHub = new tw.object.DBLookup();\r\nautoObject.financialDetails.executionHub.id =1;\r\nautoObject.financialDetails.executionHub.code = \"077\";\r\nautoObject.financialDetails.executionHub.arabicdescription = \"\u0648\u062d\u062f\u0629 \u0627\u0639\u062a\u0645\u0627\u062f\u0627\u062a \u0627\u0644\u0628\u0631\u062c\";\r\nautoObject.financialDetails.executionHub.englishdescription = \"english\";\r\nautoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();\r\nautoObject.financialDetails.sourceOfForeignCurrency.id =1;\r\nautoObject.financialDetails.sourceOfForeignCurrency.code = \"001\";\r\nautoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = \"\u0644\u0627 \u064a\u0648\u062c\u062f \u0639\u0645\u0644\u0629 \u0627\u062c\u0646\u0628\u064a\u0629\";\r\nautoObject.financialDetails.sourceOfForeignCurrency.englishdescription = \"No Foreign Currency\";\r\nautoObject.financialDetails.facilityAmtWithNoCurrency =1.0;\r\nautoObject.financialDetails.documentCurrency = new tw.object.DBLookup();\r\nautoObject.financialDetails.documentCurrency.id =1;\r\nautoObject.financialDetails.documentCurrency.code = \"EGP\";\r\nautoObject.financialDetails.documentCurrency.arabicdescription = \"\u062c\u0646\u064a\u0647 \u0645\u0635\u0631\u0649\";\r\nautoObject.financialDetails.documentCurrency.englishdescription = \"Egyptian Pound\";\r\nautoObject.financialDetails.daysTillMaturity =1;\r\nautoObject.financialDetails.tradeFinanceApprovalNumber = \"12345\";\r\nautoObject.financialDetails.amountAdvanced =1.0;\r\nautoObject.financialDetails.paymentAccount = \"paymentAccount\";\r\nautoObject.financialDetails.tradeFOReferenceNumber = \"123\";\r\nautoObject.financialDetails.documentAmount =1.0;\r\nautoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();\r\nautoObject.financialDetails.sourceOfFunds.id =1;\r\nautoObject.financialDetails.sourceOfFunds.code = \"001\";\r\nautoObject.financialDetails.sourceOfFunds.arabicdescription = \"\u0645\u0648\u0627\u0631\u062f \u0627\u0644\u0639\u0645\u064a\u0644 \u0627\u0644\u0630\u0627\u062a\u064a\u0629\";\r\nautoObject.financialDetails.sourceOfFunds.englishdescription = \"english\";\r\nautoObject.financialDetails.chargesAccount = \"charges\";\r\nautoObject.financialDetails.CashAmtWithNoCurrency =1.0;\r\nautoObject.financialDetails.amtDeferredAvalized =1.0;\r\nautoObject.financialDetails.amtPaidbyOtherBanks =1.0;\r\nautoObject.financialDetails.cashAmtInDocCurrency =1.0;\r\nautoObject.IDCRequestType = new tw.object.DBLookup();\r\nautoObject.IDCRequestType.id =1;\r\nautoObject.IDCRequestType.code = \"code\";\r\nautoObject.IDCRequestType.arabicdescription = \"arabic\";\r\nautoObject.IDCRequestType.englishdescription = \"english\";\r\nautoObject.isIDCWithdrawn = false;\r\nautoObject.IDCRequestStage = \"Stage\";\r\nautoObject.FCContractNumber = \"FC\";\r\nautoObject.billOfLading = new tw.object.listOf.Invoice();\r\nautoObject.billOfLading[0] = new tw.object.Invoice();\r\nautoObject.billOfLading[0].date = new TWDate();\r\nautoObject.billOfLading[0].number = \"123\";\r\nautoObject.billOfLading[1] = new tw.object.Invoice();\r\nautoObject.billOfLading[1].date = new TWDate();\r\nautoObject.billOfLading[1].number = \"123\";\r\nautoObject.importPurpose = new tw.object.DBLookup();\r\nautoObject.importPurpose.id =1;\r\nautoObject.importPurpose.code = \"001\";\r\nautoObject.importPurpose.arabicdescription = \"\u0627\u0633\u062a\u064a\u0631\u0627\u062f \u0633\u0644\u0639\u0649\";\r\nautoObject.importPurpose.englishdescription = \"Commodity Import\";\r\nautoObject.IDCRequestNature = new tw.object.DBLookup();\r\nautoObject.IDCRequestNature.id =1;\r\nautoObject.IDCRequestNature.code = \"1\";\r\nautoObject.IDCRequestNature.arabicdescription = \"\u0627\u0635\u062f\u0631 \u062c\u062f\u064a\u062f\";\r\nautoObject.IDCRequestNature.englishdescription = \"New Request\";\r\nautoObject.customerInformation = new tw.object.CustomerInformation();\r\nautoObject.customerInformation.CIFNumber = \"02366014\";\r\nautoObject.customerInformation.importCardNumber = \"import\";\r\nautoObject.customerInformation.commercialRegistrationNumber = \"commercial\";\r\nautoObject.customerInformation.customerName = \"customerName\";\r\nautoObject.customerInformation.isCustomeSanctionedbyCBE = \"\";\r\nautoObject.customerInformation.CBENumber = \"cbe\";\r\nautoObject.customerInformation.customerSector = \"rSector\";\r\nautoObject.customerInformation.facilityType = new tw.object.DBLookup();\r\nautoObject.customerInformation.facilityType.id = 1;\r\nautoObject.customerInformation.facilityType.arabicdescription= \"\u0644\u0627 \u064a\u062a\u0645\u062a\u0639 \u0628\u062a\u0633\u0647\u064a\u0644\u0627\u062a\";\r\nautoObject.customerInformation.commercialRegistrationOffice = \"Office\";\r\nautoObject.customerInformation.taxCardNumber = \"taxCardNumber\";\r\nautoObject.customerInformation.customerType = \"cType\";\r\nautoObject.customerInformation.addressLine1 = \"1\";\r\nautoObject.customerInformation.addressLine2 = \"2\";\r\nautoObject.invoices = new tw.object.listOf.Invoice();\r\nautoObject.invoices[0] = new tw.object.Invoice();\r\nautoObject.invoices[0].date = new TWDate();\r\nautoObject.invoices[0].number = \"number\";\r\nautoObject.invoices[1] = new tw.object.Invoice();\r\nautoObject.invoices[1].date = new TWDate();\r\nautoObject.invoices[1].number = \"number2\";\r\nautoObject.productCategory = new tw.object.DBLookup();\r\nautoObject.productCategory.id =1;\r\nautoObject.productCategory.code = \"001\";\r\nautoObject.productCategory.arabicdescription = \"arabic\";\r\nautoObject.productCategory.englishdescription = \"Automotive industry\";\r\nautoObject.documentsSource = new tw.object.DBLookup();\r\nautoObject.documentsSource.id =1;\r\nautoObject.documentsSource.code = \"001\";\r\nautoObject.documentsSource.arabicdescription = \"arabic\";\r\nautoObject.documentsSource.englishdescription = \"Automotive industry\";\r\nautoObject.ParentIDCRequestNumber = \"Paren\";\r\nautoObject.paymentTerms = new tw.object.DBLookup();\r\nautoObject.paymentTerms.id =1;\r\nautoObject.paymentTerms.code = \"001\";\r\nautoObject.paymentTerms.arabicdescription = \"\u0627\u0637\u0644\u0627\u0639\";\r\nautoObject.paymentTerms.englishdescription = \"Sight\";\r\nautoObject.approvals = new tw.object.Approvals();\r\nautoObject.approvals.CAD = false;\r\nautoObject.approvals.treasury = false;\r\nautoObject.approvals.compliance = false;\r\nautoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();\r\nautoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();\r\nautoObject.appLog[0].startTime = new TWDate();\r\nautoObject.appLog[0].endTime = new TWDate();\r\nautoObject.appLog[0].userName = \"userName\";\r\nautoObject.appLog[0].role = \"role\";\r\nautoObject.appLog[0].step = \"step\";\r\nautoObject.appLog[0].action = \"action\";\r\nautoObject.appLog[0].comment = \"comment\";\r\nautoObject.appLog[0].terminateReason = \"terminateReason\";\r\nautoObject.appLog[0].returnReason = \"returnReason\";\r\nautoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();\r\nautoObject.stepLog.startTime = new TWDate();\r\nautoObject.stepLog.endTime = new TWDate();\r\nautoObject.stepLog.userName = \"userName\";\r\nautoObject.stepLog.role = \"role\";\r\nautoObject.stepLog.step = \"step\";\r\nautoObject.stepLog.action = \"action\";\r\nautoObject.stepLog.comment = \"comment\";\r\nautoObject.stepLog.terminateReason = \"terminateReason\";\r\nautoObject.stepLog.returnReason = \"returnReason\";\r\nautoObject.DBID =7;\r\nautoObject.requestDate = new TWDate();\r\nautoObject"}]},"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"IDCRequest","isCollection":false,"id":"2055.1cb68f60-25be-43eb-8983-faa1eebbd945"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = new tw.object.IDCContract();\nautoObject.collateralAmount = 0.0;\nautoObject.userReference = \"\";\nautoObject.settlementAccounts = new tw.object.listOf.SettlementAccount();\nautoObject.settlementAccounts[0] = new tw.object.SettlementAccount();\nautoObject.settlementAccounts[0].debitedAccount = new tw.object.DebitedAccount();\nautoObject.settlementAccounts[0].debitedAccount.balanceSign = \"\";\nautoObject.settlementAccounts[0].debitedAccount.GLAccountNumber = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency = new tw.object.DBLookup();\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.id = 0;\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.code = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.arabicdescription = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.englishdescription = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountBranchCode = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountBalance = 0.0;\nautoObject.settlementAccounts[0].debitedAccount.accountNumber = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountClass = \"\";\nautoObject.settlementAccounts[0].debitedAccount.ownerAccounts = \"\";\nautoObject.settlementAccounts[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.settlementAccounts[0].debitedAccount.currency.name = \"\";\nautoObject.settlementAccounts[0].debitedAccount.currency.value = \"\";\nautoObject.settlementAccounts[0].debitedAccount.isGLFoundC = false;\nautoObject.settlementAccounts[0].debitedAccount.commCIF = \"\";\nautoObject.settlementAccounts[0].debitedAccount.isGLVerifiedC = false;\nautoObject.settlementAccounts[0].debitedAccount.isOverDraft = false;\nautoObject.settlementAccounts[0].debitedAmount = new tw.object.DebitedAmount();\nautoObject.settlementAccounts[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.debitPercentage = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.standardExchangeRate = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.negotiatedExchangeRate = 0.0;\nautoObject.settlementAccounts[0].accountNumberList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.settlementAccounts[0].accountNumberList[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.settlementAccounts[0].accountNumberList[0].name = \"\";\nautoObject.settlementAccounts[0].accountNumberList[0].value = \"\";\nautoObject.settlementAccounts[0].settCIF = \"\";\nautoObject.billAmount = 0.0;\nautoObject.billCurrency = new tw.object.DBLookup();\nautoObject.billCurrency.id = 0;\nautoObject.billCurrency.code = \"\";\nautoObject.billCurrency.arabicdescription = \"\";\nautoObject.billCurrency.englishdescription = \"\";\nautoObject.party = new tw.object.listOf.Parties();\nautoObject.party[0] = new tw.object.Parties();\nautoObject.party[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.party[0].partyType.name = \"\";\nautoObject.party[0].partyType.value = \"\";\nautoObject.party[0].partyId = \"\";\nautoObject.party[0].name = \"\";\nautoObject.party[0].country = \"\";\nautoObject.party[0].reference = \"\";\nautoObject.party[0].address1 = \"\";\nautoObject.party[0].address2 = \"\";\nautoObject.party[0].address3 = \"\";\nautoObject.party[0].address4 = \"\";\nautoObject.party[0].media = \"\";\nautoObject.party[0].address = \"\";\nautoObject.party[0].phone = \"\";\nautoObject.party[0].fax = \"\";\nautoObject.party[0].email = \"\";\nautoObject.party[0].contactPersonName = \"\";\nautoObject.party[0].mobile = \"\";\nautoObject.party[0].branch = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.party[0].branch.name = \"\";\nautoObject.party[0].branch.value = \"\";\nautoObject.party[0].language = \"\";\nautoObject.party[0].partyCIF = \"\";\nautoObject.party[0].isNbeCustomer = false;\nautoObject.party[0].isRetrived = false;\nautoObject.sourceReference = \"\";\nautoObject.isLimitsTrackingRequired = false;\nautoObject.liquidationSummary = new tw.object.LiquidationSummary();\nautoObject.liquidationSummary.liquidationCurrency = \"\";\nautoObject.liquidationSummary.debitBasisby = \"\";\nautoObject.liquidationSummary.liquidationAmt = 0.0;\nautoObject.liquidationSummary.debitValueDate = new TWDate();\nautoObject.liquidationSummary.creditValueDate = new TWDate();\nautoObject.IDCProduct = new tw.object.DBLookup();\nautoObject.IDCProduct.id = 0;\nautoObject.IDCProduct.code = \"\";\nautoObject.IDCProduct.arabicdescription = \"\";\nautoObject.IDCProduct.englishdescription = \"\";\nautoObject.interestToDate = new TWDate();\nautoObject.transactionMaturityDate = new TWDate();\nautoObject.commissionsAndCharges = new tw.object.listOf.CommissionsAndChargesDetails();\nautoObject.commissionsAndCharges[0] = new tw.object.CommissionsAndChargesDetails();\nautoObject.commissionsAndCharges[0].component = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount = new tw.object.DebitedAccount();\nautoObject.commissionsAndCharges[0].debitedAccount.balanceSign = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.GLAccountNumber = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency = new tw.object.DBLookup();\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.id = 0;\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.code = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.arabicdescription = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.englishdescription = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountBranchCode = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountBalance = 0.0;\nautoObject.commissionsAndCharges[0].debitedAccount.accountNumber = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountClass = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.ownerAccounts = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.commissionsAndCharges[0].debitedAccount.currency.name = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.currency.value = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.isGLFoundC = false;\nautoObject.commissionsAndCharges[0].debitedAccount.commCIF = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.isGLVerifiedC = false;\nautoObject.commissionsAndCharges[0].debitedAccount.isOverDraft = false;\nautoObject.commissionsAndCharges[0].chargeAmount = 0.0;\nautoObject.commissionsAndCharges[0].waiver = false;\nautoObject.commissionsAndCharges[0].debitedAmount = new tw.object.DebitedAmount();\nautoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.debitPercentage = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.standardExchangeRate = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.negotiatedExchangeRate = 0.0;\nautoObject.commissionsAndCharges[0].defaultCurrency = new tw.object.DBLookup();\nautoObject.commissionsAndCharges[0].defaultCurrency.id = 0;\nautoObject.commissionsAndCharges[0].defaultCurrency.code = \"\";\nautoObject.commissionsAndCharges[0].defaultCurrency.arabicdescription = \"\";\nautoObject.commissionsAndCharges[0].defaultCurrency.englishdescription = \"\";\nautoObject.commissionsAndCharges[0].defaultAmount = 0.0;\nautoObject.commissionsAndCharges[0].commAccountList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.commissionsAndCharges[0].commAccountList[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.commissionsAndCharges[0].commAccountList[0].name = \"\";\nautoObject.commissionsAndCharges[0].commAccountList[0].value = \"\";\nautoObject.transactionBaseDate = new TWDate();\nautoObject.tradeFinanceApprovalNumber = \"\";\nautoObject.FCContractNumber = \"\";\nautoObject.collateralCurrency = new tw.object.DBLookup();\nautoObject.collateralCurrency.id = 0;\nautoObject.collateralCurrency.code = \"\";\nautoObject.collateralCurrency.arabicdescription = \"\";\nautoObject.collateralCurrency.englishdescription = \"\";\nautoObject.interestRate = 0.0;\nautoObject.transactionTransitDays = 0;\nautoObject.swiftMessageData = new tw.object.SwiftMessageData();\nautoObject.swiftMessageData.intermediary = new tw.object.SwiftMessagePart();\nautoObject.swiftMessageData.intermediary.line1 = \"\";\nautoObject.swiftMessageData.intermediary.line2 = \"\";\nautoObject.swiftMessageData.intermediary.line3 = \"\";\nautoObject.swiftMessageData.intermediary.line4 = \"\";\nautoObject.swiftMessageData.intermediary.line5 = \"\";\nautoObject.swiftMessageData.intermediary.line6 = \"\";\nautoObject.swiftMessageData.detailsOfCharge = \"\";\nautoObject.swiftMessageData.accountWithInstitution = new tw.object.SwiftMessagePart();\nautoObject.swiftMessageData.accountWithInstitution.line1 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line2 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line3 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line4 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line5 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line6 = \"\";\nautoObject.swiftMessageData.receiver = \"\";\nautoObject.swiftMessageData.swiftMessageOption = \"\";\nautoObject.swiftMessageData.coverRequired = \"\";\nautoObject.swiftMessageData.transferType = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution = new tw.object.SwiftMessagePart();\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line1 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line2 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line3 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line4 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line5 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line6 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent = new tw.object.SwiftMessagePart();\nautoObject.swiftMessageData.receiverCorrespondent.line1 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line2 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line3 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line4 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line5 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line6 = \"\";\nautoObject.swiftMessageData.detailsOfPayment = new tw.object.SwiftMessagePart();\nautoObject.swiftMessageData.detailsOfPayment.line1 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line2 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line3 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line4 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line5 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line6 = \"\";\nautoObject.swiftMessageData.orderingInstitution = new tw.object.SwiftMessagePart();\nautoObject.swiftMessageData.orderingInstitution.line1 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line2 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line3 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line4 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line5 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line6 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution = new tw.object.SwiftMessagePart();\nautoObject.swiftMessageData.beneficiaryInstitution.line1 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line2 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line3 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line4 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line5 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line6 = \"\";\nautoObject.swiftMessageData.receiverOfCover = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary = new tw.object.SwiftMessagePart();\nautoObject.swiftMessageData.ultimateBeneficiary.line1 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line2 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line3 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line4 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line5 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line6 = \"\";\nautoObject.swiftMessageData.orderingCustomer = new tw.object.SwiftMessagePart();\nautoObject.swiftMessageData.orderingCustomer.line1 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line2 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line3 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line4 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line5 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line6 = \"\";\nautoObject.swiftMessageData.senderToReciever = new tw.object.SwiftMessagePart();\nautoObject.swiftMessageData.senderToReciever.line1 = \"\";\nautoObject.swiftMessageData.senderToReciever.line2 = \"\";\nautoObject.swiftMessageData.senderToReciever.line3 = \"\";\nautoObject.swiftMessageData.senderToReciever.line4 = \"\";\nautoObject.swiftMessageData.senderToReciever.line5 = \"\";\nautoObject.swiftMessageData.senderToReciever.line6 = \"\";\nautoObject.swiftMessageData.RTGS = \"\";\nautoObject.swiftMessageData.RTGSNetworkType = \"\";\nautoObject.advices = new tw.object.listOf.ContractAdvice();\nautoObject.advices[0] = new tw.object.ContractAdvice();\nautoObject.advices[0].adviceCode = \"\";\nautoObject.advices[0].suppressed = false;\nautoObject.advices[0].advicelines = new tw.object.SwiftMessagePart();\nautoObject.advices[0].advicelines.line1 = \"\";\nautoObject.advices[0].advicelines.line2 = \"\";\nautoObject.advices[0].advicelines.line3 = \"\";\nautoObject.advices[0].advicelines.line4 = \"\";\nautoObject.advices[0].advicelines.line5 = \"\";\nautoObject.advices[0].advicelines.line6 = \"\";\nautoObject.cashCollateralAccounts = new tw.object.listOf.CashCollateralAccount();\nautoObject.cashCollateralAccounts[0] = new tw.object.CashCollateralAccount();\nautoObject.cashCollateralAccounts[0].accountCurrency = \"\";\nautoObject.cashCollateralAccounts[0].accountClass = \"\";\nautoObject.cashCollateralAccounts[0].debitedAmtinCollateralCurrency = 0.0;\nautoObject.cashCollateralAccounts[0].GLAccountNumber = \"\";\nautoObject.cashCollateralAccounts[0].accountBranchCode = \"\";\nautoObject.cashCollateralAccounts[0].accountNumber = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.cashCollateralAccounts[0].accountNumber.name = \"\";\nautoObject.cashCollateralAccounts[0].accountNumber.value = \"\";\nautoObject.cashCollateralAccounts[0].isGLFound = false;\nautoObject.cashCollateralAccounts[0].isGLVerified = false;\nautoObject.IDCRequestStage = \"\";\nautoObject.transactionValueDate = new TWDate();\nautoObject.transactionTenorDays = 0;\nautoObject.contractLimitsTracking = new tw.object.listOf.ContractLimitTracking();\nautoObject.contractLimitsTracking[0] = new tw.object.ContractLimitTracking();\nautoObject.contractLimitsTracking[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.contractLimitsTracking[0].partyType.name = \"\";\nautoObject.contractLimitsTracking[0].partyType.value = \"\";\nautoObject.contractLimitsTracking[0].type = \"\";\nautoObject.contractLimitsTracking[0].jointVentureParent = \"\";\nautoObject.contractLimitsTracking[0].customerNo = \"\";\nautoObject.contractLimitsTracking[0].linkageRefNum = \"\";\nautoObject.contractLimitsTracking[0].amountTag = \"\";\nautoObject.contractLimitsTracking[0].contributionPercentage = 0.0;\nautoObject.contractLimitsTracking[0].isCIFfound = false;\nautoObject.interestFromDate = new TWDate();\nautoObject.interestAmount = 0.0;\nautoObject.haveInterest = false;\nautoObject.accountNumberList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.accountNumberList[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.accountNumberList[0].name = \"\";\nautoObject.accountNumberList[0].value = \"\";\n\/\/autoObject.facilities = new tw.object.listOf.toolkit.NBEC.creditFacilityInformation();\n\/\/autoObject.facilities[0] = new tw.object.toolkit.NBEC.creditFacilityInformation();\n\/\/autoObject.facilities[0].facilityCode = \"\";\n\/\/autoObject.facilities[0].overallLimit = 0.0;\n\/\/autoObject.facilities[0].limitAmount = 0.0;\n\/\/autoObject.facilities[0].effectiveLimitAmount = 0.0;\n\/\/autoObject.facilities[0].availableAmount = 0.0;\n\/\/autoObject.facilities[0].expiryDate = new TWDate();\n\/\/autoObject.facilities[0].availableFlag = false;\n\/\/autoObject.facilities[0].authorizedFlag = false;\n\/\/autoObject.facilities[0].Utilization = 0.0;\n\/\/autoObject.facilities[0].returnCode = \"\";\n\/\/autoObject.facilities[0].facilityLines = new tw.object.listOf.toolkit.NBEC.FacilityLinesDetails();\n\/\/autoObject.facilities[0].facilityLines[0] = new tw.object.toolkit.NBEC.FacilityLinesDetails();\n\/\/autoObject.facilities[0].facilityLines[0].lineCode = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].lineAmount = 0.0;\n\/\/autoObject.facilities[0].facilityLines[0].availableAmount = 0.0;\n\/\/autoObject.facilities[0].facilityLines[0].effectiveLineAmount = 0.0;\n\/\/autoObject.facilities[0].facilityLines[0].expiryDate = new TWDate();\n\/\/autoObject.facilities[0].facilityLines[0].facilityBranch = new tw.object.toolkit.TWSYS.NameValuePair();\n\/\/autoObject.facilities[0].facilityLines[0].facilityBranch.name = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].facilityBranch.value = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].availableFlag = false;\n\/\/autoObject.facilities[0].facilityLines[0].authorizedFlag = false;\n\/\/autoObject.facilities[0].facilityLines[0].facilityPercentageToBook = 0;\n\/\/autoObject.facilities[0].facilityLines[0].internalRemarks = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].purpose = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].LCCommissionPercentage = 0;\n\/\/autoObject.facilities[0].facilityLines[0].LCDef = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].LCCashCover = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].IDCCommission = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].IDCAvalCommPercentage = 0;\n\/\/autoObject.facilities[0].facilityLines[0].IDCCashCoverPercentage = 0;\n\/\/autoObject.facilities[0].facilityLines[0].debitAccountNumber = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].lineCurrency = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].lineSerialNumber = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].returnCode = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].LGCommission = 0;\n\/\/autoObject.facilities[0].facilityLines[0].LGCashCover_BidBond = 0;\n\/\/autoObject.facilities[0].facilityLines[0].LGCashCover_Performance = 0;\n\/\/autoObject.facilities[0].facilityLines[0].LGCashCover_AdvancedPayment = 0;\n\/\/autoObject.facilities[0].facilityLines[0].restrictedCurrencies = new tw.object.listOf.toolkit.NBEC.restrictedItem();\n\/\/autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0] = new tw.object.toolkit.NBEC.restrictedItem();\n\/\/autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].name = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].code = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].source = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].status = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].restrictedBranches = new tw.object.listOf.toolkit.NBEC.restrictedItem();\n\/\/autoObject.facilities[0].facilityLines[0].restrictedBranches[0] = new tw.object.toolkit.NBEC.restrictedItem();\n\/\/autoObject.facilities[0].facilityLines[0].restrictedBranches[0].name = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].restrictedBranches[0].code = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].restrictedBranches[0].source = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].restrictedBranches[0].status = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].restrictedProducts = new tw.object.listOf.toolkit.NBEC.restrictedItem();\n\/\/autoObject.facilities[0].facilityLines[0].restrictedProducts[0] = new tw.object.toolkit.NBEC.restrictedItem();\n\/\/autoObject.facilities[0].facilityLines[0].restrictedProducts[0].name = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].restrictedProducts[0].code = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].restrictedProducts[0].source = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].restrictedProducts[0].status = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].restrictedCustomers = new tw.object.listOf.toolkit.NBEC.restrictedItem();\n\/\/autoObject.facilities[0].facilityLines[0].restrictedCustomers[0] = new tw.object.toolkit.NBEC.restrictedItem();\n\/\/autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].name = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].code = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].source = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].status = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].exposureRestrictions = new tw.object.listOf.toolkit.NBEC.restrictedItem();\n\/\/autoObject.facilities[0].facilityLines[0].exposureRestrictions[0] = new tw.object.toolkit.NBEC.restrictedItem();\n\/\/autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].name = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].code = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].source = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].status = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].CIF = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();\n\/\/autoObject.facilities[0].facilityLines[0].partyType.name = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].partyType.value = \"\";\n\/\/autoObject.facilities[0].facilityLines[0].facilityID = \"\";\n\/\/autoObject.facilities[0].status = \"\";\n\/\/autoObject.facilities[0].facilityCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\n\/\/autoObject.facilities[0].facilityCurrency.name = \"\";\n\/\/autoObject.facilities[0].facilityCurrency.value = \"\";\n\/\/autoObject.facilities[0].facilityID = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1","name":"idcContract","isCollection":false,"id":"2055.8dc5d463-89eb-4f51-8d45-5412d38d92ae"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="IDCRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.1cb68f60-25be-43eb-8983-faa1eebbd945</processParameterId>
            <processId>1.e75c339e-472e-439b-878f-bfafd0c4b968</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a5459184-9485-4050-80ef-2974c69fafb4</guid>
            <versionId>b1556ceb-993e-4291-9974-90d529c5f852</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.0b80fea0-48fa-48ca-9b76-88a1104c7d48</processParameterId>
            <processId>1.e75c339e-472e-439b-878f-bfafd0c4b968</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>02be3272-5d27-4faf-b575-c0b63cc412c5</guid>
            <versionId>a3b87db6-03a2-445f-84d5-04d2bb78ed0c</versionId>
        </processParameter>
        <processParameter name="idcContract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.8dc5d463-89eb-4f51-8d45-5412d38d92ae</processParameterId>
            <processId>1.e75c339e-472e-439b-878f-bfafd0c4b968</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>8d1035f0-bc2e-49f7-98e3-5006b43348a0</guid>
            <versionId>eebcfc5a-15f3-4e11-a334-eb917e898fcb</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.6d23c29f-dcc7-4d77-81e8-3c00f3db633d</processParameterId>
            <processId>1.e75c339e-472e-439b-878f-bfafd0c4b968</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>335336d8-6e9c-40e9-8853-1134425f141f</guid>
            <versionId>f04a97be-aef6-476e-a26d-bc2e25549549</versionId>
        </processParameter>
        <processVariable name="sqlStatements">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b9206c57-ebfa-44da-80f2-45a474a00a3f</processVariableId>
            <description isNull="true" />
            <processId>1.e75c339e-472e-439b-878f-bfafd0c4b968</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3990d8b4-983d-4250-bd06-3600f527fed0</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>833f0035-446e-4cfe-8134-9a378bdf7508</guid>
            <versionId>8a70b116-ec0b-4d66-bf7d-d0c7622f5934</versionId>
        </processVariable>
        <processVariable name="i">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.48790657-e9c4-4474-8390-840e19f82662</processVariableId>
            <description isNull="true" />
            <processId>1.e75c339e-472e-439b-878f-bfafd0c4b968</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>0</defaultValue>
            <guid>1d806a46-f358-41a0-8cc2-017bd2d2a0ef</guid>
            <versionId>d55ba7c9-4421-46df-9834-61fc38917314</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5c0a8709-3c23-4859-89f0-37d37667fd87</processVariableId>
            <description isNull="true" />
            <processId>1.e75c339e-472e-439b-878f-bfafd0c4b968</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3593242c-860a-4919-aa8e-fe6d1a15b40a</guid>
            <versionId>b0203b61-f4f7-4d21-903a-b010d3e0a6fa</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.08188f42-8534-47c7-83a5-bf60b20530c5</processItemId>
            <processId>1.e75c339e-472e-439b-878f-bfafd0c4b968</processId>
            <name>UPDATE IDC Request Q</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.4eb55b60-e48f-4145-a227-d75b3fba65b8</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.e89eb804-d511-49e2-8947-ba49fc792260</errorHandlerItemId>
            <guid>guid:e5873b94e72cc4db:-66d3a9fb:189cfb43e32:-7075</guid>
            <versionId>2082285f-d594-49a5-bc09-e15562b08f41</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="118" y="55">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-637e</errorHandlerItem>
                <errorHandlerItemId>2025.e89eb804-d511-49e2-8947-ba49fc792260</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.4eb55b60-e48f-4145-a227-d75b3fba65b8</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>&#xD;
tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
tw.local.sqlStatements[0] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[0].sql = "UPDATE BPM.IDC_REQUEST_DETAILS SET REQUEST_NUMBER = ?, PARENT_REQUEST_NUMBER = ?, BPM_INSTANCE_NUMBER = ?, REQUEST_INITIATOR = ?, REQUEST_BRANCH_HUB = ?, REQUEST_NATURE_ID = ?, REQUEST_TYPE_ID = ?, REQUEST_DATE = ?, REQUEST_STATE = ?, REQUEST_STAGE = ?, REQUEST_STATUS = ?, REQUEST_SUB_STATUS = ?, FLEX_CUBE_CONTRACT_NUMBER = ?, IS_WITHDRAWEN = ?, IMPORT_PURPOSE_ID = ?, PAYMENT_TERMS_ID = ?, DOCUMENTS_SOURCE_ID = ?, PRODUCT_CATEGORY_ID = ?, COMMODITY_DESCRIPTION = ?, ADVANCE_PAYMENT_OUTSTANDING_AMOUNT = ?, DOCUMENT_AMOUNT = ?, CURRENCY = ?, CHARGES_ACCOUNT = ?, SOURCE_OF_FOREIGN_CURRENCY_ID = ?, SOURCE_OF_FUNDS_ID = ?, PAYMENT_ACCOUNT = ?, CASH_AMOUNT_DOCUMENT_CURRENCY = ?, CASH_AMOUNT_NO_CURRENCY = ?, FACILITY_AMOUNT_DOCUMENT_CURRENCY = ?, FACILITY_AMOUNT_NO_CURRENCY = ?, DISCOUNT = ?, AMOUNT_ADVANCED = ?, AMOUNT_PAID_BY_OTHER_BANKS = ?, AMOUNT_SIGHT = ?, AMOUNT_DEFERRED_NO_AVALIZATION = ?, AMOUNT_DEFERRED_AVALIZATION = ?, AMOUNT_PAYABLE_BY_NBE = ?, FIRST_INSTALLEMENT_MATURITY_DATE = ?, NO_OF_DAYS_TILL_MATURITY = ?, BENEFICIARY_NAME = ?, BENEFICIARY_BANK = ?, BENEFICIARY_IBAN = ?, BENEFICIARY_COUNTRY_CODE = ?, CORRESPONDENT_REF_NUM = ?, TRADE_FO_REFERENCE_NUMBER = ?, TRADE_FU_APPROVAL_NO = ?, EXECUTION_HUB_CODE = ?, EXECUTION_HUB_NAME = ?, SHIPPING_DATE = ?, INCOTERMS = ?, SHIPMENT_METHOD_ID = ?, DESTINATION_PORT = ?, CBE_COMMODITY_CLASSIFICATION_ID = ?, HS_CODE = ?, HS_DESCRIPTION = ?, ACID = ?, COUNTRY_OF_ORIGIN_CODE = ? WHERE ID = ?";&#xD;
&#xD;
tw.local.sqlStatements[0].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.i = 0;&#xD;
function paramInit (value) {&#xD;
	tw.local.sqlStatements[0].parameters[tw.local.i] = new tw.object.SQLParameter();&#xD;
//	tw.local.sqlStatements[0].parameters[tw.local.i].type = type;&#xD;
	tw.local.sqlStatements[0].parameters[tw.local.i].value = value;&#xD;
//	tw.local.sqlStatements[0].parameters[tw.local.i].mode = "IN";&#xD;
	tw.local.i= tw.local.i+1;&#xD;
//	return tw.local.parameter;&#xD;
}&#xD;
paramInit (tw.local.IDCRequest.appInfo.instanceID);&#xD;
paramInit (tw.local.IDCRequest.ParentIDCRequestNumber);&#xD;
paramInit (tw.local.IDCRequest.appInfo.appID);&#xD;
paramInit (tw.local.IDCRequest.appInfo.initiator);&#xD;
paramInit (tw.local.IDCRequest.appInfo.branch.name);&#xD;
paramInit (tw.local.IDCRequest.IDCRequestNature.id);&#xD;
paramInit (tw.local.IDCRequest.IDCRequestType.id);&#xD;
paramInit (tw.local.IDCRequest.requestDate);&#xD;
paramInit (tw.local.IDCRequest.IDCRequestState);&#xD;
paramInit (tw.local.IDCRequest.IDCRequestStage);&#xD;
paramInit (tw.local.IDCRequest.appInfo.status);&#xD;
paramInit (tw.local.IDCRequest.appInfo.subStatus);&#xD;
paramInit (tw.local.IDCRequest.FCContractNumber);&#xD;
if (tw.local.IDCRequest.isIDCWithdrawn == true) {&#xD;
	paramInit (1);&#xD;
	&#xD;
}else{&#xD;
	paramInit (0);&#xD;
	&#xD;
}&#xD;
paramInit (tw.local.IDCRequest.importPurpose.id);&#xD;
paramInit (tw.local.IDCRequest.paymentTerms.id);&#xD;
paramInit (tw.local.IDCRequest.documentsSource.id);&#xD;
paramInit (tw.local.IDCRequest.productCategory.id);&#xD;
paramInit (tw.local.IDCRequest.commodityDescription);&#xD;
if (tw.local.IDCRequest.IDCRequestType.englishdescription == "Advance Payment") {&#xD;
	paramInit (tw.local.IDCRequest.financialDetails.amtPayableByNBE);&#xD;
	&#xD;
}else{&#xD;
	paramInit (0.0);&#xD;
	&#xD;
}&#xD;
paramInit (tw.local.IDCRequest.financialDetails.documentAmount);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.documentCurrency.code);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.chargesAccount);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.sourceOfForeignCurrency.id);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.sourceOfFunds.id);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.paymentAccount);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.cashAmtInDocCurrency);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.CashAmtWithNoCurrency);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.facilityAmtInDocCurrency);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.facilityAmtWithNoCurrency);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.discountAmt);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.amountAdvanced);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.amtPaidbyOtherBanks);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.amtSight);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.amtDeferredNoAvalized);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.amtDeferredAvalized);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.amtPayableByNBE);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.firstInstallementMaturityDate);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.daysTillMaturity);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.beneficiaryDetails.name);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.beneficiaryDetails.bank);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.beneficiaryDetails.account);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.beneficiaryDetails.country.code);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.beneficiaryDetails.correspondentRefNum);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.tradeFOReferenceNumber);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.tradeFinanceApprovalNumber);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.executionHub.code);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.executionHub.arabicdescription);&#xD;
paramInit (tw.local.IDCRequest.productsDetails.shippingDate);&#xD;
if (tw.local.IDCRequest.productsDetails.incoterms.id == 0) {&#xD;
	paramInit (null);&#xD;
} else {&#xD;
	paramInit (tw.local.IDCRequest.productsDetails.incoterms.id);&#xD;
}&#xD;
&#xD;
if (tw.local.IDCRequest.productsDetails.shipmentMethod.id == 0) {&#xD;
	paramInit (null);&#xD;
} else {&#xD;
	paramInit (tw.local.IDCRequest.productsDetails.shipmentMethod.id);&#xD;
}&#xD;
&#xD;
paramInit (tw.local.IDCRequest.productsDetails.destinationPort);&#xD;
if (tw.local.IDCRequest.productsDetails.CBECommodityClassification.id == 0) {&#xD;
	paramInit (null);&#xD;
} else {&#xD;
	paramInit (tw.local.IDCRequest.productsDetails.CBECommodityClassification.id);&#xD;
}&#xD;
&#xD;
paramInit (tw.local.IDCRequest.productsDetails.HSProduct.code);&#xD;
paramInit (tw.local.IDCRequest.productsDetails.HSProduct.englishdescription);&#xD;
paramInit (tw.local.IDCRequest.productsDetails.ACID);&#xD;
paramInit (tw.local.IDCRequest.countryOfOrigin.code);&#xD;
&#xD;
paramInit (tw.local.IDCRequest.DBID);&#xD;
//-------------------------------------------------------------------------------------------&#xD;
</script>
                <isRule>false</isRule>
                <guid>28855b9a-6de0-4c53-be60-18d2f94071b9</guid>
                <versionId>b7412d54-b4f2-4312-90dc-3d64f80f426e</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.485c1cfa-b21c-4f9b-8c38-408c70d91afd</processItemId>
            <processId>1.e75c339e-472e-439b-878f-bfafd0c4b968</processId>
            <name>UPDATE IDC Request</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.0f70af28-f5a8-4284-83ab-cbea9e4cfdf5</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.e89eb804-d511-49e2-8947-ba49fc792260</errorHandlerItemId>
            <guid>guid:e5873b94e72cc4db:-66d3a9fb:189cfb43e32:-75ee</guid>
            <versionId>23e0bc67-5572-4d56-8f74-c73a3438d813</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FFC875</nodeColor>
            <layoutData x="260" y="56">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-637e</errorHandlerItem>
                <errorHandlerItemId>2025.e89eb804-d511-49e2-8947-ba49fc792260</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.0f70af28-f5a8-4284-83ab-cbea9e4cfdf5</subProcessId>
                <attachedProcessRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7</attachedProcessRef>
                <guid>1f786cf9-3701-4b54-81d8-64036c2138f5</guid>
                <versionId>60c93516-e8a9-4130-89fb-6bdf07df3755</versionId>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.98b308fa-1a1e-4969-b25a-5ef1e01cbeb7</parameterMappingId>
                    <processParameterId>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</processParameterId>
                    <parameterMappingParentId>3012.0f70af28-f5a8-4284-83ab-cbea9e4cfdf5</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>f761d957-d59e-4461-bf7a-f9db9324213d</guid>
                    <versionId>5e4b0342-fea6-46b7-9ba1-429eac632116</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sqlStatements">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d69b9ab6-472f-4465-a37a-edab52c268c7</parameterMappingId>
                    <processParameterId>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</processParameterId>
                    <parameterMappingParentId>3012.0f70af28-f5a8-4284-83ab-cbea9e4cfdf5</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlStatements</value>
                    <classRef>/12.3990d8b4-983d-4250-bd06-3600f527fed0</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>8d21e419-6a4a-4016-9239-77d21529d84b</guid>
                    <versionId>62903c62-964a-4a13-aaf8-80b46673d155</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.05d40ad1-f16d-43f3-b7fe-720843d77ce9</parameterMappingId>
                    <processParameterId>2055.a634f531-c979-476c-91db-a17bf5e55c90</processParameterId>
                    <parameterMappingParentId>3012.0f70af28-f5a8-4284-83ab-cbea9e4cfdf5</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>50135b1c-28b1-43d8-be04-d5bb0ebae348</guid>
                    <versionId>a570dfa8-6146-4fcf-b87f-1f36a1dc4e3d</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.e89eb804-d511-49e2-8947-ba49fc792260</processItemId>
            <processId>1.e75c339e-472e-439b-878f-bfafd0c4b968</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.60224638-72ce-4475-b5e5-4a4fd443ce63</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-637e</guid>
            <versionId>31024bb5-2f92-4452-9f10-356e8f14c3a8</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.3ae17196-c0e5-49b2-907a-13691fb60b64</processItemPrePostId>
                <processItemId>2025.e89eb804-d511-49e2-8947-ba49fc792260</processItemId>
                <location>1</location>
                <script>log.info("*============ IDC =============*");&#xD;
log.info("[Update IDC Request -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
&#xD;
tw.local.error = new tw.object.AjaxError();&#xD;
var attribute = String(tw.system.error.getAttribute("type"));&#xD;
var element = String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
tw.local.errorMSG = attribute + "," + element;&#xD;
//tw.local.errorMSG =String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Update IDC Request -&gt; Log Error ]- END");&#xD;
log.info("*============================================*");&#xD;
&#xD;
tw.local.error.errorText = tw.local.errorMSG;</script>
                <guid>de774e9e-8483-4014-b034-34604ded21f2</guid>
                <versionId>bcd69ac1-47be-4af5-9ba5-ca7f8cd39d4f</versionId>
            </processPrePosts>
            <layoutData x="531" y="190">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.60224638-72ce-4475-b5e5-4a4fd443ce63</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>d181859c-8f20-4aba-a361-1a1e7a628471</guid>
                <versionId>289a4ff9-188f-4178-ba7b-992037799d74</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a6d014f7-2cda-473a-8f8c-a09cf6c5cd53</parameterMappingId>
                    <processParameterId>2055.0b80fea0-48fa-48ca-9b76-88a1104c7d48</processParameterId>
                    <parameterMappingParentId>3007.60224638-72ce-4475-b5e5-4a4fd443ce63</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>4df4fe2f-e5f4-4488-8819-8714def2051d</guid>
                    <versionId>a194f3c3-d694-43da-905f-f8e5b9e391d9</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.e66fcbcf-ea21-45ab-86e1-f555f1a2bb7a</processItemId>
            <processId>1.e75c339e-472e-439b-878f-bfafd0c4b968</processId>
            <name>INSERT Invoices &amp; Bills</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.7c42f0e8-c498-4565-a188-6c8c7a509c7c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.e89eb804-d511-49e2-8947-ba49fc792260</errorHandlerItemId>
            <guid>guid:e5873b94e72cc4db:-66d3a9fb:189cfb43e32:-75ec</guid>
            <versionId>3e16460b-e023-475f-90d2-798824e2acdc</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FFC875</nodeColor>
            <layoutData x="929" y="56">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error5</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-637e</errorHandlerItem>
                <errorHandlerItemId>2025.e89eb804-d511-49e2-8947-ba49fc792260</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.7c42f0e8-c498-4565-a188-6c8c7a509c7c</subProcessId>
                <attachedProcessRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7</attachedProcessRef>
                <guid>ba115abf-e5a9-4dc3-b149-43025c7f4021</guid>
                <versionId>a606035d-6e82-4e71-b2e8-3c9d31906847</versionId>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.59a17b93-124d-4af4-aa44-437dc5a382af</parameterMappingId>
                    <processParameterId>2055.a634f531-c979-476c-91db-a17bf5e55c90</processParameterId>
                    <parameterMappingParentId>3012.7c42f0e8-c498-4565-a188-6c8c7a509c7c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>3dc7ae32-163d-4c5c-b16d-80c76b90d856</guid>
                    <versionId>557fbb53-c28a-428c-a2c1-6f59a44838dd</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.34b9f018-ee82-4d7e-a7be-6de3603670be</parameterMappingId>
                    <processParameterId>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</processParameterId>
                    <parameterMappingParentId>3012.7c42f0e8-c498-4565-a188-6c8c7a509c7c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>1fe55bc3-824d-4d99-89c1-1ed9bcb97551</guid>
                    <versionId>9fc721c6-5219-4fb8-9f23-3570474be0db</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sqlStatements">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.336b22e6-e677-496c-bf84-be9f04d9d2f0</parameterMappingId>
                    <processParameterId>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</processParameterId>
                    <parameterMappingParentId>3012.7c42f0e8-c498-4565-a188-6c8c7a509c7c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlStatements</value>
                    <classRef>/12.3990d8b4-983d-4250-bd06-3600f527fed0</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>a5817313-a11d-4f9a-94ec-237f94fa95f1</guid>
                    <versionId>ccb70495-6777-459e-9a04-84f4c0fa3199</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.360f095c-c838-407d-8326-f25a1a115a02</processItemId>
            <processId>1.e75c339e-472e-439b-878f-bfafd0c4b968</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.4baa89ff-ff87-4ca0-a28b-3e9ce0bcbe8f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:e5873b94e72cc4db:-66d3a9fb:189cfb43e32:-7712</guid>
            <versionId>699582ea-2fd5-405e-80d4-326e36e29c5f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="1190" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.4baa89ff-ff87-4ca0-a28b-3e9ce0bcbe8f</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>cc29503c-b916-46b4-998d-cb0002641cfe</guid>
                <versionId>5064d255-5e1b-4bca-a21c-46e039486194</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6322d481-5d75-47ff-8b5f-56cada0079b8</processItemId>
            <processId>1.e75c339e-472e-439b-878f-bfafd0c4b968</processId>
            <name>INSERT Q</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.c80486db-adf1-4639-9568-703b405a7e7a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.e89eb804-d511-49e2-8947-ba49fc792260</errorHandlerItemId>
            <guid>guid:e5873b94e72cc4db:-66d3a9fb:189cfb43e32:-7576</guid>
            <versionId>ae507875-994e-4dc0-807a-0a5f12aa9733</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="758" y="55">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error4</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-637e</errorHandlerItem>
                <errorHandlerItemId>2025.e89eb804-d511-49e2-8947-ba49fc792260</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.c80486db-adf1-4639-9568-703b405a7e7a</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var j = 0&#xD;
function paramInit (type,value) {&#xD;
	tw.local.sqlStatements[j].parameters[tw.local.i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[tw.local.i].type = type;&#xD;
	tw.local.sqlStatements[j].parameters[tw.local.i].value = value;&#xD;
	tw.local.i= tw.local.i+1;&#xD;
}&#xD;
//-----------------------------invoice--------------------------------------------------&#xD;
&#xD;
for (j=0; j&lt;tw.local.IDCRequest.invoices.listLength; j++) {&#xD;
	tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
	tw.local.sqlStatements[j].sql = "INSERT INTO BPM.IDC_REQUEST_INVOICES (IDC_REQUEST_ID, INVOICE_NUMBER, INVOICE_DATE) VALUES (?,?,?) ;";&#xD;
	&#xD;
	tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
	tw.local.i = 0;&#xD;
	&#xD;
	paramInit("INTEGER",tw.local.IDCRequest.DBID);&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.invoices[j].number);&#xD;
	paramInit("DATE",tw.local.IDCRequest.invoices[j].date);&#xD;
}&#xD;
&#xD;
////-----------------------------bill-----------------------------------------------------&#xD;
var n = 0;&#xD;
// j++;&#xD;
for (; j&lt;(tw.local.IDCRequest.billOfLading.listLength + tw.local.IDCRequest.invoices.listLength); j++) {&#xD;
	tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
	tw.local.sqlStatements[j].sql = "INSERT INTO BPM.IDC_REQUEST_BILLS (IDC_REQUEST_ID, BILL_OF_LADING_REF, BILL_OF_LADING_DATE) VALUES (?,?,?) ;";&#xD;
	&#xD;
	tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
	tw.local.i = 0;&#xD;
	&#xD;
	paramInit("INTEGER",tw.local.IDCRequest.DBID);&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.billOfLading[n].number);&#xD;
	paramInit("DATE",tw.local.IDCRequest.billOfLading[n].date);&#xD;
	n+=1;&#xD;
}&#xD;
&#xD;
////-----------------------------UPDATE customer---------------------------------------------------&#xD;
// j+=1;&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].sql = "UPDATE BPM.IDC_CUSTOMER_INFORMATION SET CIF = ?, CUSTOMER_NAME = ?, CUSTOMER_SECTOR = ?, CUSTOMER_TYPE = ?, CUSTOMER_NUMBER_AT_CBE = ?, FACILITY_TYPE_ID = ?, COMMERCIAL_REGISTRATION_NUMBER = ?, COMMERCIAL_REGISTRATION_OFFICE = ?, TAX_CARD_NUMBER = ?, IMPORT_CARD_NUMBER = ? WHERE IDC_REQUEST_ID = ?;";&#xD;
&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.i = 0;&#xD;
&#xD;
paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.CIFNumber);&#xD;
paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.customerName);&#xD;
paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.customerSector);//can be update later &lt;----------------------------------------------------&#xD;
paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.customerType);&#xD;
paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.CBENumber);&#xD;
paramInit("INTEGER",tw.local.IDCRequest.customerInformation.facilityType.id);&#xD;
paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.commercialRegistrationNumber);&#xD;
paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.commercialRegistrationOffice);&#xD;
paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.taxCardNumber);&#xD;
paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.importCardNumber);&#xD;
&#xD;
paramInit("INTEGER",tw.local.IDCRequest.DBID);&#xD;
//-----------------------------------------facility-----------------------------------------------------------&#xD;
//if (tw.local.idcContract.facilities != null &amp;&amp; tw.local.idcContract.facilities != undefined) {&#xD;
	if (tw.local.idcContract.facilities.listLength &gt; 0) {&#xD;
		j++;&#xD;
		for (var m_f_len=0; m_f_len &lt; tw.local.idcContract.facilities.listLength; m_f_len++) {&#xD;
			for (var s_f_len=0; s_f_len &lt; tw.local.idcContract.facilities[m_f_len].facilityLines.listLength; s_f_len++) {&#xD;
				tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
				tw.local.sqlStatements[j].sql = "INSERT INTO BPM.IDC_FACILITY_DETAILS (IDC_REQUEST_ID, FACILITY_ID, CURRENCY, LINE_SERIAL, LINE_CODE, FACILITY_PERCENTAGE_TO_BOOK) VALUES (?,?,?,?,?,?) ;";&#xD;
				&#xD;
				tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
				tw.local.i = 0;&#xD;
				paramInit("INTEGER",tw.local.IDCRequest.DBID);&#xD;
				paramInit("VARCHAR",tw.local.idcContract.facilities[m_f_len].facilityID);&#xD;
				paramInit("VARCHAR",tw.local.idcContract.facilities[m_f_len].facilityCurrency.value);&#xD;
				paramInit("VARCHAR",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].lineSerialNumber);&#xD;
				paramInit("VARCHAR",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].lineCode);&#xD;
				paramInit("DECIMAL",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].facilityPercentageToBook);&#xD;
				j++;&#xD;
			}&#xD;
		}&#xD;
	}&#xD;
	&#xD;
&#xD;
//}&#xD;
</script>
                <isRule>false</isRule>
                <guid>64987af5-16ff-4a84-907f-ba94af6d1cbf</guid>
                <versionId>64593e4f-4408-49cc-b208-9729c2fb9263</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8c791fc3-3fec-4c45-8b19-58d174daab20</processItemId>
            <processId>1.e75c339e-472e-439b-878f-bfafd0c4b968</processId>
            <name>DELETE Q</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.ce5195d2-0de1-4255-a18b-9e81f8096ca2</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.e89eb804-d511-49e2-8947-ba49fc792260</errorHandlerItemId>
            <guid>guid:e5873b94e72cc4db:-66d3a9fb:189cfb43e32:-7577</guid>
            <versionId>af18e658-3738-4b0f-b9a0-eb2ad4b69dea</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="427" y="55">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-637e</errorHandlerItem>
                <errorHandlerItemId>2025.e89eb804-d511-49e2-8947-ba49fc792260</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.ce5195d2-0de1-4255-a18b-9e81f8096ca2</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
&#xD;
//---------------------------------Invoices------------------------------------------------------&#xD;
tw.local.sqlStatements[0] = {};&#xD;
tw.local.sqlStatements[0].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[0].parameters[0] = {};&#xD;
tw.local.sqlStatements[0].parameters[0].value = tw.local.IDCRequest.DBID;&#xD;
tw.local.sqlStatements[0].sql = "DELETE FROM BPM.IDC_REQUEST_INVOICES WHERE IDC_REQUEST_ID = ?"&#xD;
&#xD;
//---------------------------------Bills-----------------------------------------------------------&#xD;
tw.local.sqlStatements[1] = {};&#xD;
tw.local.sqlStatements[1].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[1].parameters[0] = {};&#xD;
tw.local.sqlStatements[1].parameters[0].value = tw.local.IDCRequest.DBID;&#xD;
tw.local.sqlStatements[1].sql = "DELETE FROM BPM.IDC_REQUEST_BILLS WHERE IDC_REQUEST_ID = ?"&#xD;
//--------------------------------------------------------------------------------------------------&#xD;
tw.local.sqlStatements[2] = {};&#xD;
tw.local.sqlStatements[2].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[2].parameters[0] = {};&#xD;
tw.local.sqlStatements[2].parameters[0].value = tw.local.IDCRequest.DBID;&#xD;
tw.local.sqlStatements[2].sql = "DELETE FROM BPM.IDC_FACILITY_DETAILS WHERE IDC_REQUEST_ID = ?"</script>
                <isRule>false</isRule>
                <guid>accb9fe5-2a18-4a4b-a7eb-b85062f9a72e</guid>
                <versionId>5cbd8a5d-e947-4084-9810-81b1d35dc6e1</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.a8fb1bfb-58c7-4ce7-8f29-137efb1dc5d9</processItemId>
            <processId>1.e75c339e-472e-439b-878f-bfafd0c4b968</processId>
            <name>DELETE Invoices &amp; Bills</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.1cc5d0b2-210f-4fa3-b888-119f02673feb</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.e89eb804-d511-49e2-8947-ba49fc792260</errorHandlerItemId>
            <guid>guid:e5873b94e72cc4db:-66d3a9fb:189cfb43e32:-75ed</guid>
            <versionId>bc53d2bd-ccd4-44ee-8d31-28092d1cbe77</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FFC875</nodeColor>
            <layoutData x="576" y="56">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error3</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-637e</errorHandlerItem>
                <errorHandlerItemId>2025.e89eb804-d511-49e2-8947-ba49fc792260</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.1cc5d0b2-210f-4fa3-b888-119f02673feb</subProcessId>
                <attachedProcessRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7</attachedProcessRef>
                <guid>35a95157-9d33-413c-b170-5fd206fc7669</guid>
                <versionId>2c02d696-3baf-4a6f-82de-fa840b639d78</versionId>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.bcac1364-593b-4cb6-b079-31ae8b1e6aaf</parameterMappingId>
                    <processParameterId>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</processParameterId>
                    <parameterMappingParentId>3012.1cc5d0b2-210f-4fa3-b888-119f02673feb</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>84beed59-7afc-453c-84a9-28cfcbed34a5</guid>
                    <versionId>b2d786dc-fe7e-4f16-a891-85009021c61f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.177c1122-e6fd-4064-a31b-6e4943b827a9</parameterMappingId>
                    <processParameterId>2055.a634f531-c979-476c-91db-a17bf5e55c90</processParameterId>
                    <parameterMappingParentId>3012.1cc5d0b2-210f-4fa3-b888-119f02673feb</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>a5bdd63b-d8c9-4a3e-bca6-5e9defa7e242</guid>
                    <versionId>c9f33e80-f123-4f37-be4b-fa2bcff64971</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sqlStatements">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.2233de4c-2533-4d9a-8bd6-8602f85f027e</parameterMappingId>
                    <processParameterId>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</processParameterId>
                    <parameterMappingParentId>3012.1cc5d0b2-210f-4fa3-b888-119f02673feb</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlStatements</value>
                    <classRef>/12.3990d8b4-983d-4250-bd06-3600f527fed0</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>dc5ce47f-79b4-42ea-9518-ac5e622a5eb9</guid>
                    <versionId>f73bdfdb-777e-4d46-9f76-611b940f0f22</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.08188f42-8534-47c7-83a5-bf60b20530c5</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Update IDC Request" id="1.e75c339e-472e-439b-878f-bfafd0c4b968" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="IDCRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.1cb68f60-25be-43eb-8983-faa1eebbd945">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">var autoObject = new tw.object.IDCRequest();&#xD;
autoObject.IDCRequestState = "Final";&#xD;
autoObject.commodityDescription = "commo";&#xD;
autoObject.countryOfOrigin = new tw.object.DBLookup();&#xD;
autoObject.countryOfOrigin.id = 1;&#xD;
autoObject.countryOfOrigin.code = "EG";&#xD;
autoObject.countryOfOrigin.arabicdescription = "مصر";&#xD;
autoObject.countryOfOrigin.englishdescription = "ُEgypt";&#xD;
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();&#xD;
autoObject.appInfo.requestDate = "12-08-2023";&#xD;
autoObject.appInfo.status = "status";&#xD;
autoObject.appInfo.subStatus = "subStatus";&#xD;
autoObject.appInfo.initiator = "initiator";&#xD;
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.appInfo.branch.name = "001";&#xD;
autoObject.appInfo.branch.value = "001";&#xD;
autoObject.appInfo.requestName = "ICAP";&#xD;
autoObject.appInfo.requestType = "ICAP";&#xD;
autoObject.appInfo.stepName = "stepName";&#xD;
autoObject.appInfo.appRef = "appRef";&#xD;
autoObject.appInfo.appID = "85634";&#xD;
autoObject.appInfo.instanceID = "00102230000220";&#xD;
autoObject.productsDetails = new tw.object.ProductsDetails();&#xD;
autoObject.productsDetails.destinationPort = "destinationPort";&#xD;
autoObject.productsDetails.shippingDate = new TWDate();&#xD;
autoObject.productsDetails.HSProduct = new tw.object.DBLookup();&#xD;
autoObject.productsDetails.HSProduct.id = 1;&#xD;
autoObject.productsDetails.HSProduct.code = "0101 – 0106";&#xD;
autoObject.productsDetails.HSProduct.arabicdescription = "arabic";&#xD;
autoObject.productsDetails.HSProduct.englishdescription = "Live animals";&#xD;
autoObject.productsDetails.incoterms = new tw.object.DBLookup();&#xD;
autoObject.productsDetails.incoterms.id = 2;&#xD;
autoObject.productsDetails.incoterms.code = "FCA";&#xD;
autoObject.productsDetails.incoterms.arabicdescription = "arabicdon";&#xD;
autoObject.productsDetails.incoterms.englishdescription = "Free to Carrier";&#xD;
autoObject.productsDetails.ACID = "ACID";&#xD;
autoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();&#xD;
autoObject.productsDetails.CBECommodityClassification.id = 3;&#xD;
autoObject.productsDetails.CBECommodityClassification.code = "003";&#xD;
autoObject.productsDetails.CBECommodityClassification.arabicdescription = "الالات و المعدات";&#xD;
autoObject.productsDetails.CBECommodityClassification.englishdescription = "english";&#xD;
autoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();&#xD;
autoObject.productsDetails.shipmentMethod.id = 4;&#xD;
autoObject.productsDetails.shipmentMethod.code = "004";&#xD;
autoObject.productsDetails.shipmentMethod.arabicdescription = "بريد سريع";&#xD;
autoObject.productsDetails.shipmentMethod.englishdescription = "english";&#xD;
autoObject.financialDetails = new tw.object.FinancialDetails();&#xD;
autoObject.financialDetails.isAdvancePaymentsUsed = false;&#xD;
autoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();&#xD;
autoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();&#xD;
autoObject.financialDetails.paymentTerms[0].installmentAmount = 1.1;&#xD;
autoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();&#xD;
autoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();&#xD;
autoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();&#xD;
autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency =1.0;&#xD;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();&#xD;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id =1;&#xD;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = "EGP";&#xD;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = "جنيه مصرى";&#xD;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = "Egyptian Pound";&#xD;
autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated =1.0;&#xD;
autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = "advance";&#xD;
autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount =1.0;&#xD;
autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = "invoicer";&#xD;
autoObject.financialDetails.usedAdvancePayment[0].paidAmount =1.0;&#xD;
autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = "benefic";&#xD;
autoObject.financialDetails.usedAdvancePayment[0].documentAmount =1.0;&#xD;
autoObject.financialDetails.discountAmt =77.0;&#xD;
autoObject.financialDetails.firstInstallementMaturityDate = new TWDate();&#xD;
autoObject.financialDetails.facilityAmtInDocCurrency =1.0;&#xD;
autoObject.financialDetails.amtSight =1.0;&#xD;
autoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();&#xD;
autoObject.financialDetails.beneficiaryDetails.account = "account";&#xD;
autoObject.financialDetails.beneficiaryDetails.bank = "bank";&#xD;
autoObject.financialDetails.beneficiaryDetails.correspondentRefNum = "corresp";&#xD;
autoObject.financialDetails.beneficiaryDetails.name = "beneficiary";&#xD;
autoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();&#xD;
autoObject.financialDetails.beneficiaryDetails.country.id =1;&#xD;
autoObject.financialDetails.beneficiaryDetails.country.code = "EG";&#xD;
autoObject.financialDetails.beneficiaryDetails.country.arabicdescription = "مصر";&#xD;
autoObject.financialDetails.beneficiaryDetails.country.englishdescription = "Egypt";&#xD;
autoObject.financialDetails.amtDeferredNoAvalized =1.0;&#xD;
autoObject.financialDetails.amtPayableByNBE =1.0;&#xD;
autoObject.financialDetails.executionHub = new tw.object.DBLookup();&#xD;
autoObject.financialDetails.executionHub.id =1;&#xD;
autoObject.financialDetails.executionHub.code = "077";&#xD;
autoObject.financialDetails.executionHub.arabicdescription = "وحدة اعتمادات البرج";&#xD;
autoObject.financialDetails.executionHub.englishdescription = "english";&#xD;
autoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();&#xD;
autoObject.financialDetails.sourceOfForeignCurrency.id =1;&#xD;
autoObject.financialDetails.sourceOfForeignCurrency.code = "001";&#xD;
autoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = "لا يوجد عملة اجنبية";&#xD;
autoObject.financialDetails.sourceOfForeignCurrency.englishdescription = "No Foreign Currency";&#xD;
autoObject.financialDetails.facilityAmtWithNoCurrency =1.0;&#xD;
autoObject.financialDetails.documentCurrency = new tw.object.DBLookup();&#xD;
autoObject.financialDetails.documentCurrency.id =1;&#xD;
autoObject.financialDetails.documentCurrency.code = "EGP";&#xD;
autoObject.financialDetails.documentCurrency.arabicdescription = "جنيه مصرى";&#xD;
autoObject.financialDetails.documentCurrency.englishdescription = "Egyptian Pound";&#xD;
autoObject.financialDetails.daysTillMaturity =1;&#xD;
autoObject.financialDetails.tradeFinanceApprovalNumber = "12345";&#xD;
autoObject.financialDetails.amountAdvanced =1.0;&#xD;
autoObject.financialDetails.paymentAccount = "paymentAccount";&#xD;
autoObject.financialDetails.tradeFOReferenceNumber = "123";&#xD;
autoObject.financialDetails.documentAmount =1.0;&#xD;
autoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();&#xD;
autoObject.financialDetails.sourceOfFunds.id =1;&#xD;
autoObject.financialDetails.sourceOfFunds.code = "001";&#xD;
autoObject.financialDetails.sourceOfFunds.arabicdescription = "موارد العميل الذاتية";&#xD;
autoObject.financialDetails.sourceOfFunds.englishdescription = "english";&#xD;
autoObject.financialDetails.chargesAccount = "charges";&#xD;
autoObject.financialDetails.CashAmtWithNoCurrency =1.0;&#xD;
autoObject.financialDetails.amtDeferredAvalized =1.0;&#xD;
autoObject.financialDetails.amtPaidbyOtherBanks =1.0;&#xD;
autoObject.financialDetails.cashAmtInDocCurrency =1.0;&#xD;
autoObject.IDCRequestType = new tw.object.DBLookup();&#xD;
autoObject.IDCRequestType.id =1;&#xD;
autoObject.IDCRequestType.code = "code";&#xD;
autoObject.IDCRequestType.arabicdescription = "arabic";&#xD;
autoObject.IDCRequestType.englishdescription = "english";&#xD;
autoObject.isIDCWithdrawn = false;&#xD;
autoObject.IDCRequestStage = "Stage";&#xD;
autoObject.FCContractNumber = "FC";&#xD;
autoObject.billOfLading = new tw.object.listOf.Invoice();&#xD;
autoObject.billOfLading[0] = new tw.object.Invoice();&#xD;
autoObject.billOfLading[0].date = new TWDate();&#xD;
autoObject.billOfLading[0].number = "123";&#xD;
autoObject.billOfLading[1] = new tw.object.Invoice();&#xD;
autoObject.billOfLading[1].date = new TWDate();&#xD;
autoObject.billOfLading[1].number = "123";&#xD;
autoObject.importPurpose = new tw.object.DBLookup();&#xD;
autoObject.importPurpose.id =1;&#xD;
autoObject.importPurpose.code = "001";&#xD;
autoObject.importPurpose.arabicdescription = "استيراد سلعى";&#xD;
autoObject.importPurpose.englishdescription = "Commodity Import";&#xD;
autoObject.IDCRequestNature = new tw.object.DBLookup();&#xD;
autoObject.IDCRequestNature.id =1;&#xD;
autoObject.IDCRequestNature.code = "1";&#xD;
autoObject.IDCRequestNature.arabicdescription = "اصدر جديد";&#xD;
autoObject.IDCRequestNature.englishdescription = "New Request";&#xD;
autoObject.customerInformation = new tw.object.CustomerInformation();&#xD;
autoObject.customerInformation.CIFNumber = "02366014";&#xD;
autoObject.customerInformation.importCardNumber = "import";&#xD;
autoObject.customerInformation.commercialRegistrationNumber = "commercial";&#xD;
autoObject.customerInformation.customerName = "customerName";&#xD;
autoObject.customerInformation.isCustomeSanctionedbyCBE = "";&#xD;
autoObject.customerInformation.CBENumber = "cbe";&#xD;
autoObject.customerInformation.customerSector = "rSector";&#xD;
autoObject.customerInformation.facilityType = new tw.object.DBLookup();&#xD;
autoObject.customerInformation.facilityType.id = 1;&#xD;
autoObject.customerInformation.facilityType.arabicdescription= "لا يتمتع بتسهيلات";&#xD;
autoObject.customerInformation.commercialRegistrationOffice = "Office";&#xD;
autoObject.customerInformation.taxCardNumber = "taxCardNumber";&#xD;
autoObject.customerInformation.customerType = "cType";&#xD;
autoObject.customerInformation.addressLine1 = "1";&#xD;
autoObject.customerInformation.addressLine2 = "2";&#xD;
autoObject.invoices = new tw.object.listOf.Invoice();&#xD;
autoObject.invoices[0] = new tw.object.Invoice();&#xD;
autoObject.invoices[0].date = new TWDate();&#xD;
autoObject.invoices[0].number = "number";&#xD;
autoObject.invoices[1] = new tw.object.Invoice();&#xD;
autoObject.invoices[1].date = new TWDate();&#xD;
autoObject.invoices[1].number = "number2";&#xD;
autoObject.productCategory = new tw.object.DBLookup();&#xD;
autoObject.productCategory.id =1;&#xD;
autoObject.productCategory.code = "001";&#xD;
autoObject.productCategory.arabicdescription = "arabic";&#xD;
autoObject.productCategory.englishdescription = "Automotive industry";&#xD;
autoObject.documentsSource = new tw.object.DBLookup();&#xD;
autoObject.documentsSource.id =1;&#xD;
autoObject.documentsSource.code = "001";&#xD;
autoObject.documentsSource.arabicdescription = "arabic";&#xD;
autoObject.documentsSource.englishdescription = "Automotive industry";&#xD;
autoObject.ParentIDCRequestNumber = "Paren";&#xD;
autoObject.paymentTerms = new tw.object.DBLookup();&#xD;
autoObject.paymentTerms.id =1;&#xD;
autoObject.paymentTerms.code = "001";&#xD;
autoObject.paymentTerms.arabicdescription = "اطلاع";&#xD;
autoObject.paymentTerms.englishdescription = "Sight";&#xD;
autoObject.approvals = new tw.object.Approvals();&#xD;
autoObject.approvals.CAD = false;&#xD;
autoObject.approvals.treasury = false;&#xD;
autoObject.approvals.compliance = false;&#xD;
autoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();&#xD;
autoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();&#xD;
autoObject.appLog[0].startTime = new TWDate();&#xD;
autoObject.appLog[0].endTime = new TWDate();&#xD;
autoObject.appLog[0].userName = "userName";&#xD;
autoObject.appLog[0].role = "role";&#xD;
autoObject.appLog[0].step = "step";&#xD;
autoObject.appLog[0].action = "action";&#xD;
autoObject.appLog[0].comment = "comment";&#xD;
autoObject.appLog[0].terminateReason = "terminateReason";&#xD;
autoObject.appLog[0].returnReason = "returnReason";&#xD;
autoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();&#xD;
autoObject.stepLog.startTime = new TWDate();&#xD;
autoObject.stepLog.endTime = new TWDate();&#xD;
autoObject.stepLog.userName = "userName";&#xD;
autoObject.stepLog.role = "role";&#xD;
autoObject.stepLog.step = "step";&#xD;
autoObject.stepLog.action = "action";&#xD;
autoObject.stepLog.comment = "comment";&#xD;
autoObject.stepLog.terminateReason = "terminateReason";&#xD;
autoObject.stepLog.returnReason = "returnReason";&#xD;
autoObject.DBID =7;&#xD;
autoObject.requestDate = new TWDate();&#xD;
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="idcContract" itemSubjectRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1" isCollection="false" id="2055.8dc5d463-89eb-4f51-8d45-5412d38d92ae">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">var autoObject = new tw.object.IDCContract();
autoObject.collateralAmount = 0.0;
autoObject.userReference = "";
autoObject.settlementAccounts = new tw.object.listOf.SettlementAccount();
autoObject.settlementAccounts[0] = new tw.object.SettlementAccount();
autoObject.settlementAccounts[0].debitedAccount = new tw.object.DebitedAccount();
autoObject.settlementAccounts[0].debitedAccount.balanceSign = "";
autoObject.settlementAccounts[0].debitedAccount.GLAccountNumber = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency = new tw.object.DBLookup();
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.id = 0;
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.code = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.arabicdescription = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.englishdescription = "";
autoObject.settlementAccounts[0].debitedAccount.accountBranchCode = "";
autoObject.settlementAccounts[0].debitedAccount.accountBalance = 0.0;
autoObject.settlementAccounts[0].debitedAccount.accountNumber = "";
autoObject.settlementAccounts[0].debitedAccount.accountClass = "";
autoObject.settlementAccounts[0].debitedAccount.ownerAccounts = "";
autoObject.settlementAccounts[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.settlementAccounts[0].debitedAccount.currency.name = "";
autoObject.settlementAccounts[0].debitedAccount.currency.value = "";
autoObject.settlementAccounts[0].debitedAccount.isGLFoundC = false;
autoObject.settlementAccounts[0].debitedAccount.commCIF = "";
autoObject.settlementAccounts[0].debitedAccount.isGLVerifiedC = false;
autoObject.settlementAccounts[0].debitedAccount.isOverDraft = false;
autoObject.settlementAccounts[0].debitedAmount = new tw.object.DebitedAmount();
autoObject.settlementAccounts[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;
autoObject.settlementAccounts[0].debitedAmount.debitPercentage = 0.0;
autoObject.settlementAccounts[0].debitedAmount.standardExchangeRate = 0.0;
autoObject.settlementAccounts[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;
autoObject.settlementAccounts[0].debitedAmount.negotiatedExchangeRate = 0.0;
autoObject.settlementAccounts[0].accountNumberList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.settlementAccounts[0].accountNumberList[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.settlementAccounts[0].accountNumberList[0].name = "";
autoObject.settlementAccounts[0].accountNumberList[0].value = "";
autoObject.settlementAccounts[0].settCIF = "";
autoObject.billAmount = 0.0;
autoObject.billCurrency = new tw.object.DBLookup();
autoObject.billCurrency.id = 0;
autoObject.billCurrency.code = "";
autoObject.billCurrency.arabicdescription = "";
autoObject.billCurrency.englishdescription = "";
autoObject.party = new tw.object.listOf.Parties();
autoObject.party[0] = new tw.object.Parties();
autoObject.party[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.party[0].partyType.name = "";
autoObject.party[0].partyType.value = "";
autoObject.party[0].partyId = "";
autoObject.party[0].name = "";
autoObject.party[0].country = "";
autoObject.party[0].reference = "";
autoObject.party[0].address1 = "";
autoObject.party[0].address2 = "";
autoObject.party[0].address3 = "";
autoObject.party[0].address4 = "";
autoObject.party[0].media = "";
autoObject.party[0].address = "";
autoObject.party[0].phone = "";
autoObject.party[0].fax = "";
autoObject.party[0].email = "";
autoObject.party[0].contactPersonName = "";
autoObject.party[0].mobile = "";
autoObject.party[0].branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.party[0].branch.name = "";
autoObject.party[0].branch.value = "";
autoObject.party[0].language = "";
autoObject.party[0].partyCIF = "";
autoObject.party[0].isNbeCustomer = false;
autoObject.party[0].isRetrived = false;
autoObject.sourceReference = "";
autoObject.isLimitsTrackingRequired = false;
autoObject.liquidationSummary = new tw.object.LiquidationSummary();
autoObject.liquidationSummary.liquidationCurrency = "";
autoObject.liquidationSummary.debitBasisby = "";
autoObject.liquidationSummary.liquidationAmt = 0.0;
autoObject.liquidationSummary.debitValueDate = new TWDate();
autoObject.liquidationSummary.creditValueDate = new TWDate();
autoObject.IDCProduct = new tw.object.DBLookup();
autoObject.IDCProduct.id = 0;
autoObject.IDCProduct.code = "";
autoObject.IDCProduct.arabicdescription = "";
autoObject.IDCProduct.englishdescription = "";
autoObject.interestToDate = new TWDate();
autoObject.transactionMaturityDate = new TWDate();
autoObject.commissionsAndCharges = new tw.object.listOf.CommissionsAndChargesDetails();
autoObject.commissionsAndCharges[0] = new tw.object.CommissionsAndChargesDetails();
autoObject.commissionsAndCharges[0].component = "";
autoObject.commissionsAndCharges[0].debitedAccount = new tw.object.DebitedAccount();
autoObject.commissionsAndCharges[0].debitedAccount.balanceSign = "";
autoObject.commissionsAndCharges[0].debitedAccount.GLAccountNumber = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency = new tw.object.DBLookup();
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.id = 0;
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.code = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.arabicdescription = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.englishdescription = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountBranchCode = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountBalance = 0.0;
autoObject.commissionsAndCharges[0].debitedAccount.accountNumber = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountClass = "";
autoObject.commissionsAndCharges[0].debitedAccount.ownerAccounts = "";
autoObject.commissionsAndCharges[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.commissionsAndCharges[0].debitedAccount.currency.name = "";
autoObject.commissionsAndCharges[0].debitedAccount.currency.value = "";
autoObject.commissionsAndCharges[0].debitedAccount.isGLFoundC = false;
autoObject.commissionsAndCharges[0].debitedAccount.commCIF = "";
autoObject.commissionsAndCharges[0].debitedAccount.isGLVerifiedC = false;
autoObject.commissionsAndCharges[0].debitedAccount.isOverDraft = false;
autoObject.commissionsAndCharges[0].chargeAmount = 0.0;
autoObject.commissionsAndCharges[0].waiver = false;
autoObject.commissionsAndCharges[0].debitedAmount = new tw.object.DebitedAmount();
autoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.debitPercentage = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.standardExchangeRate = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.negotiatedExchangeRate = 0.0;
autoObject.commissionsAndCharges[0].defaultCurrency = new tw.object.DBLookup();
autoObject.commissionsAndCharges[0].defaultCurrency.id = 0;
autoObject.commissionsAndCharges[0].defaultCurrency.code = "";
autoObject.commissionsAndCharges[0].defaultCurrency.arabicdescription = "";
autoObject.commissionsAndCharges[0].defaultCurrency.englishdescription = "";
autoObject.commissionsAndCharges[0].defaultAmount = 0.0;
autoObject.commissionsAndCharges[0].commAccountList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.commissionsAndCharges[0].commAccountList[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.commissionsAndCharges[0].commAccountList[0].name = "";
autoObject.commissionsAndCharges[0].commAccountList[0].value = "";
autoObject.transactionBaseDate = new TWDate();
autoObject.tradeFinanceApprovalNumber = "";
autoObject.FCContractNumber = "";
autoObject.collateralCurrency = new tw.object.DBLookup();
autoObject.collateralCurrency.id = 0;
autoObject.collateralCurrency.code = "";
autoObject.collateralCurrency.arabicdescription = "";
autoObject.collateralCurrency.englishdescription = "";
autoObject.interestRate = 0.0;
autoObject.transactionTransitDays = 0;
autoObject.swiftMessageData = new tw.object.SwiftMessageData();
autoObject.swiftMessageData.intermediary = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.intermediary.line1 = "";
autoObject.swiftMessageData.intermediary.line2 = "";
autoObject.swiftMessageData.intermediary.line3 = "";
autoObject.swiftMessageData.intermediary.line4 = "";
autoObject.swiftMessageData.intermediary.line5 = "";
autoObject.swiftMessageData.intermediary.line6 = "";
autoObject.swiftMessageData.detailsOfCharge = "";
autoObject.swiftMessageData.accountWithInstitution = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.accountWithInstitution.line1 = "";
autoObject.swiftMessageData.accountWithInstitution.line2 = "";
autoObject.swiftMessageData.accountWithInstitution.line3 = "";
autoObject.swiftMessageData.accountWithInstitution.line4 = "";
autoObject.swiftMessageData.accountWithInstitution.line5 = "";
autoObject.swiftMessageData.accountWithInstitution.line6 = "";
autoObject.swiftMessageData.receiver = "";
autoObject.swiftMessageData.swiftMessageOption = "";
autoObject.swiftMessageData.coverRequired = "";
autoObject.swiftMessageData.transferType = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line1 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line2 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line3 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line4 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line5 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line6 = "";
autoObject.swiftMessageData.receiverCorrespondent = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.receiverCorrespondent.line1 = "";
autoObject.swiftMessageData.receiverCorrespondent.line2 = "";
autoObject.swiftMessageData.receiverCorrespondent.line3 = "";
autoObject.swiftMessageData.receiverCorrespondent.line4 = "";
autoObject.swiftMessageData.receiverCorrespondent.line5 = "";
autoObject.swiftMessageData.receiverCorrespondent.line6 = "";
autoObject.swiftMessageData.detailsOfPayment = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.detailsOfPayment.line1 = "";
autoObject.swiftMessageData.detailsOfPayment.line2 = "";
autoObject.swiftMessageData.detailsOfPayment.line3 = "";
autoObject.swiftMessageData.detailsOfPayment.line4 = "";
autoObject.swiftMessageData.detailsOfPayment.line5 = "";
autoObject.swiftMessageData.detailsOfPayment.line6 = "";
autoObject.swiftMessageData.orderingInstitution = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.orderingInstitution.line1 = "";
autoObject.swiftMessageData.orderingInstitution.line2 = "";
autoObject.swiftMessageData.orderingInstitution.line3 = "";
autoObject.swiftMessageData.orderingInstitution.line4 = "";
autoObject.swiftMessageData.orderingInstitution.line5 = "";
autoObject.swiftMessageData.orderingInstitution.line6 = "";
autoObject.swiftMessageData.beneficiaryInstitution = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.beneficiaryInstitution.line1 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line2 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line3 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line4 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line5 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line6 = "";
autoObject.swiftMessageData.receiverOfCover = "";
autoObject.swiftMessageData.ultimateBeneficiary = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.ultimateBeneficiary.line1 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line2 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line3 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line4 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line5 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line6 = "";
autoObject.swiftMessageData.orderingCustomer = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.orderingCustomer.line1 = "";
autoObject.swiftMessageData.orderingCustomer.line2 = "";
autoObject.swiftMessageData.orderingCustomer.line3 = "";
autoObject.swiftMessageData.orderingCustomer.line4 = "";
autoObject.swiftMessageData.orderingCustomer.line5 = "";
autoObject.swiftMessageData.orderingCustomer.line6 = "";
autoObject.swiftMessageData.senderToReciever = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.senderToReciever.line1 = "";
autoObject.swiftMessageData.senderToReciever.line2 = "";
autoObject.swiftMessageData.senderToReciever.line3 = "";
autoObject.swiftMessageData.senderToReciever.line4 = "";
autoObject.swiftMessageData.senderToReciever.line5 = "";
autoObject.swiftMessageData.senderToReciever.line6 = "";
autoObject.swiftMessageData.RTGS = "";
autoObject.swiftMessageData.RTGSNetworkType = "";
autoObject.advices = new tw.object.listOf.ContractAdvice();
autoObject.advices[0] = new tw.object.ContractAdvice();
autoObject.advices[0].adviceCode = "";
autoObject.advices[0].suppressed = false;
autoObject.advices[0].advicelines = new tw.object.SwiftMessagePart();
autoObject.advices[0].advicelines.line1 = "";
autoObject.advices[0].advicelines.line2 = "";
autoObject.advices[0].advicelines.line3 = "";
autoObject.advices[0].advicelines.line4 = "";
autoObject.advices[0].advicelines.line5 = "";
autoObject.advices[0].advicelines.line6 = "";
autoObject.cashCollateralAccounts = new tw.object.listOf.CashCollateralAccount();
autoObject.cashCollateralAccounts[0] = new tw.object.CashCollateralAccount();
autoObject.cashCollateralAccounts[0].accountCurrency = "";
autoObject.cashCollateralAccounts[0].accountClass = "";
autoObject.cashCollateralAccounts[0].debitedAmtinCollateralCurrency = 0.0;
autoObject.cashCollateralAccounts[0].GLAccountNumber = "";
autoObject.cashCollateralAccounts[0].accountBranchCode = "";
autoObject.cashCollateralAccounts[0].accountNumber = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.cashCollateralAccounts[0].accountNumber.name = "";
autoObject.cashCollateralAccounts[0].accountNumber.value = "";
autoObject.cashCollateralAccounts[0].isGLFound = false;
autoObject.cashCollateralAccounts[0].isGLVerified = false;
autoObject.IDCRequestStage = "";
autoObject.transactionValueDate = new TWDate();
autoObject.transactionTenorDays = 0;
autoObject.contractLimitsTracking = new tw.object.listOf.ContractLimitTracking();
autoObject.contractLimitsTracking[0] = new tw.object.ContractLimitTracking();
autoObject.contractLimitsTracking[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.contractLimitsTracking[0].partyType.name = "";
autoObject.contractLimitsTracking[0].partyType.value = "";
autoObject.contractLimitsTracking[0].type = "";
autoObject.contractLimitsTracking[0].jointVentureParent = "";
autoObject.contractLimitsTracking[0].customerNo = "";
autoObject.contractLimitsTracking[0].linkageRefNum = "";
autoObject.contractLimitsTracking[0].amountTag = "";
autoObject.contractLimitsTracking[0].contributionPercentage = 0.0;
autoObject.contractLimitsTracking[0].isCIFfound = false;
autoObject.interestFromDate = new TWDate();
autoObject.interestAmount = 0.0;
autoObject.haveInterest = false;
autoObject.accountNumberList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.accountNumberList[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.accountNumberList[0].name = "";
autoObject.accountNumberList[0].value = "";
//autoObject.facilities = new tw.object.listOf.toolkit.NBEC.creditFacilityInformation();
//autoObject.facilities[0] = new tw.object.toolkit.NBEC.creditFacilityInformation();
//autoObject.facilities[0].facilityCode = "";
//autoObject.facilities[0].overallLimit = 0.0;
//autoObject.facilities[0].limitAmount = 0.0;
//autoObject.facilities[0].effectiveLimitAmount = 0.0;
//autoObject.facilities[0].availableAmount = 0.0;
//autoObject.facilities[0].expiryDate = new TWDate();
//autoObject.facilities[0].availableFlag = false;
//autoObject.facilities[0].authorizedFlag = false;
//autoObject.facilities[0].Utilization = 0.0;
//autoObject.facilities[0].returnCode = "";
//autoObject.facilities[0].facilityLines = new tw.object.listOf.toolkit.NBEC.FacilityLinesDetails();
//autoObject.facilities[0].facilityLines[0] = new tw.object.toolkit.NBEC.FacilityLinesDetails();
//autoObject.facilities[0].facilityLines[0].lineCode = "";
//autoObject.facilities[0].facilityLines[0].lineAmount = 0.0;
//autoObject.facilities[0].facilityLines[0].availableAmount = 0.0;
//autoObject.facilities[0].facilityLines[0].effectiveLineAmount = 0.0;
//autoObject.facilities[0].facilityLines[0].expiryDate = new TWDate();
//autoObject.facilities[0].facilityLines[0].facilityBranch = new tw.object.toolkit.TWSYS.NameValuePair();
//autoObject.facilities[0].facilityLines[0].facilityBranch.name = "";
//autoObject.facilities[0].facilityLines[0].facilityBranch.value = "";
//autoObject.facilities[0].facilityLines[0].availableFlag = false;
//autoObject.facilities[0].facilityLines[0].authorizedFlag = false;
//autoObject.facilities[0].facilityLines[0].facilityPercentageToBook = 0;
//autoObject.facilities[0].facilityLines[0].internalRemarks = "";
//autoObject.facilities[0].facilityLines[0].purpose = "";
//autoObject.facilities[0].facilityLines[0].LCCommissionPercentage = 0;
//autoObject.facilities[0].facilityLines[0].LCDef = "";
//autoObject.facilities[0].facilityLines[0].LCCashCover = "";
//autoObject.facilities[0].facilityLines[0].IDCCommission = "";
//autoObject.facilities[0].facilityLines[0].IDCAvalCommPercentage = 0;
//autoObject.facilities[0].facilityLines[0].IDCCashCoverPercentage = 0;
//autoObject.facilities[0].facilityLines[0].debitAccountNumber = "";
//autoObject.facilities[0].facilityLines[0].lineCurrency = "";
//autoObject.facilities[0].facilityLines[0].lineSerialNumber = "";
//autoObject.facilities[0].facilityLines[0].returnCode = "";
//autoObject.facilities[0].facilityLines[0].LGCommission = 0;
//autoObject.facilities[0].facilityLines[0].LGCashCover_BidBond = 0;
//autoObject.facilities[0].facilityLines[0].LGCashCover_Performance = 0;
//autoObject.facilities[0].facilityLines[0].LGCashCover_AdvancedPayment = 0;
//autoObject.facilities[0].facilityLines[0].restrictedCurrencies = new tw.object.listOf.toolkit.NBEC.restrictedItem();
//autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0] = new tw.object.toolkit.NBEC.restrictedItem();
//autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].name = "";
//autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].code = "";
//autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].source = "";
//autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].status = "";
//autoObject.facilities[0].facilityLines[0].restrictedBranches = new tw.object.listOf.toolkit.NBEC.restrictedItem();
//autoObject.facilities[0].facilityLines[0].restrictedBranches[0] = new tw.object.toolkit.NBEC.restrictedItem();
//autoObject.facilities[0].facilityLines[0].restrictedBranches[0].name = "";
//autoObject.facilities[0].facilityLines[0].restrictedBranches[0].code = "";
//autoObject.facilities[0].facilityLines[0].restrictedBranches[0].source = "";
//autoObject.facilities[0].facilityLines[0].restrictedBranches[0].status = "";
//autoObject.facilities[0].facilityLines[0].restrictedProducts = new tw.object.listOf.toolkit.NBEC.restrictedItem();
//autoObject.facilities[0].facilityLines[0].restrictedProducts[0] = new tw.object.toolkit.NBEC.restrictedItem();
//autoObject.facilities[0].facilityLines[0].restrictedProducts[0].name = "";
//autoObject.facilities[0].facilityLines[0].restrictedProducts[0].code = "";
//autoObject.facilities[0].facilityLines[0].restrictedProducts[0].source = "";
//autoObject.facilities[0].facilityLines[0].restrictedProducts[0].status = "";
//autoObject.facilities[0].facilityLines[0].restrictedCustomers = new tw.object.listOf.toolkit.NBEC.restrictedItem();
//autoObject.facilities[0].facilityLines[0].restrictedCustomers[0] = new tw.object.toolkit.NBEC.restrictedItem();
//autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].name = "";
//autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].code = "";
//autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].source = "";
//autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].status = "";
//autoObject.facilities[0].facilityLines[0].exposureRestrictions = new tw.object.listOf.toolkit.NBEC.restrictedItem();
//autoObject.facilities[0].facilityLines[0].exposureRestrictions[0] = new tw.object.toolkit.NBEC.restrictedItem();
//autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].name = "";
//autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].code = "";
//autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].source = "";
//autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].status = "";
//autoObject.facilities[0].facilityLines[0].CIF = "";
//autoObject.facilities[0].facilityLines[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();
//autoObject.facilities[0].facilityLines[0].partyType.name = "";
//autoObject.facilities[0].facilityLines[0].partyType.value = "";
//autoObject.facilities[0].facilityLines[0].facilityID = "";
//autoObject.facilities[0].status = "";
//autoObject.facilities[0].facilityCurrency = new tw.object.toolkit.TWSYS.NameValuePair();
//autoObject.facilities[0].facilityCurrency.name = "";
//autoObject.facilities[0].facilityCurrency.value = "";
//autoObject.facilities[0].facilityID = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.6d23c29f-dcc7-4d77-81e8-3c00f3db633d" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.1cb68f60-25be-43eb-8983-faa1eebbd945</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.8dc5d463-89eb-4f51-8d45-5412d38d92ae</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.6d23c29f-dcc7-4d77-81e8-3c00f3db633d</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="83537b6a-332d-4186-8d20-e9fa33ad269b">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="14e0e4d4-93f0-4369-8cf6-f45786228172" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>1c48126d-f842-4cc4-88b7-e379c0afbac0</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>360f095c-c838-407d-8326-f25a1a115a02</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>485c1cfa-b21c-4f9b-8c38-408c70d91afd</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a8fb1bfb-58c7-4ce7-8f29-137efb1dc5d9</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e66fcbcf-ea21-45ab-86e1-f555f1a2bb7a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8c791fc3-3fec-4c45-8b19-58d174daab20</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6322d481-5d75-47ff-8b5f-56cada0079b8</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>08188f42-8534-47c7-83a5-bf60b20530c5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>fe41e509-5f0c-459e-847c-d70a899d3be5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>21552222-2fdb-4ae9-875c-b9a82761b60b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>68b88d8d-4d56-42d0-8463-31a3e6d422bf</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>babb4479-8247-49a2-896c-2883cf18c868</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6943fb57-7d25-492b-87e9-e6076031a215</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>220efb6a-ffba-4dd8-86f0-9e556b4573c0</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e89eb804-d511-49e2-8947-ba49fc792260</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="1c48126d-f842-4cc4-88b7-e379c0afbac0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.466e771e-c0a3-4f39-8638-7921cf440833</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="360f095c-c838-407d-8326-f25a1a115a02">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1190" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:e5873b94e72cc4db:-66d3a9fb:189cfb43e32:-7712</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>01020eac-7ccd-4b18-81f0-2e98c68bc117</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="1c48126d-f842-4cc4-88b7-e379c0afbac0" targetRef="08188f42-8534-47c7-83a5-bf60b20530c5" name="To UPDATE IDC Request Q" id="2027.466e771e-c0a3-4f39-8638-7921cf440833">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7" name="UPDATE IDC Request" id="485c1cfa-b21c-4f9b-8c38-408c70d91afd">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="260" y="56" width="95" height="70" color="#FFC875" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>3f70aa03-14cd-4098-811e-ef15c12abae9</ns16:incoming>
                        
                        
                        <ns16:outgoing>c2c0641c-c534-46ee-821f-928b01692f0c</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a634f531-c979-476c-91db-a17bf5e55c90</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0">tw.local.sqlStatements</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="485c1cfa-b21c-4f9b-8c38-408c70d91afd" targetRef="8c791fc3-3fec-4c45-8b19-58d174daab20" name="To DELETE Q" id="c2c0641c-c534-46ee-821f-928b01692f0c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7" name="DELETE Invoices &amp; Bills" id="a8fb1bfb-58c7-4ce7-8f29-137efb1dc5d9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="576" y="56" width="95" height="70" color="#FFC875" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>1a3ded6b-baee-47a8-856f-acab7c1c6667</ns16:incoming>
                        
                        
                        <ns16:outgoing>26923925-0f09-4da5-8564-ee592a52a840</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a634f531-c979-476c-91db-a17bf5e55c90</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0">tw.local.sqlStatements</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="a8fb1bfb-58c7-4ce7-8f29-137efb1dc5d9" targetRef="6322d481-5d75-47ff-8b5f-56cada0079b8" name="To INSERT Q" id="26923925-0f09-4da5-8564-ee592a52a840">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7" name="INSERT Invoices &amp; Bills" id="e66fcbcf-ea21-45ab-86e1-f555f1a2bb7a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="929" y="56" width="95" height="70" color="#FFC875" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>02052b06-180c-4b80-86e4-2c397f656ac3</ns16:incoming>
                        
                        
                        <ns16:outgoing>01020eac-7ccd-4b18-81f0-2e98c68bc117</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a634f531-c979-476c-91db-a17bf5e55c90</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0">tw.local.sqlStatements</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="e66fcbcf-ea21-45ab-86e1-f555f1a2bb7a" targetRef="360f095c-c838-407d-8326-f25a1a115a02" name="To End" id="01020eac-7ccd-4b18-81f0-2e98c68bc117">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="DELETE Q" id="8c791fc3-3fec-4c45-8b19-58d174daab20">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="427" y="55" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>c2c0641c-c534-46ee-821f-928b01692f0c</ns16:incoming>
                        
                        
                        <ns16:outgoing>1a3ded6b-baee-47a8-856f-acab7c1c6667</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
&#xD;
//---------------------------------Invoices------------------------------------------------------&#xD;
tw.local.sqlStatements[0] = {};&#xD;
tw.local.sqlStatements[0].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[0].parameters[0] = {};&#xD;
tw.local.sqlStatements[0].parameters[0].value = tw.local.IDCRequest.DBID;&#xD;
tw.local.sqlStatements[0].sql = "DELETE FROM BPM.IDC_REQUEST_INVOICES WHERE IDC_REQUEST_ID = ?"&#xD;
&#xD;
//---------------------------------Bills-----------------------------------------------------------&#xD;
tw.local.sqlStatements[1] = {};&#xD;
tw.local.sqlStatements[1].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[1].parameters[0] = {};&#xD;
tw.local.sqlStatements[1].parameters[0].value = tw.local.IDCRequest.DBID;&#xD;
tw.local.sqlStatements[1].sql = "DELETE FROM BPM.IDC_REQUEST_BILLS WHERE IDC_REQUEST_ID = ?"&#xD;
//--------------------------------------------------------------------------------------------------&#xD;
tw.local.sqlStatements[2] = {};&#xD;
tw.local.sqlStatements[2].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlStatements[2].parameters[0] = {};&#xD;
tw.local.sqlStatements[2].parameters[0].value = tw.local.IDCRequest.DBID;&#xD;
tw.local.sqlStatements[2].sql = "DELETE FROM BPM.IDC_FACILITY_DETAILS WHERE IDC_REQUEST_ID = ?"</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="8c791fc3-3fec-4c45-8b19-58d174daab20" targetRef="a8fb1bfb-58c7-4ce7-8f29-137efb1dc5d9" name="To DELETE Invoices &amp; Bills" id="1a3ded6b-baee-47a8-856f-acab7c1c6667">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="INSERT Q" id="6322d481-5d75-47ff-8b5f-56cada0079b8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="758" y="55" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>26923925-0f09-4da5-8564-ee592a52a840</ns16:incoming>
                        
                        
                        <ns16:outgoing>02052b06-180c-4b80-86e4-2c397f656ac3</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var j = 0&#xD;
function paramInit (type,value) {&#xD;
	tw.local.sqlStatements[j].parameters[tw.local.i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[tw.local.i].type = type;&#xD;
	tw.local.sqlStatements[j].parameters[tw.local.i].value = value;&#xD;
	tw.local.i= tw.local.i+1;&#xD;
}&#xD;
//-----------------------------invoice--------------------------------------------------&#xD;
&#xD;
for (j=0; j&lt;tw.local.IDCRequest.invoices.listLength; j++) {&#xD;
	tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
	tw.local.sqlStatements[j].sql = "INSERT INTO BPM.IDC_REQUEST_INVOICES (IDC_REQUEST_ID, INVOICE_NUMBER, INVOICE_DATE) VALUES (?,?,?) ;";&#xD;
	&#xD;
	tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
	tw.local.i = 0;&#xD;
	&#xD;
	paramInit("INTEGER",tw.local.IDCRequest.DBID);&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.invoices[j].number);&#xD;
	paramInit("DATE",tw.local.IDCRequest.invoices[j].date);&#xD;
}&#xD;
&#xD;
////-----------------------------bill-----------------------------------------------------&#xD;
var n = 0;&#xD;
// j++;&#xD;
for (; j&lt;(tw.local.IDCRequest.billOfLading.listLength + tw.local.IDCRequest.invoices.listLength); j++) {&#xD;
	tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
	tw.local.sqlStatements[j].sql = "INSERT INTO BPM.IDC_REQUEST_BILLS (IDC_REQUEST_ID, BILL_OF_LADING_REF, BILL_OF_LADING_DATE) VALUES (?,?,?) ;";&#xD;
	&#xD;
	tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
	tw.local.i = 0;&#xD;
	&#xD;
	paramInit("INTEGER",tw.local.IDCRequest.DBID);&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.billOfLading[n].number);&#xD;
	paramInit("DATE",tw.local.IDCRequest.billOfLading[n].date);&#xD;
	n+=1;&#xD;
}&#xD;
&#xD;
////-----------------------------UPDATE customer---------------------------------------------------&#xD;
// j+=1;&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].sql = "UPDATE BPM.IDC_CUSTOMER_INFORMATION SET CIF = ?, CUSTOMER_NAME = ?, CUSTOMER_SECTOR = ?, CUSTOMER_TYPE = ?, CUSTOMER_NUMBER_AT_CBE = ?, FACILITY_TYPE_ID = ?, COMMERCIAL_REGISTRATION_NUMBER = ?, COMMERCIAL_REGISTRATION_OFFICE = ?, TAX_CARD_NUMBER = ?, IMPORT_CARD_NUMBER = ? WHERE IDC_REQUEST_ID = ?;";&#xD;
&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.i = 0;&#xD;
&#xD;
paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.CIFNumber);&#xD;
paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.customerName);&#xD;
paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.customerSector);//can be update later &lt;----------------------------------------------------&#xD;
paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.customerType);&#xD;
paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.CBENumber);&#xD;
paramInit("INTEGER",tw.local.IDCRequest.customerInformation.facilityType.id);&#xD;
paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.commercialRegistrationNumber);&#xD;
paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.commercialRegistrationOffice);&#xD;
paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.taxCardNumber);&#xD;
paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.importCardNumber);&#xD;
&#xD;
paramInit("INTEGER",tw.local.IDCRequest.DBID);&#xD;
//-----------------------------------------facility-----------------------------------------------------------&#xD;
//if (tw.local.idcContract.facilities != null &amp;&amp; tw.local.idcContract.facilities != undefined) {&#xD;
	if (tw.local.idcContract.facilities.listLength &gt; 0) {&#xD;
		j++;&#xD;
		for (var m_f_len=0; m_f_len &lt; tw.local.idcContract.facilities.listLength; m_f_len++) {&#xD;
			for (var s_f_len=0; s_f_len &lt; tw.local.idcContract.facilities[m_f_len].facilityLines.listLength; s_f_len++) {&#xD;
				tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
				tw.local.sqlStatements[j].sql = "INSERT INTO BPM.IDC_FACILITY_DETAILS (IDC_REQUEST_ID, FACILITY_ID, CURRENCY, LINE_SERIAL, LINE_CODE, FACILITY_PERCENTAGE_TO_BOOK) VALUES (?,?,?,?,?,?) ;";&#xD;
				&#xD;
				tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
				tw.local.i = 0;&#xD;
				paramInit("INTEGER",tw.local.IDCRequest.DBID);&#xD;
				paramInit("VARCHAR",tw.local.idcContract.facilities[m_f_len].facilityID);&#xD;
				paramInit("VARCHAR",tw.local.idcContract.facilities[m_f_len].facilityCurrency.value);&#xD;
				paramInit("VARCHAR",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].lineSerialNumber);&#xD;
				paramInit("VARCHAR",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].lineCode);&#xD;
				paramInit("DECIMAL",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].facilityPercentageToBook);&#xD;
				j++;&#xD;
			}&#xD;
		}&#xD;
	}&#xD;
	&#xD;
&#xD;
//}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="6322d481-5d75-47ff-8b5f-56cada0079b8" targetRef="e66fcbcf-ea21-45ab-86e1-f555f1a2bb7a" name="To INSERT Invoices &amp; Bills" id="02052b06-180c-4b80-86e4-2c397f656ac3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="UPDATE IDC Request Q" id="08188f42-8534-47c7-83a5-bf60b20530c5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="118" y="55" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.466e771e-c0a3-4f39-8638-7921cf440833</ns16:incoming>
                        
                        
                        <ns16:outgoing>3f70aa03-14cd-4098-811e-ef15c12abae9</ns16:outgoing>
                        
                        
                        <ns16:script>&#xD;
tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
tw.local.sqlStatements[0] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[0].sql = "UPDATE BPM.IDC_REQUEST_DETAILS SET REQUEST_NUMBER = ?, PARENT_REQUEST_NUMBER = ?, BPM_INSTANCE_NUMBER = ?, REQUEST_INITIATOR = ?, REQUEST_BRANCH_HUB = ?, REQUEST_NATURE_ID = ?, REQUEST_TYPE_ID = ?, REQUEST_DATE = ?, REQUEST_STATE = ?, REQUEST_STAGE = ?, REQUEST_STATUS = ?, REQUEST_SUB_STATUS = ?, FLEX_CUBE_CONTRACT_NUMBER = ?, IS_WITHDRAWEN = ?, IMPORT_PURPOSE_ID = ?, PAYMENT_TERMS_ID = ?, DOCUMENTS_SOURCE_ID = ?, PRODUCT_CATEGORY_ID = ?, COMMODITY_DESCRIPTION = ?, ADVANCE_PAYMENT_OUTSTANDING_AMOUNT = ?, DOCUMENT_AMOUNT = ?, CURRENCY = ?, CHARGES_ACCOUNT = ?, SOURCE_OF_FOREIGN_CURRENCY_ID = ?, SOURCE_OF_FUNDS_ID = ?, PAYMENT_ACCOUNT = ?, CASH_AMOUNT_DOCUMENT_CURRENCY = ?, CASH_AMOUNT_NO_CURRENCY = ?, FACILITY_AMOUNT_DOCUMENT_CURRENCY = ?, FACILITY_AMOUNT_NO_CURRENCY = ?, DISCOUNT = ?, AMOUNT_ADVANCED = ?, AMOUNT_PAID_BY_OTHER_BANKS = ?, AMOUNT_SIGHT = ?, AMOUNT_DEFERRED_NO_AVALIZATION = ?, AMOUNT_DEFERRED_AVALIZATION = ?, AMOUNT_PAYABLE_BY_NBE = ?, FIRST_INSTALLEMENT_MATURITY_DATE = ?, NO_OF_DAYS_TILL_MATURITY = ?, BENEFICIARY_NAME = ?, BENEFICIARY_BANK = ?, BENEFICIARY_IBAN = ?, BENEFICIARY_COUNTRY_CODE = ?, CORRESPONDENT_REF_NUM = ?, TRADE_FO_REFERENCE_NUMBER = ?, TRADE_FU_APPROVAL_NO = ?, EXECUTION_HUB_CODE = ?, EXECUTION_HUB_NAME = ?, SHIPPING_DATE = ?, INCOTERMS = ?, SHIPMENT_METHOD_ID = ?, DESTINATION_PORT = ?, CBE_COMMODITY_CLASSIFICATION_ID = ?, HS_CODE = ?, HS_DESCRIPTION = ?, ACID = ?, COUNTRY_OF_ORIGIN_CODE = ? WHERE ID = ?";&#xD;
&#xD;
tw.local.sqlStatements[0].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.i = 0;&#xD;
function paramInit (value) {&#xD;
	tw.local.sqlStatements[0].parameters[tw.local.i] = new tw.object.SQLParameter();&#xD;
//	tw.local.sqlStatements[0].parameters[tw.local.i].type = type;&#xD;
	tw.local.sqlStatements[0].parameters[tw.local.i].value = value;&#xD;
//	tw.local.sqlStatements[0].parameters[tw.local.i].mode = "IN";&#xD;
	tw.local.i= tw.local.i+1;&#xD;
//	return tw.local.parameter;&#xD;
}&#xD;
paramInit (tw.local.IDCRequest.appInfo.instanceID);&#xD;
paramInit (tw.local.IDCRequest.ParentIDCRequestNumber);&#xD;
paramInit (tw.local.IDCRequest.appInfo.appID);&#xD;
paramInit (tw.local.IDCRequest.appInfo.initiator);&#xD;
paramInit (tw.local.IDCRequest.appInfo.branch.name);&#xD;
paramInit (tw.local.IDCRequest.IDCRequestNature.id);&#xD;
paramInit (tw.local.IDCRequest.IDCRequestType.id);&#xD;
paramInit (tw.local.IDCRequest.requestDate);&#xD;
paramInit (tw.local.IDCRequest.IDCRequestState);&#xD;
paramInit (tw.local.IDCRequest.IDCRequestStage);&#xD;
paramInit (tw.local.IDCRequest.appInfo.status);&#xD;
paramInit (tw.local.IDCRequest.appInfo.subStatus);&#xD;
paramInit (tw.local.IDCRequest.FCContractNumber);&#xD;
if (tw.local.IDCRequest.isIDCWithdrawn == true) {&#xD;
	paramInit (1);&#xD;
	&#xD;
}else{&#xD;
	paramInit (0);&#xD;
	&#xD;
}&#xD;
paramInit (tw.local.IDCRequest.importPurpose.id);&#xD;
paramInit (tw.local.IDCRequest.paymentTerms.id);&#xD;
paramInit (tw.local.IDCRequest.documentsSource.id);&#xD;
paramInit (tw.local.IDCRequest.productCategory.id);&#xD;
paramInit (tw.local.IDCRequest.commodityDescription);&#xD;
if (tw.local.IDCRequest.IDCRequestType.englishdescription == "Advance Payment") {&#xD;
	paramInit (tw.local.IDCRequest.financialDetails.amtPayableByNBE);&#xD;
	&#xD;
}else{&#xD;
	paramInit (0.0);&#xD;
	&#xD;
}&#xD;
paramInit (tw.local.IDCRequest.financialDetails.documentAmount);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.documentCurrency.code);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.chargesAccount);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.sourceOfForeignCurrency.id);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.sourceOfFunds.id);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.paymentAccount);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.cashAmtInDocCurrency);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.CashAmtWithNoCurrency);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.facilityAmtInDocCurrency);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.facilityAmtWithNoCurrency);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.discountAmt);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.amountAdvanced);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.amtPaidbyOtherBanks);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.amtSight);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.amtDeferredNoAvalized);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.amtDeferredAvalized);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.amtPayableByNBE);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.firstInstallementMaturityDate);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.daysTillMaturity);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.beneficiaryDetails.name);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.beneficiaryDetails.bank);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.beneficiaryDetails.account);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.beneficiaryDetails.country.code);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.beneficiaryDetails.correspondentRefNum);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.tradeFOReferenceNumber);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.tradeFinanceApprovalNumber);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.executionHub.code);&#xD;
paramInit (tw.local.IDCRequest.financialDetails.executionHub.arabicdescription);&#xD;
paramInit (tw.local.IDCRequest.productsDetails.shippingDate);&#xD;
if (tw.local.IDCRequest.productsDetails.incoterms.id == 0) {&#xD;
	paramInit (null);&#xD;
} else {&#xD;
	paramInit (tw.local.IDCRequest.productsDetails.incoterms.id);&#xD;
}&#xD;
&#xD;
if (tw.local.IDCRequest.productsDetails.shipmentMethod.id == 0) {&#xD;
	paramInit (null);&#xD;
} else {&#xD;
	paramInit (tw.local.IDCRequest.productsDetails.shipmentMethod.id);&#xD;
}&#xD;
&#xD;
paramInit (tw.local.IDCRequest.productsDetails.destinationPort);&#xD;
if (tw.local.IDCRequest.productsDetails.CBECommodityClassification.id == 0) {&#xD;
	paramInit (null);&#xD;
} else {&#xD;
	paramInit (tw.local.IDCRequest.productsDetails.CBECommodityClassification.id);&#xD;
}&#xD;
&#xD;
paramInit (tw.local.IDCRequest.productsDetails.HSProduct.code);&#xD;
paramInit (tw.local.IDCRequest.productsDetails.HSProduct.englishdescription);&#xD;
paramInit (tw.local.IDCRequest.productsDetails.ACID);&#xD;
paramInit (tw.local.IDCRequest.countryOfOrigin.code);&#xD;
&#xD;
paramInit (tw.local.IDCRequest.DBID);&#xD;
//-------------------------------------------------------------------------------------------&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0" isCollection="true" name="sqlStatements" id="2056.b9206c57-ebfa-44da-80f2-45a474a00a3f" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" name="i" id="2056.48790657-e9c4-4474-8390-840e19f82662">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">0</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:sequenceFlow sourceRef="08188f42-8534-47c7-83a5-bf60b20530c5" targetRef="485c1cfa-b21c-4f9b-8c38-408c70d91afd" name="To UPDATE IDC Request" id="3f70aa03-14cd-4098-811e-ef15c12abae9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="08188f42-8534-47c7-83a5-bf60b20530c5" parallelMultiple="false" name="Error" id="fe41e509-5f0c-459e-847c-d70a899d3be5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="153" y="113" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>b19a024b-42ba-4014-86e3-2bcb9271fb8c</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="5b0321ed-d002-4c05-8f56-98483e229482" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="08f92dc3-2aa2-4607-84c5-1eadc4baab35" eventImplId="7c722598-0f7c-476f-84c7-94cf6e0a58eb">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="485c1cfa-b21c-4f9b-8c38-408c70d91afd" parallelMultiple="false" name="Error1" id="21552222-2fdb-4ae9-875c-b9a82761b60b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="295" y="114" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>5e65cdb5-7600-4574-8e98-e52d8c393a89</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2320fd86-a886-4b59-8eeb-212a322b8508" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="ffc8b9da-6ce9-43d5-838e-a2c1a68b4e73" eventImplId="5097d80b-ef39-4032-8a31-a3eb7a4c8fbe">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="8c791fc3-3fec-4c45-8b19-58d174daab20" parallelMultiple="false" name="Error2" id="68b88d8d-4d56-42d0-8463-31a3e6d422bf">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="462" y="113" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>a3091323-ea29-418a-8f55-3aace96a0238</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="62a620e0-829b-4c0d-8cae-a759c2943819" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="0cc3898b-f654-4ca4-8c2b-f38df1446876" eventImplId="3b81d5ed-621c-4551-83e0-19744dc00f98">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="a8fb1bfb-58c7-4ce7-8f29-137efb1dc5d9" parallelMultiple="false" name="Error3" id="babb4479-8247-49a2-896c-2883cf18c868">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="611" y="114" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>ae9760b1-3e54-4af3-8c8f-84d8bff70cfa</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="7870779c-7fa5-4591-8e70-dad36fd4eafc" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="7e6df494-0949-4314-8e84-e413883057ff" eventImplId="d833445d-97e8-418f-8f93-ce40e9d24623">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="6322d481-5d75-47ff-8b5f-56cada0079b8" parallelMultiple="false" name="Error4" id="6943fb57-7d25-492b-87e9-e6076031a215">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="793" y="113" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>c2b72103-6d45-4620-8545-2995c7bb499f</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="79dd141c-c3ab-43b4-8d18-16aec5ebcbd6" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="da114bbf-fd66-4c4c-80a8-232fbbe7cdec" eventImplId="a2ac3353-4350-44b4-832d-6f458199cf03">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="e66fcbcf-ea21-45ab-86e1-f555f1a2bb7a" parallelMultiple="false" name="Error5" id="220efb6a-ffba-4dd8-86f0-9e556b4573c0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="964" y="114" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>b75c322b-4188-4e62-82ed-82e85714a328</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="673954b5-1c0a-4820-8771-f85837fe4ef9" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="8eba4d11-7ac0-41e5-8494-27c136a964b1" eventImplId="6a034290-267a-4ef5-86d6-67d531b78962">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:endEvent name="End Event" id="e89eb804-d511-49e2-8947-ba49fc792260">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="531" y="190" width="24" height="24" />
                            
                            
                            <ns3:preAssignmentScript>log.info("*============ IDC =============*");&#xD;
log.info("[Update IDC Request -&gt; Log Error ]- start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
&#xD;
tw.local.error = new tw.object.AjaxError();&#xD;
var attribute = String(tw.system.error.getAttribute("type"));&#xD;
var element = String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
tw.local.errorMSG = attribute + "," + element;&#xD;
//tw.local.errorMSG =String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("[Update IDC Request -&gt; Log Error ]- END");&#xD;
log.info("*============================================*");&#xD;
&#xD;
tw.local.error.errorText = tw.local.errorMSG;</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>b19a024b-42ba-4014-86e3-2bcb9271fb8c</ns16:incoming>
                        
                        
                        <ns16:incoming>5e65cdb5-7600-4574-8e98-e52d8c393a89</ns16:incoming>
                        
                        
                        <ns16:incoming>a3091323-ea29-418a-8f55-3aace96a0238</ns16:incoming>
                        
                        
                        <ns16:incoming>ae9760b1-3e54-4af3-8c8f-84d8bff70cfa</ns16:incoming>
                        
                        
                        <ns16:incoming>c2b72103-6d45-4620-8545-2995c7bb499f</ns16:incoming>
                        
                        
                        <ns16:incoming>b75c322b-4188-4e62-82ed-82e85714a328</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:errorEventDefinition id="6b9a105e-e992-47a4-8891-8315c1a41c41" eventImplId="fe2ffa6e-23f0-4a38-862c-41f7ccf0a792">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="fe41e509-5f0c-459e-847c-d70a899d3be5" targetRef="e89eb804-d511-49e2-8947-ba49fc792260" name="To End Event" id="b19a024b-42ba-4014-86e3-2bcb9271fb8c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="21552222-2fdb-4ae9-875c-b9a82761b60b" targetRef="e89eb804-d511-49e2-8947-ba49fc792260" name="To End Event" id="5e65cdb5-7600-4574-8e98-e52d8c393a89">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="68b88d8d-4d56-42d0-8463-31a3e6d422bf" targetRef="e89eb804-d511-49e2-8947-ba49fc792260" name="To End Event" id="a3091323-ea29-418a-8f55-3aace96a0238">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="babb4479-8247-49a2-896c-2883cf18c868" targetRef="e89eb804-d511-49e2-8947-ba49fc792260" name="To End Event" id="ae9760b1-3e54-4af3-8c8f-84d8bff70cfa">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="6943fb57-7d25-492b-87e9-e6076031a215" targetRef="e89eb804-d511-49e2-8947-ba49fc792260" name="To End Event" id="c2b72103-6d45-4620-8545-2995c7bb499f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="220efb6a-ffba-4dd8-86f0-9e556b4573c0" targetRef="e89eb804-d511-49e2-8947-ba49fc792260" name="To End Event" id="b75c322b-4188-4e62-82ed-82e85714a328">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.5c0a8709-3c23-4859-89f0-37d37667fd87" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To DELETE Q">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.c2c0641c-c534-46ee-821f-928b01692f0c</processLinkId>
            <processId>1.e75c339e-472e-439b-878f-bfafd0c4b968</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.485c1cfa-b21c-4f9b-8c38-408c70d91afd</fromProcessItemId>
            <endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</endStateId>
            <toProcessItemId>2025.8c791fc3-3fec-4c45-8b19-58d174daab20</toProcessItemId>
            <guid>3dd6c224-a637-48c0-aff7-b43a9fa14918</guid>
            <versionId>20ac515a-bbef-4bf6-ba9d-396a1ce1d6b7</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.485c1cfa-b21c-4f9b-8c38-408c70d91afd</fromProcessItemId>
            <toProcessItemId>2025.8c791fc3-3fec-4c45-8b19-58d174daab20</toProcessItemId>
        </link>
        <link name="To INSERT Invoices &amp; Bills">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.02052b06-180c-4b80-86e4-2c397f656ac3</processLinkId>
            <processId>1.e75c339e-472e-439b-878f-bfafd0c4b968</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.6322d481-5d75-47ff-8b5f-56cada0079b8</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.e66fcbcf-ea21-45ab-86e1-f555f1a2bb7a</toProcessItemId>
            <guid>06090dec-32ab-4d2e-bd05-733e9f5c77e4</guid>
            <versionId>232f7509-337e-48d5-bbd2-a181058a5640</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.6322d481-5d75-47ff-8b5f-56cada0079b8</fromProcessItemId>
            <toProcessItemId>2025.e66fcbcf-ea21-45ab-86e1-f555f1a2bb7a</toProcessItemId>
        </link>
        <link name="To UPDATE IDC Request">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.3f70aa03-14cd-4098-811e-ef15c12abae9</processLinkId>
            <processId>1.e75c339e-472e-439b-878f-bfafd0c4b968</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.08188f42-8534-47c7-83a5-bf60b20530c5</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.485c1cfa-b21c-4f9b-8c38-408c70d91afd</toProcessItemId>
            <guid>57c9bafd-a812-4b93-9874-0ab78dda1b38</guid>
            <versionId>3db8161c-c76f-4bc4-82bc-d005a3172302</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.08188f42-8534-47c7-83a5-bf60b20530c5</fromProcessItemId>
            <toProcessItemId>2025.485c1cfa-b21c-4f9b-8c38-408c70d91afd</toProcessItemId>
        </link>
        <link name="To INSERT Q">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.26923925-0f09-4da5-8564-ee592a52a840</processLinkId>
            <processId>1.e75c339e-472e-439b-878f-bfafd0c4b968</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.a8fb1bfb-58c7-4ce7-8f29-137efb1dc5d9</fromProcessItemId>
            <endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</endStateId>
            <toProcessItemId>2025.6322d481-5d75-47ff-8b5f-56cada0079b8</toProcessItemId>
            <guid>4069b442-f507-49f4-8e33-b5cae62b50ce</guid>
            <versionId>437c8314-8e25-4090-a138-d3bfa2e803f2</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.a8fb1bfb-58c7-4ce7-8f29-137efb1dc5d9</fromProcessItemId>
            <toProcessItemId>2025.6322d481-5d75-47ff-8b5f-56cada0079b8</toProcessItemId>
        </link>
        <link name="To DELETE Invoices &amp; Bills">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.1a3ded6b-baee-47a8-856f-acab7c1c6667</processLinkId>
            <processId>1.e75c339e-472e-439b-878f-bfafd0c4b968</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8c791fc3-3fec-4c45-8b19-58d174daab20</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.a8fb1bfb-58c7-4ce7-8f29-137efb1dc5d9</toProcessItemId>
            <guid>217f77de-171b-4f91-9c3d-0fc7d88a41bf</guid>
            <versionId>53d5c17b-c454-4906-8891-4bb169cf5181</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.8c791fc3-3fec-4c45-8b19-58d174daab20</fromProcessItemId>
            <toProcessItemId>2025.a8fb1bfb-58c7-4ce7-8f29-137efb1dc5d9</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.01020eac-7ccd-4b18-81f0-2e98c68bc117</processLinkId>
            <processId>1.e75c339e-472e-439b-878f-bfafd0c4b968</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.e66fcbcf-ea21-45ab-86e1-f555f1a2bb7a</fromProcessItemId>
            <endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</endStateId>
            <toProcessItemId>2025.360f095c-c838-407d-8326-f25a1a115a02</toProcessItemId>
            <guid>003440e8-bfef-4bde-83e2-5711a71ce307</guid>
            <versionId>f2cac3b3-b47f-46d3-b057-7e321f3753bd</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.e66fcbcf-ea21-45ab-86e1-f555f1a2bb7a</fromProcessItemId>
            <toProcessItemId>2025.360f095c-c838-407d-8326-f25a1a115a02</toProcessItemId>
        </link>
    </process>
</teamworks>

