<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.19d05118-4f9d-482a-afd5-662663bc3612" name="Create IDC Request">
        <lastModified>1723034586020</lastModified>
        <lastModifiedBy>eslam</lastModifiedBy>
        <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.7d9060a3-db6f-4f1e-991c-a7b4c03053aa</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>false</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>ab6914a5-9a3e-4e42-b151-8573c692042e</guid>
        <versionId>c6684745-aea7-48d3-b17a-570cd239aa61</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.ed651f9e-edd3-4ef0-b049-225616fba16f"],"isInterrupting":true,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":12,"y":178,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"141c9309-b3d1-422b-a948-c8d0d4a9c81b"},{"incoming":["2027.f7d79e2e-d4f5-4ec4-8194-c9455b758a1c"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":1295,"y":123,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"End","declaredType":"endEvent","id":"ced35e6d-1321-4fd5-84dc-fc0e81d91c45"},{"targetRef":"2025.6d76e0dd-17bb-4e8d-98d1-82e8d0682c86","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Initialization Service","declaredType":"sequenceFlow","id":"2027.ed651f9e-edd3-4ef0-b049-225616fba16f","sourceRef":"141c9309-b3d1-422b-a948-c8d0d4a9c81b"},{"startQuantity":1,"outgoing":["2027.491dcb01-2d8e-4999-a6f0-8b87817c909a"],"incoming":["2027.1ad2c476-2429-4849-b48d-e8dc071b0173"],"default":"2027.491dcb01-2d8e-4999-a6f0-8b87817c909a","extensionElements":{"nodeVisualInfo":[{"width":95,"x":212,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Step Name","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.ce3318bc-ad0a-4809-b3e5-8d0a8f3f6929","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.idcRequest.appInfo.stepName = tw.system.processInstance.task.subject;\r\ntw.local.idcRequest.appInfo.appID = tw.system.processInstance.id;\r\n\r\n"]}},{"startQuantity":1,"outgoing":["2027.d4c507ee-6622-4a63-86c2-2d8c273474d5"],"incoming":["2027.37c1287e-8454-48d6-8236-610248cf002a"],"default":"2027.d4c507ee-6622-4a63-86c2-2d8c273474d5","extensionElements":{"nodeVisualInfo":[{"width":95,"x":929,"y":100,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Status ","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.48434093-edd9-4b92-8b57-feca505a198d","scriptFormat":"text\/x-javascript","script":{"content":["if (tw.local.idcRequest.IDCRequestType.englishdescription == \"IDC Payment\" &amp;&amp; tw.local.selectedAction == tw.epv.Action.submitRequestToHubDirectly) {\r\n\ttw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingExecutionHubLiquidation;\r\n\ttw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inExecution;\r\n}\r\nelse if (tw.local.idcRequest.IDCRequestType.englishdescription == \"IDC Amendment\" &amp;&amp; tw.local.selectedAction == tw.epv.Action.submitRequestToHubDirectly) {\r\n\ttw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingExecutionHubInitiation;\t\r\n\ttw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inExecution;\r\n}\r\nelse if (tw.local.selectedAction == tw.epv.Action.cancelReques) {\r\n\ttw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingBranchComplianceCancelationConfirmation;\r\n\ttw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.initiated;\r\n} \r\nelse {\t\r\n\ttw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingBranchComplianceInitiationReview;\r\n\ttw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.initiated;\r\n}\r\n\r\ntw.local.idcRequest.stepLog.action = tw.local.selectedAction;"]}},{"targetRef":"2025.0c9a7cd3-c644-472f-8c23-976def594caa","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"OK To End","declaredType":"sequenceFlow","id":"2027.d4c507ee-6622-4a63-86c2-2d8c273474d5","sourceRef":"2025.48434093-edd9-4b92-8b57-feca505a198d"},{"outgoing":["2027.5b57c95f-3145-4ff1-b121-54b1b660ed84","2027.58aa3cd1-28e5-4d95-a115-9a0418d3e55b"],"incoming":["2027.53e9259e-a120-43ef-99ca-fb822db0f39d","2027.09f07a5e-4dcf-4ede-a465-3f7689a9bb0f","2027.8303ffc2-34a7-4d7f-8bd7-b43829b1c16d","2027.ed651f9e-edd3-4ef0-b049-225616fba16f"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":381,"y":155,"declaredType":"TNodeVisualInfo","height":70}]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"layoutItemId":"Error_Message1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0abe9db1-c47f-4505-8b0f-8d6bec43aea6","optionName":"@label","value":"Error Message"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"be9ac253-c385-4322-8ad8-90afab999685","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d7e682ad-c27e-477c-87fc-7de40826e05b","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"dda57087-e7be-473b-8482-62dd45c24ded","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9c625c6f-aeb7-4b4a-8b02-61f3be54b3b7","optionName":"@visibility","value":"tw.local.errorVIS"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e124408f-3c0f-4ac0-8dce-fcc1599409b0","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.46690ca1-5521-4e5c-bc9e-ecb22d75ca97","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"99a5568f-10e0-475e-8e05-e82e4c291385","version":"8550"},{"contentBoxContrib":[{"contributions":[{"layoutItemId":"Start_New_Request","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fa3af240-c889-4cfd-80f1-f904807acdf0","optionName":"@label","value":"Start New Request"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e38a8ba6-5e3e-407b-8642-4dd77d3beeea","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"813d1354-f541-4412-8719-2ac60972e5d6","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f10559d5-59ab-4c56-89c8-87426ad84a75","optionName":"parentIDCRequest","value":"tw.local.tmpIDCrequest"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"40c924ee-dd90-4e23-8f48-ab84d28ff4a0","optionName":"retrieveRequest","value":"tw.local.retrieveRequest"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f08220c7-8fce-4893-8bfd-8a69e4c49641","optionName":"retrieveCustomer","value":"tw.local.retrieveCustomer"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"273e0b48-a16b-4ba4-8fc8-98f32c4d30e5","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a6fdef21-d133-48c7-828b-df2a9f18ef59","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.f547d7e6-17be-4187-b0e1-14f05f5fdfd4","binding":"tw.local.idcRequest","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"59820df2-db91-4b97-863e-68284c5efa03","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"5712c7a3-1da3-468a-8220-8dd1cb7e2ce9"}],"layoutItemId":"Header_View2","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"06142723-9d7f-44b4-8539-98dc930d0ff6","optionName":"@label","value":"Header View 2"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"abc3519b-23da-4236-8105-821292ae3e60","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"196b2815-bed1-406a-8a45-68bb58783e39","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1bef3b9a-20bb-43e9-835a-d6b0268e16ca","optionName":"buttonName","value":"Create Request "},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e81cc769-c8c6-4368-828e-fe82753deae0","optionName":"stepLog","value":"tw.local.idcRequest.stepLog"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"897334ee-922d-4806-8df7-ff8df6640773","optionName":"approvals","value":"tw.local.idcRequest.approvals"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"be146911-37e3-46f3-855b-5499bded5c9e","optionName":"action","value":"tw.local.action[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0a9e11f8-e507-421b-8e02-64c340d9ce57","optionName":"selectedAction","value":"tw.local.selectedAction"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"398485b1-048e-4648-8494-d1532eb3ff7d","optionName":"hasReturnReason","value":"tw.local.hasReturnReason"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"29c86d27-aae9-495b-8856-bbf65a95b110","optionName":"hasApprovals","value":"tw.local.haseApprovals"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"943a19ee-d356-4fc2-8bb8-6d99e644457d","optionName":"approvalsReadOnly","value":"false"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a3f4388f-cb67-4d56-8c20-35495703d4a0","optionName":"invalidTabs","value":"tw.local.invalidTabs[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6ff6f5fc-885c-4528-801a-fa2fb92cf982","optionName":"validationMessage","value":"tw.local.validationMessage"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a46181df-e6ee-4163-8e58-a8d92e8405cc","optionName":"isCAD","value":"false"}],"viewUUID":"64.f9e6899d-e7d7-4296-ba71-268fcd57e296","binding":"tw.local.idcRequest.appInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"c1b815ac-7bf9-4c2e-869f-27d9c10b5fd8","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"Create IDC Request","isForCompensation":false,"completionQuantity":1,"id":"2025.6d76e0dd-17bb-4e8d-98d1-82e8d0682c86"},{"targetRef":"2025.5dab97c3-4bdd-4631-a0f6-e602a9c3ba8c","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"7b24b46d-3335-4df8-9a4a-01c1b6b8b32f","coachEventPath":"Header_View2\/submit"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topLeft","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftBottom","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To IDC Request Data","declaredType":"sequenceFlow","id":"2027.5b57c95f-3145-4ff1-b121-54b1b660ed84","sourceRef":"2025.6d76e0dd-17bb-4e8d-98d1-82e8d0682c86"},{"targetRef":"2025.baab68ba-c57c-48fb-ad2d-8499438e3995","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Attachment","declaredType":"sequenceFlow","id":"2027.491dcb01-2d8e-4999-a6f0-8b87817c909a","sourceRef":"2025.ce3318bc-ad0a-4809-b3e5-8d0a8f3f6929"},{"outgoing":["2027.53e9259e-a120-43ef-99ca-fb822db0f39d"],"incoming":["2027.58aa3cd1-28e5-4d95-a115-9a0418d3e55b"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition"}],"extensionElements":{"default":["2027.53e9259e-a120-43ef-99ca-fb822db0f39d"],"nodeVisualInfo":[{"width":24,"x":420,"y":258,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Postpone","declaredType":"intermediateThrowEvent","id":"2025.5c073fba-612b-44a2-b49b-d5bad4a9bc05"},{"targetRef":"2025.5c073fba-612b-44a2-b49b-d5bad4a9bc05","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"4dd49ae0-3f50-45e2-919b-9dc6b06dcffc","coachEventPath":"Header_View2\/saveState"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomRight","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Postpone","declaredType":"sequenceFlow","id":"2027.58aa3cd1-28e5-4d95-a115-9a0418d3e55b","sourceRef":"2025.6d76e0dd-17bb-4e8d-98d1-82e8d0682c86"},{"targetRef":"2025.6d76e0dd-17bb-4e8d-98d1-82e8d0682c86","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Create IDC Request","declaredType":"sequenceFlow","id":"2027.53e9259e-a120-43ef-99ca-fb822db0f39d","sourceRef":"2025.5c073fba-612b-44a2-b49b-d5bad4a9bc05"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = {};\nautoObject.IDCRequestState = \"\";\nautoObject.commodityDescription = \"\";\nautoObject.countryOfOrigin = {};\nautoObject.countryOfOrigin.id = 0;\nautoObject.countryOfOrigin.code = \"\";\nautoObject.countryOfOrigin.arabicdescription = \"\";\nautoObject.countryOfOrigin.englishdescription = \"\";\nautoObject.appInfo = {};\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = {};\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.productsDetails = {};\nautoObject.productsDetails.destinationPort = \"\";\nautoObject.productsDetails.shippingDate = new Date();\nautoObject.productsDetails.HSProduct = {};\nautoObject.productsDetails.HSProduct.id = 0;\nautoObject.productsDetails.HSProduct.code = \"\";\nautoObject.productsDetails.HSProduct.arabicdescription = \"\";\nautoObject.productsDetails.HSProduct.englishdescription = \"\";\nautoObject.productsDetails.incoterms = {};\nautoObject.productsDetails.incoterms.id = 0;\nautoObject.productsDetails.incoterms.code = \"\";\nautoObject.productsDetails.incoterms.arabicdescription = \"\";\nautoObject.productsDetails.incoterms.englishdescription = \"\";\nautoObject.productsDetails.ACID = \"\";\nautoObject.productsDetails.CBECommodityClassification = {};\nautoObject.productsDetails.CBECommodityClassification.id = 0;\nautoObject.productsDetails.CBECommodityClassification.code = \"\";\nautoObject.productsDetails.CBECommodityClassification.arabicdescription = \"\";\nautoObject.productsDetails.CBECommodityClassification.englishdescription = \"\";\nautoObject.productsDetails.shipmentMethod = {};\nautoObject.productsDetails.shipmentMethod.id = 0;\nautoObject.productsDetails.shipmentMethod.code = \"\";\nautoObject.productsDetails.shipmentMethod.arabicdescription = \"\";\nautoObject.productsDetails.shipmentMethod.englishdescription = \"\";\nautoObject.financialDetails = {};\nautoObject.financialDetails.isAdvancePaymentsUsed = false;\nautoObject.financialDetails.paymentTerms = [];\nautoObject.financialDetails.paymentTerms[0] = {};\nautoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;\nautoObject.financialDetails.paymentTerms[0].installmentDate = new Date();\nautoObject.financialDetails.usedAdvancePayment = [];\nautoObject.financialDetails.usedAdvancePayment[0] = {};\nautoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = {};\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;\nautoObject.financialDetails.discountAmt = 0.0;\nautoObject.financialDetails.firstInstallementMaturityDate = new Date();\nautoObject.financialDetails.facilityAmtInDocCurrency = 0.0;\nautoObject.financialDetails.amtSight = 0.0;\nautoObject.financialDetails.beneficiaryDetails = {};\nautoObject.financialDetails.beneficiaryDetails.account = \"\";\nautoObject.financialDetails.beneficiaryDetails.bank = \"\";\nautoObject.financialDetails.beneficiaryDetails.correspondentRefNum = \"\";\nautoObject.financialDetails.beneficiaryDetails.name = \"\";\nautoObject.financialDetails.beneficiaryDetails.country = {};\nautoObject.financialDetails.beneficiaryDetails.country.id = 0;\nautoObject.financialDetails.beneficiaryDetails.country.code = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.arabicdescription = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.englishdescription = \"\";\nautoObject.financialDetails.amtDeferredNoAvalized = 0.0;\nautoObject.financialDetails.amtPayableByNBE = 0.0;\nautoObject.financialDetails.executionHub = {};\nautoObject.financialDetails.executionHub.id = 0;\nautoObject.financialDetails.executionHub.code = \"\";\nautoObject.financialDetails.executionHub.arabicdescription = \"\";\nautoObject.financialDetails.executionHub.englishdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency = {};\nautoObject.financialDetails.sourceOfForeignCurrency.id = 0;\nautoObject.financialDetails.sourceOfForeignCurrency.code = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.englishdescription = \"\";\nautoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.documentCurrency = {};\nautoObject.financialDetails.documentCurrency.id = 0;\nautoObject.financialDetails.documentCurrency.code = \"\";\nautoObject.financialDetails.documentCurrency.arabicdescription = \"\";\nautoObject.financialDetails.documentCurrency.englishdescription = \"\";\nautoObject.financialDetails.daysTillMaturity = 0;\nautoObject.financialDetails.tradeFinanceApprovalNumber = \"\";\nautoObject.financialDetails.amountAdvanced = 0.0;\nautoObject.financialDetails.paymentAccount = \"\";\nautoObject.financialDetails.tradeFOReferenceNumber = \"\";\nautoObject.financialDetails.documentAmount = 0.0;\nautoObject.financialDetails.sourceOfFunds = {};\nautoObject.financialDetails.sourceOfFunds.id = 0;\nautoObject.financialDetails.sourceOfFunds.code = \"\";\nautoObject.financialDetails.sourceOfFunds.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfFunds.englishdescription = \"\";\nautoObject.financialDetails.chargesAccount = \"\";\nautoObject.financialDetails.CashAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.amtDeferredAvalized = 0.0;\nautoObject.financialDetails.amtPaidbyOtherBanks = 0.0;\nautoObject.financialDetails.cashAmtInDocCurrency = 0.0;\nautoObject.IDCRequestType = {};\nautoObject.IDCRequestType.id = 0;\nautoObject.IDCRequestType.code = \"\";\nautoObject.IDCRequestType.arabicdescription = \"\";\nautoObject.IDCRequestType.englishdescription = \"\";\nautoObject.isIDCWithdrawn = false;\nautoObject.IDCRequestStage = \"\";\nautoObject.FCContractNumber = \"\";\nautoObject.billOfLading = [];\nautoObject.billOfLading[0] = {};\nautoObject.billOfLading[0].date = new Date();\nautoObject.billOfLading[0].number = \"\";\nautoObject.importPurpose = {};\nautoObject.importPurpose.id = 0;\nautoObject.importPurpose.code = \"\";\nautoObject.importPurpose.arabicdescription = \"\";\nautoObject.importPurpose.englishdescription = \"\";\nautoObject.IDCRequestNature = {};\nautoObject.IDCRequestNature.id = 0;\nautoObject.IDCRequestNature.code = \"\";\nautoObject.IDCRequestNature.arabicdescription = \"\";\nautoObject.IDCRequestNature.englishdescription = \"\";\nautoObject.customerInformation = {};\nautoObject.customerInformation.CIFNumber = \"\";\nautoObject.customerInformation.importCardNumber = \"\";\nautoObject.customerInformation.commercialRegistrationNumber = \"\";\nautoObject.customerInformation.customerName = \"\";\nautoObject.customerInformation.isCustomeSanctionedbyCBE = false;\nautoObject.customerInformation.CBENumber = \"\";\nautoObject.customerInformation.customerSector = \"\";\nautoObject.customerInformation.facilityType = \"\";\nautoObject.customerInformation.commercialRegistrationOffice = \"\";\nautoObject.customerInformation.taxCardNumber = \"\";\nautoObject.customerInformation.customerType = \"\";\nautoObject.invoices = [];\nautoObject.invoices[0] = {};\nautoObject.invoices[0].date = new Date();\nautoObject.invoices[0].number = \"\";\nautoObject.productCategory = {};\nautoObject.productCategory.id = 0;\nautoObject.productCategory.code = \"\";\nautoObject.productCategory.arabicdescription = \"\";\nautoObject.productCategory.englishdescription = \"\";\nautoObject.documentsSource = {};\nautoObject.documentsSource.id = 0;\nautoObject.documentsSource.code = \"\";\nautoObject.documentsSource.arabicdescription = \"\";\nautoObject.documentsSource.englishdescription = \"\";\nautoObject.ParentIDCRequestNumber = \"\";\nautoObject.paymentTerms = {};\nautoObject.paymentTerms.id = 0;\nautoObject.paymentTerms.code = \"\";\nautoObject.paymentTerms.arabicdescription = \"\";\nautoObject.paymentTerms.englishdescription = \"\";\nautoObject.approvals = {};\nautoObject.approvals.CAD = false;\nautoObject.approvals.treasury = false;\nautoObject.approvals.compliance = false;\nautoObject"}]},"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"tmpIDCrequest","isCollection":false,"declaredType":"dataObject","id":"2056.c034f8c0-6cfd-445e-a683-0a33aa97f71f"},{"outgoing":["2027.7c77eb27-c50f-469c-a575-96530c0ae69c","2027.7d5b995d-f6f6-4fa4-8ed5-7dd8fa484da7"],"incoming":["2027.1e0bb62b-d40a-4cb4-8c63-2d4d15c326b6","2027.ff715148-f279-4371-849f-de7043fa1d26","2027.9cecf069-47c9-41c6-9247-cc005b368801","2027.0fd60f96-4c02-43df-831d-0328a3497a99"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":659,"y":100,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["DC_Templete1\/submit"],"preAssignmentScript":["tw.local.idcRequest.appInfo.subStatus = \"final\";"]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"layoutItemId":"Error_Message1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2ea8235b-224e-48ca-80fa-71f8340a23cd","optionName":"@label","value":"Error Message"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"16718ba5-09ec-4457-8d65-dfe5330e33af","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fe4f9c40-1fa3-43a5-89c1-4d90915bc74e","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a3b0c6ca-29d0-4c37-8e6a-7c28d3c96cc8","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"97f90d72-13f5-4223-82ea-b96d5e4890fe","optionName":"@visibility","value":"tw.local.errorVIS"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"98c5ce44-992e-4069-8278-7ee7b4308abe","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.46690ca1-5521-4e5c-bc9e-ecb22d75ca97","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"8df2d008-cc3e-4dbd-8aaa-995669d21c9e","version":"8550"},{"contentBoxContrib":[{"contributions":[{"contentBoxContrib":[{"contributions":[{"layoutItemId":"0","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e96d13a1-5f98-440c-864a-51419fee15ac","optionName":"@label","value":"Customer Information"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"06bc7d1e-9ed0-440f-8308-aab6bd2e23c1","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"08401cec-**************-d49cdbda2f3b","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"88754e9c-ff6f-43f5-8f49-6510c65d158c","optionName":"validationTabs","value":"tw.local.invalidTabs[]"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0f4ad413-662d-43ae-8511-38782f201238","optionName":"instanceview","value":"false"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"40d98c16-f213-4ef9-8b7a-491d90c43643","optionName":"alertMessage","value":"tw.local.alertMessage"}],"viewUUID":"64.656cc232-8247-43c3-9481-3cc7a9aec2e6","binding":"tw.local.idcRequest.customerInformation","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"6de938d3-7ad8-4abb-8950-153cb0fa0086","version":"8550"},{"layoutItemId":"1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"007b54be-3009-4e40-8063-66006bd2c4d7","optionName":"@label","value":"Basic Details"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"02805ab2-bc79-4baa-89e4-930e049cbd02","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"84bdf173-e130-44c2-8baf-d75c0e9a8c48","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cbaefb68-8984-40da-879e-e3cb662f9290","optionName":"addBill","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"901a5aa3-a8fc-46bd-831c-b7f701bc32d4","optionName":"deleteBill","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2d0a7b55-0937-4a6d-8158-876a1c17c108","optionName":"addInvoice","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8217ac1e-fe7b-46c0-81cd-53db4efc4242","optionName":"deleteInvoice","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4e7ab343-6319-4368-894e-4a6719f0bf3e","optionName":"hasWithdraw","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7a044f62-b215-443d-8d00-3e0a65b1ce35","optionName":"havePaymentTerm","value":"tw.local.havePaymentTerms"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6aa1e8a2-77b2-4b27-85fa-354122df599d","optionName":"alertMessage","value":"tw.local.errorMSG"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"40ae9c21-85d1-4e2a-8824-dd3a7188d758","optionName":"billExist","value":"0"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"89ece940-ca07-4064-824e-a5a38cb503a9","optionName":"invoiceExist","value":"0"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7c92d517-81d8-4d78-875f-5d142e9d81b0","optionName":"errorVis","value":"tw.local.errorVIS"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1776b765-29be-4f72-8b2a-faebfa909f30","optionName":"idcContract","value":"tw.local.idcContract"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6f3497ed-2f96-4db7-81c0-a9e2ff71d2c2","optionName":"InterestAndChargesList","value":"tw.local.InterestAndChargesList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cf1607f3-4591-49b0-861e-fc39e936a675","optionName":"stage","value":"tw.local.idcRequest.IDCRequestStage"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"563876d1-1725-4e50-8846-3c5278d7c00f","optionName":"exRate","value":"tw.local.exRate"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"299237f5-44b6-4df2-871d-38f4084318b7","optionName":"adviceCodeList","value":"tw.local.adviceCodeList[]"}],"viewUUID":"64.3d13e9b9-bcc9-4fe9-ac80-3c287115f705","binding":"tw.local.idcRequest","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"0568f87f-5223-4be5-8f75-18301042981a","version":"8550"},{"layoutItemId":"2","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5b442036-93a2-488f-8f14-08ef7166c4bb","optionName":"@label","value":"Financial Details - Branch"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4362b91a-f56f-4f8e-855d-06c65df3e0fb","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"634753d7-f4e5-4594-81d5-1dfb5d6b598e","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"46bff10b-24a0-4203-80fe-f7a5cee682b5","optionName":"@overflow","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"visible\"}]}"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"527f7fb4-d9ae-49d3-8874-aac7dcdcd3da","optionName":"@visibility.script"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c7f708ed-de24-4756-83c4-60940332531d","optionName":"advancePaymentsUsedOption","value":"tw.local.advancePaymentsUsedVis"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3d15c48c-a5da-4be2-80df-1750e9be38f9","optionName":"docAmount","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"eee8f66f-f156-49ec-8e97-0ee29e5e5e03","optionName":"currncy","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cf34bb0e-415f-477b-86ae-58118b761e99","optionName":"CIF","value":"tw.local.idcRequest.customerInformation.CIFNumber"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"604d05ac-df9b-4e73-8e1f-************","optionName":"accountsList","value":"tw.local.accountsList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"39404d9b-de4d-4ef2-8cea-1d97691da137","optionName":"requestType","value":"tw.local.idcRequest.IDCRequestType.englishdescription"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f457b721-663a-47b5-8dbb-905ff6248c5b","optionName":"haveAmountAdvanced","value":"DEFAULT"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7652ca7b-05a1-4b0b-8764-cd495f7acb1b","optionName":"tmpUsedAdvancePayment","value":"tw.local.tmpUsedAdvancePayment"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7d49223a-d4bd-4c0a-87f2-cd3c4e6d915a","optionName":"isChecker","value":"false"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7fd12a0c-0d63-4879-8fcf-6492a7baef4b","optionName":"currencyVis","value":"tw.local.currencyVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6e25e78c-2f01-41cd-8352-57645f283106","optionName":"requestID","value":"tw.local.idcRequest.appInfo.instanceID"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4c464311-1f0d-4011-8ba4-dbb1e40ceee1","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"47513ac0-aba3-49d3-8516-7e73fa4fd709","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.74d3cb97-ad59-4249-847b-a21122e44b22","binding":"tw.local.idcRequest.financialDetails","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"99bde13d-42c9-4107-8f3b-3e288f058005","version":"8550"},{"layoutItemId":"3","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"446d8e7a-19f4-4eb6-8ccd-8c627254440e","optionName":"@label","value":"Attachment"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5c709e85-3d08-4826-8fef-213d52428b21","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9c1fde63-9e17-4aa0-88cf-06f50fa1fdd2","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"91550808-62ee-439b-8d49-d43d230cd559","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"EDITABLE\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d56ef36b-c2ed-4cae-8567-2b85ddbc7994","optionName":"canUpdate","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a71fc15f-e77a-433b-87bc-b64fea8cc2d4","optionName":"canCreate","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5e016d6f-d59d-42c3-8d68-d00de21ee175","optionName":"fullPath","value":"tw.local.fullPath"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b639b6be-bc96-4d93-88fe-7324dc9a4df1","optionName":"canDelete","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2f5ac9c7-8d5f-4693-8639-c9e87a91dd43","optionName":"default_properties","value":"tw.local.default_properties[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"43c07595-f33b-49fa-8e3a-ea1de02fb75a","optionName":"parentPath","value":"tw.local.parentPath"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"62e77e7e-fa86-4836-8c1e-34e8e6ed6fe0","optionName":"cmisQuery","value":"tw.local.cmisQuery"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"62fbdc76-3a7e-4759-840f-b13909a83bef","optionName":"ECMproperties","value":"tw.local.ECMproperties"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2ae19864-5592-4b0b-84d3-1ef7c1dbdd21","optionName":"visiable","value":"true"}],"viewUUID":"64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7","binding":"tw.local.attachment[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"de643479-1690-4c6d-8f83-ad6e0681badf","version":"8550"},{"layoutItemId":"Party1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fcb76d01-c067-4f7f-8115-acdb8284ec27","optionName":"@label","value":"Party"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f831e657-2429-46bd-88c5-fd09f0fbfd18","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a5caac1b-7e5e-4256-876a-323948c663e3","optionName":"@labelVisibility","value":"SHOW"}],"viewUUID":"64.42200ef0-f4f7-4d44-aa41-e48db0e2da1f","binding":"tw.local.idcContract.party[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"40d96360-b51c-4f35-8f35-f015834bcd92","version":"8550"},{"layoutItemId":"4","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"93f9dfcb-a756-4925-8c74-a3d1c2e14b73","optionName":"@label","value":"History "},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fad74bc8-047f-4278-86ba-95105c74445d","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"097a0a47-4a65-463e-8342-8cc0f3025534","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"88cc43f7-f3f4-4bf9-8b01-33c086a02fdc","optionName":"panelCollapse","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"dd280358-ec16-48df-8681-23efab9ac302","optionName":"historyVisFlag","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"19215b9b-b70a-40c2-86d2-b131e36a3ac5","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"DEFAULT\"}]}"}],"viewUUID":"64.6ac8a533-b05a-45a1-9e46-1c4542e6b8be","binding":"tw.local.idcRequest.appLog[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"2cff5a7e-bdf6-414e-8045-7682a651df97","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"678ab998-1239-4d5c-8cd0-3fd22ed49140"}],"layoutItemId":"Tab_section1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d10ec0a1-3058-49e2-8daf-9739f827296b","optionName":"@label","value":"Tab section"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"87494c05-2452-4cb6-83b2-6181bd7a43f5","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"53ba771a-b691-4cb0-80f3-810b5146b50e","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"bfe7bb53-ff9c-49f3-87e3-a02f905a0c70","optionName":"colorStyle","value":"D"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3efa1206-81c7-4b0e-8c30-6f86b2a2f49e","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"REQUIRED\"}]}"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"02538a2c-30b1-4d99-8ed4-76587e836ca1","optionName":"defaultPaneIdx","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9740a58b-386d-4555-84bd-83f7f74eff37","optionName":"eventON_LOAD","value":"\r\n\r\n"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1b0e4b67-780a-44da-8d63-4a9cbf33bbd4","optionName":"tabsStyle","value":"{\"isResponsiveData\":true,\"values\":[]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c87d0c28-2cdc-411c-8e92-6c54981a36f4","optionName":"sizeStyle","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"X\"}]}"}],"viewUUID":"64.c05b439f-a4bd-48b0-8644-fa3b59052217","binding":"","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"0717c90f-3cf5-4c4f-8185-215410509b7a","version":"8550"},{"layoutItemId":"Data1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0c139319-0582-491d-8bbe-e304059e0717","optionName":"@label","value":"Data"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c605b490-0ba1-4790-82e0-c97e37f5e69d","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3d05a0f0-6063-48d5-844b-a8529df88913","optionName":"@labelVisibility","value":"SHOW"}],"viewUUID":"64.9b679256-e93b-4400-89f2-bd15b0c5578d","binding":"tw.local.invalidTabs[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"8e0e729d-33d4-47f1-87d3-c174871b8d66","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"a1e05320-692a-4d33-81ab-c07215777f1b"}],"layoutItemId":"DC_Templete1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f5d4d26d-dd96-49ab-88e1-aa01877abce4","optionName":"@label","value":"DC Templete"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d66194f3-b584-49c9-8e5f-4c1b4f2a0129","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"79b8f2b1-85b1-4366-8a15-1b8caabaf62a","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8e14b6cf-991e-4974-8d12-8d543892c039","optionName":"buttonName","value":"Submit"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"398671fe-2bf8-4d0b-8f11-9bfaad8ed4d2","optionName":"stepLog","value":"tw.local.idcRequest.stepLog"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"18d6a862-352f-4bd8-88db-f62a6d5154c1","optionName":"action","value":"tw.local.action[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8b4c710e-eefe-41b9-8b31-5c1de12d70c9","optionName":"selectedAction","value":"tw.local.selectedAction"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"841c020c-78f8-4408-80c8-137819769042","optionName":"hasApprovals","value":"tw.local.haseApprovals"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c296b536-aea9-42ea-8660-8b14151154bb","optionName":"hasReturnReason","value":"tw.local.hasReturnReason"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e7a842a3-6fcf-4a52-8fdf-bdcf506e80a0","optionName":"approvals","value":"tw.local.idcRequest.approvals"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"71503b8f-7e42-4118-8a41-8d8afeab6895","optionName":"validationTabs","value":"tw.local.invalidTabs[]"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c0fc8584-3380-4c61-88ce-25574416c45e","optionName":"approvalsReadOnly","value":"false"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b298adb9-b84a-4941-8652-b8f966f93b51","optionName":"invalidTabs","value":"tw.local.invalidTabs[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6827a42b-3438-4420-8d60-812211e849d1","optionName":"validationMessage","value":"tw.local.validationMessage"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8696f490-7a82-4247-8129-e3ba4e007c2b","optionName":"isCAD","value":"false"}],"viewUUID":"64.f9e6899d-e7d7-4296-ba71-268fcd57e296","binding":"tw.local.idcRequest.appInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"b6af9762-fe4c-41cb-8e81-7cecfddcec7a","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"IDC Request Data","isForCompensation":false,"completionQuantity":1,"id":"2025.49ca7e00-8853-4d3f-8d52-119f94ff3e10"},{"targetRef":"2025.6f1cd112-951d-4cfc-af36-d0c02ea94f18","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"77a742fe-7df4-4267-9eb8-dfe2a48c754c","coachEventPath":"DC_Templete1\/saveState"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomRight","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Set Status ","declaredType":"sequenceFlow","id":"2027.7c77eb27-c50f-469c-a575-96530c0ae69c","sourceRef":"2025.49ca7e00-8853-4d3f-8d52-119f94ff3e10"},{"targetRef":"2025.456c9026-c887-42e9-aa46-c2db3a83a3f6","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"015d572e-2ba2-45a0-8728-866353b792cd","coachEventPath":"DC_Templete1\/submit"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To validate accounts and attachment","declaredType":"sequenceFlow","id":"2027.7d5b995d-f6f6-4fa4-8ed5-7dd8fa484da7","sourceRef":"2025.49ca7e00-8853-4d3f-8d52-119f94ff3e10"},{"startQuantity":1,"outgoing":["2027.70bea525-3044-47fb-af1d-24678fd9aa53"],"incoming":["2027.4aa6a2cc-3485-4d95-8093-0e7dd7afac2a","2027.7d5b995d-f6f6-4fa4-8ed5-7dd8fa484da7"],"default":"2027.70bea525-3044-47fb-af1d-24678fd9aa53","extensionElements":{"nodeVisualInfo":[{"color":"#95D087","width":95,"x":739,"y":-5,"declaredType":"TNodeVisualInfo","height":70}]},"name":"validation","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.456c9026-c887-42e9-aa46-c2db3a83a3f6","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.message = \"\";\r\ntw.local.validationMessage = \"\";\r\nvar mandatoryTriggered = false;\r\nvar tempLength = 0 ;\r\ntw.local.invalidTabs = [];\r\n\/*\r\n* =========================================================================================================\r\n*  \r\n* Add a coach validation error \r\n* \t\t\r\n* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\n*\r\n* =========================================================================================================\r\n*\/\r\n\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\n{\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\n\tfromMandatory &amp;&amp; mandatoryTriggered ? \"\" : tw.local.message += \"&lt;li dir='rtl'&gt;\" + validationMessage + \"&lt;\/li&gt;\";\r\n}\r\n\r\n\/*\r\n* ==============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is in the past and the last variable to exclude today\r\n*\t\r\n* EX:\tnotPastDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)\r\n*\r\n* =============================================================================================================================\r\n*\/\r\n\r\nfunction notPastDate(date , fieldName , controlMessage , validationMessage , exclude)\r\n{\r\n\tif (exclude)\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &lt;= new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\t\r\n\t}\r\n\telse\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &lt; new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\r\n\t}\r\n}\r\n\r\n\/*\r\n* ==============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is between two dates\r\n*\t\r\n* EX:\tdateBetween(tw.local.date , date1 , date2 , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!')\r\n*\r\n* =============================================================================================================================\r\n*\/\r\n\r\nfunction dateBetween(field , date1 , date2 , fieldName , controlMessage , validationMessage)\r\n{\r\n\tif(field &lt; date1 &amp;&amp; field &gt; date2)\r\n\t{\r\n\t \treturn true;\r\n\t}\r\n\taddError(fieldName , controlMessage , validationMessage);\r\n\treturn false;\r\n}\r\n\r\n\/*\r\n* ===============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is in the future and the last varaible to exculde today\r\n*\t\r\n* EX:\tnotFutureDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)\r\n*\r\n* ===============================================================================================================================\r\n*\/\r\n\r\nfunction notFutureDate(date , fieldName ,  controlMessage , validationMessage , exculde)\r\n{\r\n\tif (exculde)\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &gt;= new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\t\r\n\t}\r\n\telse\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &gt; new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\r\n\t}\r\n}\r\n\r\n\r\n\/*\r\n* =================================================================================================================================\r\n*\r\n* Add a coach validation error if the string length is less than given length\r\n*\t\r\n* EX:\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\r\n*\r\n* =================================================================================================================================\r\n*\/\r\n\r\nfunction minLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the string length is greater than given length\r\n*\t\r\n* EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field != null &amp;&amp; field != undefined &amp;&amp; field.length &gt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the number is greater than given value\r\n*\t\r\n* EX:\tmaxNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction maxNumber(field , fieldName , max , controlMessage , validationMessage)\r\n{\r\n\tif (field &gt; max)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the number is less than given value\r\n*\t\r\n* EX:\tminNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction minNumber(field , fieldName , min , controlMessage , validationMessage)\r\n{\r\n\tif (field &lt; min)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a coach validation error if the field is null 'Mandatory'\r\n*\t\r\n* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\r\nfunction mandatory(field , fieldName)\r\n{\r\n\tif (field == null || field == undefined )\r\n\t{\r\n\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\tmandatoryTriggered = true;\r\n\t\treturn false;\r\n\t}\r\n\telse\r\n\t{\r\n\t\tswitch (typeof field)\r\n\t\t{\r\n\t\t\tcase \"string\":\r\n\t\t\t\tif (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"number\":\r\n\t\t\t\tif (field == 0.0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a validation mark on the tab if there is an error\r\n*\t\r\n* EX:\tvalidateTab(1)\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\r\nfunction validateTab(index , tabName)\r\n{\r\n\tif (tw.system.coachValidation.validationErrors.length &gt; tempLength)\r\n\t{\r\n\t\tif (tw.local.validationMessage.length == 0) {\r\n\t\t\ttw.local.validationMessage += \"&lt;p&gt;\" + \"Please complete fields in the following tabs:\" + \"&lt;\/p&gt;\";\r\n\t\t}\r\n\t\ttw.local.validationMessage += \"&lt;li&gt;\" + tabName + \"&lt;\/li&gt;\";\r\n\t\ttempLength = tw.system.coachValidation.validationErrors.length ;\r\n\t\ttw.local.invalidTabs[tw.local.invalidTabs.length] = index;\r\n\t}\t\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a validation mark on the tab if there is an error\r\n*\t\r\n* EX:\tvalidateTab(1)\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\/\/function validateDecimal(field, fieldName, controlMessage , validationMessage) {\r\n\/\/   regexString = `^\\\\d{1,12}(\\\\.\\\\d{1,12})?$`;\r\n\/\/   regex = new RegExp(regexString);\r\n\/\/\r\n\/\/  if (!regex.test(field))\r\n\/\/\t{\r\n\/\/\t\taddError(fieldName , controlMessage , validationMessage);\r\n\/\/\t\treturn false;\r\n\/\/\t}\r\n\/\/\treturn true;\r\n\/\/}\r\n\r\n\/\/-----------------------------------customer info[0]--------------------------------------------------------\r\nmandatory(tw.local.idcRequest.customerInformation.facilityType.id,\"tw.local.idcRequest.customerInformation.facilityType.id\");\r\nmandatory(tw.local.idcRequest.customerInformation.importCardNumber,\"tw.local.idcRequest.customerInformation.importCardNumber\");\r\n\/\/validateTab(0, \"Customer Information\");\r\n\r\n\/\/--------------------------------------basic details[1]----------------------------------------------------\r\n\r\n\/\/mandatory(tw.local.idcRequest.importPurpose.englishdescription , \"tw.local.idcRequest.importPurpose.englishdescription\");\r\n\/\/mandatory(tw.local.idcRequest.paymentTerms.englishdescription,\"tw.local.idcRequest.paymentTerms.englishdescription\");\r\n\/\/mandatory(tw.local.idcRequest.documentsSource.englishdescription,\"tw.local.idcRequest.documentsSource.englishdescription\");\r\n\/\/mandatory(tw.local.idcRequest.productCategory.englishdescription,\"tw.local.idcRequest.productCategory.englishdescription\");\r\n\/\/mandatory(tw.local.idcRequest.commodityDescription,\"tw.local.idcRequest.commodityDescription\");\r\n\/\/if (tw.local.idcRequest.invoices.length&gt;0){\r\n\/\/\tfor (var i=0; i&lt;tw.local.idcRequest.invoices.length; i++) {\r\n\/\/\t\tmandatory(tw.local.idcRequest.invoices[i].number,\"tw.local.idcRequest.invoices[\"+i+\"].number\");\r\n\/\/\t\tmandatory(tw.local.idcRequest.invoices[i].date,\"tw.local.idcRequest.invoices[\"+i+\"].date\");\r\n\/\/\t}\r\n\/\/}\r\n\/\/if(tw.local.idcRequest.billOfLading.length&gt;0){\r\n\/\/\tfor (var i=0; i&lt;tw.local.idcRequest.billOfLading.length; i++) {\r\n\/\/\t\tmandatory(tw.local.idcRequest.billOfLading[i].number,\"tw.local.idcRequest.billOfLading[\"+i+\"].number\");\r\n\/\/\t\tmandatory(tw.local.idcRequest.billOfLading[i].date,\"tw.local.idcRequest.billOfLading[\"+i+\"].date\");\r\n\/\/\t}\r\n\/\/}\r\n\/\/mandatory(tw.local.idcRequest.countryOfOrigin.code,\"tw.local.idcRequest.countryOfOrigin.code\");\r\n\/\/validateTab(1, \"Basic Details\");\r\n\r\n\/\/-------------------------------------financial Details[2]---------------------------------------------------------\r\nmandatory(tw.local.idcRequest.financialDetails.documentAmount,\"tw.local.idcRequest.financialDetails.documentAmount\");\r\n\/\/validateDecimal(tw.local.idcRequest.financialDetails.documentAmount, \"tw.local.idcRequest.financialDetails.documentAmount\", \"max length is 14\" , \"max length is 14\");\r\n\/\/minNumber(tw.local.idcRequest.financialDetails.documentAmount , \"tw.local.idcRequest.financialDetails.documentAmount\" , 0.01 , \"must be more than 0\" , \"must be more than 0\");\r\n\/\/\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.chargesAccount,\"tw.local.idcRequest.financialDetails.chargesAccount\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.paymentAccount,\"tw.local.idcRequest.financialDetails.paymentAccount\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.documentCurrency.code,\"tw.local.idcRequest.financialDetails.documentCurrency.code\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.id,\"tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.id\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.sourceOfFunds.id,\"tw.local.idcRequest.financialDetails.sourceOfFunds.id\");\r\n\/\/validateTab(2,\"Financial Details Branch\");\r\n\r\n\/\/-------------------------------------app info--------------------------------------------------------------\r\n\/\/mandatory(tw.local.selectedAction,\"tw.local.selectedAction\");\r\n\/\/if (tw.local.selectedAction == tw.epv.Action.cancelReques+\"\") {\r\n\/\/\tmandatory(tw.local.idcRequest.stepLog.comment , \"tw.local.idcRequest.stepLog.comment\");\r\n\/\/}"]}},{"targetRef":"2025.533faa47-959b-43f4-b1dd-4360bad3ecc5","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Have Errors","declaredType":"sequenceFlow","id":"2027.70bea525-3044-47fb-af1d-24678fd9aa53","sourceRef":"2025.456c9026-c887-42e9-aa46-c2db3a83a3f6"},{"outgoing":["2027.37c1287e-8454-48d6-8236-610248cf002a","2027.1e0bb62b-d40a-4cb4-8c63-2d4d15c326b6"],"incoming":["2027.70bea525-3044-47fb-af1d-24678fd9aa53"],"default":"2027.37c1287e-8454-48d6-8236-610248cf002a","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":861,"y":14,"declaredType":"TNodeVisualInfo","height":32}],"preAssignmentScript":["if (tw.local.errorExist || (tw.system.coachValidation.validationErrors.length &gt; 0 )) {\r\n\ttw.local.validation = false;\r\n}\r\nelse{\r\n\ttw.local.validation = true;\r\n}"]},"name":"Have Errors","declaredType":"exclusiveGateway","id":"2025.533faa47-959b-43f4-b1dd-4360bad3ecc5"},{"targetRef":"2025.48434093-edd9-4b92-8b57-feca505a198d","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"2027.37c1287e-8454-48d6-8236-610248cf002a","sourceRef":"2025.533faa47-959b-43f4-b1dd-4360bad3ecc5"},{"targetRef":"2025.49ca7e00-8853-4d3f-8d52-119f94ff3e10","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.validation\t  ==\t  false"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"2027.1e0bb62b-d40a-4cb4-8c63-2d4d15c326b6","sourceRef":"2025.533faa47-959b-43f4-b1dd-4360bad3ecc5"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"message","isCollection":false,"declaredType":"dataObject","id":"2056.5487b7be-c459-4495-9b9b-2f847681ad5a"},{"startQuantity":1,"outgoing":["2027.d029fbf9-335e-4245-b721-500bb668b62b"],"incoming":["2027.491dcb01-2d8e-4999-a6f0-8b87817c909a"],"default":"2027.d029fbf9-335e-4245-b721-500bb668b62b","extensionElements":{"nodeVisualInfo":[{"width":95,"x":212,"y":155,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Attachment","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.baab68ba-c57c-48fb-ad2d-8499438e3995","scriptFormat":"text\/x-javascript","script":{"content":["\r\nif (tw.local.attachment == null || tw.local.attachment == undefined) {\r\n\ttw.local.attachment = []; \r\ntw.local.attachment[0] = {};\r\ntw.local.attachment[0].name = \"Customer Request\";\r\ntw.local.attachment[0].description = \"Customer Request\";\r\ntw.local.attachment[0].arabicName = \"\u0637\u0644\u0628 \u0627\u0644\u0639\u0645\u064a\u0644\" ;\r\n\r\ntw.local.attachment[1] = {};\r\ntw.local.attachment[1].name = \"Correspondent cover letter\";\r\ntw.local.attachment[1].description = \"Correspondent cover letter\";\r\ntw.local.attachment[1].arabicName = \"\u062e\u0637\u0627\u0628 \u0627\u0644\u0645\u0631\u0627\u0633\u0644\" ;\r\n\r\ntw.local.attachment[2] = {};\r\ntw.local.attachment[2].name = \"Invoice\";\r\ntw.local.attachment[2].description = \"Invoice\";\r\ntw.local.attachment[2].arabicName = \"\u0641\u0627\u062a\u0648\u0631\u0629\" ;\r\n\r\ntw.local.attachment[3] = {};\r\ntw.local.attachment[3].name = \"Transport document\";\r\ntw.local.attachment[3].description = \"Transport document\";\r\ntw.local.attachment[3].arabicName = \"\u0645\u0633\u062a\u0646\u062f \u0627\u0644\u0646\u0642\u0644\" ;\r\n\r\ntw.local.attachment[4] = {};\r\ntw.local.attachment[4].name = \"Packing list\";\r\ntw.local.attachment[4].description = \"Packing list\";\r\ntw.local.attachment[4].arabicName = \"\u0642\u0627\u0626\u0645\u0629 \u0627\u0644\u062a\u0639\u0628\u0626\u0629\" ;\r\n\r\ntw.local.attachment[5] = {};\r\ntw.local.attachment[5].name = \"Weight list\";\r\ntw.local.attachment[5].description = \"Weight list\"\r\ntw.local.attachment[5].arabicName = \"\u0642\u0627\u0626\u0645\u0629 \u0627\u0644\u0627\u0648\u0632\u0627\u0646\" ;\r\n\r\ntw.local.attachment[6] = {};\r\ntw.local.attachment[6].name = \"Certificate of origin\";\r\ntw.local.attachment[6].description = \"Certificate of origin\";\r\ntw.local.attachment[6].arabicName = \"\u0634\u0647\u0627\u062f\u0629 \u0627\u0644\u0645\u0646\u0634\u0623\" ;\r\n\r\ntw.local.attachment[7] = {};\r\ntw.local.attachment[7].name = \"Certificate of analysis\";\r\ntw.local.attachment[7].description = \"Certificate of analysis\";\r\ntw.local.attachment[7].arabicName = \"\u0634\u0647\u0627\u062f\u0629 \u0627\u0644\u062a\u062d\u0644\u064a\u0644\" ;\r\n\r\ntw.local.attachment[8] = {};\r\ntw.local.attachment[8].name = \"Inspection certificate\";\r\ntw.local.attachment[8].description = \"Inspection certificate\";\r\ntw.local.attachment[8].arabicName = \"\u0634\u0647\u0627\u062f\u0629 \u0627\u0644\u062a\u0641\u062a\u064a\u0634\" ;\r\n\r\ntw.local.attachment[9] = {};\r\ntw.local.attachment[9].name = \"Insurance policy \/ certificate\";\r\ntw.local.attachment[9].description = \"Insurance policy \/ certificate\";\r\ntw.local.attachment[9].arabicName = \"\u0634\u0647\u0627\u062f\u0629 \/ \u0628\u0648\u0644\u064a\u0635\u0629 \u0627\u0644\u062a\u0623\u0645\u064a\u0646\" ;\r\n\r\ntw.local.attachment[10] = {};\r\ntw.local.attachment[10].name = \"Bill of exchange\/draft\";\r\ntw.local.attachment[10].description = \"Bill of exchange\/draft\";\r\ntw.local.attachment[10].arabicName = \"\u0627\u0644\u0643\u0645\u0628\u064a\u0627\u0644\u0629\" ;\r\n\r\ntw.local.attachment[11] = {};\r\ntw.local.attachment[11].name = \"Compliance ticket\";\r\ntw.local.attachment[11].description = \"Compliance ticket\";\r\ntw.local.attachment[11].arabicName = \"\u0645\u0648\u0627\u0641\u0642\u0629 \u0627\u0644\u0625\u0644\u062a\u0632\u0627\u0645\" ;\r\n\r\ntw.local.attachment[12] = {};\r\ntw.local.attachment[12].name = \"Form 4\";\r\ntw.local.attachment[12].description = \"Form 4\";\r\ntw.local.attachment[12].arabicName = \"\u0646\u0645\u0648\u0630\u062c 4 \u0644\u0644\u0645\u0633\u062a\u0648\u0631\u062f\u064a\u0646\" ;\r\n\r\ntw.local.attachment[13] = {};\r\ntw.local.attachment[13].name = \"Customs Letter\";\r\ntw.local.attachment[13].description = \"Customs Letter\";\r\ntw.local.attachment[13].arabicName = \"\u062e\u0637\u0627\u0628 \u0627\u0644\u062c\u0645\u0627\u0631\u0643\" ;\r\n}"]}},{"targetRef":"2025.6745a6d9-029a-4342-833a-dd2d269c35ae","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Coach","declaredType":"sequenceFlow","id":"2027.d029fbf9-335e-4245-b721-500bb668b62b","sourceRef":"2025.baab68ba-c57c-48fb-ad2d-8499438e3995"},{"outgoing":["2027.ff715148-f279-4371-849f-de7043fa1d26"],"incoming":["2027.7c77eb27-c50f-469c-a575-96530c0ae69c"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition"}],"extensionElements":{"default":["2027.ff715148-f279-4371-849f-de7043fa1d26"],"nodeVisualInfo":[{"width":24,"x":701,"y":227,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Postpone ","declaredType":"intermediateThrowEvent","id":"2025.6f1cd112-951d-4cfc-af36-d0c02ea94f18"},{"targetRef":"2025.49ca7e00-8853-4d3f-8d52-119f94ff3e10","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To IDC Request Data","declaredType":"sequenceFlow","id":"2027.ff715148-f279-4371-849f-de7043fa1d26","sourceRef":"2025.6f1cd112-951d-4cfc-af36-d0c02ea94f18"},{"startQuantity":1,"outgoing":["2027.6afcb867-973e-4371-9949-30f99d014018"],"default":"2027.6afcb867-973e-4371-9949-30f99d014018","extensionElements":{"nodeVisualInfo":[{"width":95,"x":76,"y":155,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Initialization Service","dataInputAssociation":[{"targetRef":"2055.cd125a62-2dbd-4b4b-b101-03ec36dbab19","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","declaredType":"TFormalExpression","content":["tw.local.idcRequest"]}}]},{"targetRef":"2055.634377a2-f8f1-4a92-8d1c-99eaae72b7bf","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.action"]}}]},{"targetRef":"2055.23a2f68c-da9d-492b-8b22-d384b35cdca5","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1","declaredType":"TFormalExpression","content":["tw.local.idcContract"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"2025.bf1e6e71-4a30-4575-9747-448f0c391594","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","declaredType":"TFormalExpression","content":["tw.local.idcRequest"]}}],"sourceRef":["2055.4d4ced80-1e82-4190-b046-e12e920ce53c"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.action"]}}],"sourceRef":["2055.2df5550b-b2e8-4f83-8655-1a7230625849"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.cdee38a2-a864-497e-8737-402567eae371"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1","declaredType":"TFormalExpression","content":["tw.local.idcContract"]}}],"sourceRef":["2055.8e43443b-2886-4150-8230-41491f5d9a6b"]}],"calledElement":"1.bd478cbf-52d1-4705-9fe4-6868408a5256"},{"targetRef":"2025.74724f89-c330-4e84-afe5-49cce26e863b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Client-Side Script","declaredType":"sequenceFlow","id":"2027.6afcb867-973e-4371-9949-30f99d014018","sourceRef":"2025.bf1e6e71-4a30-4575-9747-448f0c391594"},{"startQuantity":1,"outgoing":["2027.20df3315-b455-438a-bae4-cf74708c912a"],"incoming":["2027.5b57c95f-3145-4ff1-b121-54b1b660ed84"],"default":"2027.20df3315-b455-438a-bae4-cf74708c912a","extensionElements":{"nodeVisualInfo":[{"color":"#95D087","width":95,"x":486,"y":-117,"declaredType":"TNodeVisualInfo","height":70}]},"name":"validation and set request name","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.5dab97c3-4bdd-4631-a0f6-e602a9c3ba8c","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.idcRequest.appInfo.requestName = tw.local.idcRequest.IDCRequestNature.englishdescription +\" - \"+ tw.local.idcRequest.IDCRequestType.englishdescription\r\nif (tw.local.idcRequest.IDCRequestType.englishdescription == \"Advance Payment\" || tw.local.idcRequest.IDCRequestType.englishdescription == \"IDC Acknowledgement\" || tw.local.idcRequest.IDCRequestType.englishdescription == \"IDC Payment\") {\r\n\ttw.local.advancePaymentsUsedVis = true;\r\n}else{\r\n\ttw.local.advancePaymentsUsedVis = false;\r\n}\r\n\r\ntw.local.message = \"\";\r\nvar mandatoryTriggered = false;\r\nvar tempLength = 0 ;\r\n\/\/tw.local.invalidTabs = [];\r\n\/*\r\n* =========================================================================================================\r\n*  \r\n* Add a coach validation error \r\n* \t\t\r\n* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\n*\r\n* =========================================================================================================\r\n*\/\r\n\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\n{\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\n\tfromMandatory &amp;&amp; mandatoryTriggered ? \"\" : tw.local.message += \"&lt;li dir='rtl'&gt;\" + validationMessage + \"&lt;\/li&gt;\";\r\n}\r\n\r\n\/*\r\n* ==============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is in the past and the last variable to exclude today\r\n*\t\r\n* EX:\tnotPastDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)\r\n*\r\n* =============================================================================================================================\r\n*\/\r\n\r\nfunction notPastDate(date , fieldName , controlMessage , validationMessage , exclude)\r\n{\r\n\tif (exclude)\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &lt;= new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\t\r\n\t}\r\n\telse\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &lt; new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\r\n\t}\r\n}\r\n\r\n\/*\r\n* ==============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is between two dates\r\n*\t\r\n* EX:\tdateBetween(tw.local.date , date1 , date2 , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!')\r\n*\r\n* =============================================================================================================================\r\n*\/\r\n\r\nfunction dateBetween(field , date1 , date2 , fieldName , controlMessage , validationMessage)\r\n{\r\n\tif(field &lt; date1 &amp;&amp; field &gt; date2)\r\n\t{\r\n\t \treturn true;\r\n\t}\r\n\taddError(fieldName , controlMessage , validationMessage);\r\n\treturn false;\r\n}\r\n\r\n\/*\r\n* ===============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is in the future and the last varaible to exculde today\r\n*\t\r\n* EX:\tnotFutureDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)\r\n*\r\n* ===============================================================================================================================\r\n*\/\r\n\r\nfunction notFutureDate(date , fieldName ,  controlMessage , validationMessage , exculde)\r\n{\r\n\tif (exculde)\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &gt;= new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\t\r\n\t}\r\n\telse\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &gt; new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\r\n\t}\r\n}\r\n\r\n\r\n\/*\r\n* =================================================================================================================================\r\n*\r\n* Add a coach validation error if the string length is less than given length\r\n*\t\r\n* EX:\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\r\n*\r\n* =================================================================================================================================\r\n*\/\r\n\r\nfunction minLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the string length is greater than given length\r\n*\t\r\n* EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field != null &amp;&amp; field != undefined &amp;&amp; field.length &gt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the number is greater than given value\r\n*\t\r\n* EX:\tmaxNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction maxNumber(field , fieldName , max , controlMessage , validationMessage)\r\n{\r\n\tif (field &gt; max)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the number is less than given value\r\n*\t\r\n* EX:\tminNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction minNumber(field , fieldName , min , controlMessage , validationMessage)\r\n{\r\n\tif (field &lt; min)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a coach validation error if the field is null 'Mandatory'\r\n*\t\r\n* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\r\nfunction mandatory(field , fieldName)\r\n{\r\n\tif (field == null || field == undefined )\r\n\t{\r\n\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\tmandatoryTriggered = true;\r\n\t\treturn false;\r\n\t}\r\n\telse\r\n\t{\r\n\t\tswitch (typeof field)\r\n\t\t{\r\n\t\t\tcase \"string\":\r\n\t\t\t\tif (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"number\":\r\n\t\t\t\tif (field == 0.0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a validation mark on the tab if there is an error\r\n*\t\r\n* EX:\tvalidateTab(1)\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\r\nfunction validateTab(index)\r\n{\r\n\tif (tw.system.coachValidation.validationErrors.length &gt; tempLength)\r\n\t{\r\n\t\ttempLength = tw.system.coachValidation.validationErrors.length ;\r\n\t\ttw.local.invalidTabs[tw.local.invalidTabs.length] = index;\r\n\t}\t\r\n}\r\n\/\/--------------------------------------------------------------------------------------------------\r\nmandatory(tw.local.idcRequest.IDCRequestNature.id,\"tw.local.idcRequest.IDCRequestNature.id\");\r\nmandatory(tw.local.idcRequest.IDCRequestType.id,\"tw.local.idcRequest.IDCRequestType.id\");\r\nmandatory(tw.local.idcRequest.customerInformation.CIFNumber,\"tw.local.idcRequest.customerInformation.CIFNumber\");\r\nminLength(tw.local.idcRequest.customerInformation.CIFNumber , \"tw.local.idcRequest.customerInformation.CIFNumber\" , 8 , \"CIF can't be empty and must be 8 digits\" , \"CIF can't be empty and must be 8 digits\")\r\nmaxLength(tw.local.idcRequest.customerInformation.CIFNumber , \"tw.local.idcRequest.customerInformation.CIFNumber\" , 8 , \"\" , \"\")\r\nif(!tw.local.retrieveCustomer || tw.local.idcRequest.customerInformation.customerName ==\"\" || tw.local.idcRequest.customerInformation.customerName ==null ){\r\n\r\n\taddError(\"tw.local.idcRequest.customerInformation.customerName\" , \"you must retrieve customer data\" , \"you must retrieve customer data\");\r\n\/\/addError(\"tw.local.retrieveCustomer\" , \"you must retrive customer data\" , \"you must retrive customer data\");\r\n}\r\n\r\nif (tw.local.idcRequest.IDCRequestNature.englishdescription == \"Update Request\") {\r\n\tmandatory(tw.local.idcRequest.ParentIDCRequestNumber,\"tw.local.idcRequest.ParentIDCRequestNumber\");\r\n\tminLength(tw.local.idcRequest.ParentIDCRequestNumber , \"tw.local.idcRequest.ParentIDCRequestNumber\" , 14 , \"Parent IDC Request Number must be 14 digits\" , \"Parent IDC Request Number must be 14 digits\")\r\n\tmaxLength(tw.local.idcRequest.ParentIDCRequestNumber , \"tw.local.idcRequest.ParentIDCRequestNumber\" , 14 , \"\" , \"\")\r\n\tif(!tw.local.retrieveRequest || tw.local.tmpIDCrequest.financialDetails.beneficiaryDetails.name ==\"\" || tw.local.tmpIDCrequest.financialDetails.beneficiaryDetails.name == null){\r\n\t\taddError(\"tw.local.tmpIDCrequest.financialDetails.beneficiaryDetails.name\" , \"you must retrieve parent IDC request data\" , \"you must retrieve parent IDC request data\");\r\n\t\taddError(\"tw.local.tmpIDCrequest.appInfo.requestDate\" , \"you must retrieve parent IDC request data\" , \"you must retrieve parent IDC request data\");\r\n\t}\r\n}\r\n\r\n"]}},{"targetRef":"2025.70bc81e9-24b8-4567-9fa4-00e3ddb3383e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exclusive Gateway","declaredType":"sequenceFlow","id":"2027.20df3315-b455-438a-bae4-cf74708c912a","sourceRef":"2025.5dab97c3-4bdd-4631-a0f6-e602a9c3ba8c"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"action","isCollection":true,"declaredType":"dataObject","id":"2056.74e58948-7016-4ea7-837f-9c0a9564659c"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"selectedAction","isCollection":false,"declaredType":"dataObject","id":"2056.af984f2d-e341-4e5d-b5c4-a1d7d94df897"},{"startQuantity":1,"outgoing":["2027.1ad2c476-2429-4849-b48d-e8dc071b0173"],"incoming":["2027.6afcb867-973e-4371-9949-30f99d014018"],"default":"2027.1ad2c476-2429-4849-b48d-e8dc071b0173","extensionElements":{"nodeVisualInfo":[{"width":95,"x":211,"y":-56,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Initialization Script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.74724f89-c330-4e84-afe5-49cce26e863b","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.idcRequest.stepLog = {};\r\ntw.local.idcRequest.stepLog.startTime = new Date();\r\n\r\ntw.local.advancePaymentsUsedVis=true;\r\nif (tw.local.ECMproperties==null || tw.local.ECMproperties==undefined) {\r\n\ttw.local.ECMproperties= {};\r\n\r\n}\r\n\r\ntw.local.errorVIS = \"NONE\";\r\n\r\ntw.local.action = [];\r\n\r\ntw.local.action[0] = tw.epv.Action.submitRequest+\"\";\r\ntw.local.action[1] = tw.epv.Action.cancelReques+\"\";\r\nif (tw.local.idcRoutingDetails.hubCode != null &amp;&amp; tw.local.idcRoutingDetails.hubCode != \"\" &amp;&amp; tw.local.idcRoutingDetails.hubCode != undefined) {\r\n\t\r\n\ttw.local.idcRequest.appInfo.branch.name = tw.local.idcRoutingDetails.hubName;\r\n\ttw.local.idcRequest.appInfo.branch.value = tw.local.idcRoutingDetails.hubCode;\r\n\ttw.local.role = \"Hub Maker\";\r\n}else{\r\n\ttw.local.role = \"Branch Maker\"\r\n\ttw.local.idcRequest.appInfo.branch.name = tw.local.idcRoutingDetails.branchName;\r\n\ttw.local.idcRequest.appInfo.branch.value = tw.local.idcRoutingDetails.branchSeq;\r\n}\r\n\r\n"]}},{"targetRef":"2025.ce3318bc-ad0a-4809-b3e5-8d0a8f3f6929","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set Step Name","declaredType":"sequenceFlow","id":"2027.1ad2c476-2429-4849-b48d-e8dc071b0173","sourceRef":"2025.74724f89-c330-4e84-afe5-49cce26e863b"},{"outgoing":["2027.1bd1cf32-7c29-467c-a632-d8e6f8aa6643","2027.09f07a5e-4dcf-4ede-a465-3f7689a9bb0f"],"incoming":["2027.20df3315-b455-438a-bae4-cf74708c912a"],"default":"2027.1bd1cf32-7c29-467c-a632-d8e6f8aa6643","gatewayDirection":"Unspecified","extensionElements":{"postAssignmentScript":["tw.local.idcRequest.appInfo.status = \"New Request\";\r\ntw.local.idcRequest.appInfo.subStatus = \"New Request\";"],"nodeVisualInfo":[{"width":32,"x":508,"y":-3,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Exclusive Gateway","declaredType":"exclusiveGateway","id":"2025.70bc81e9-24b8-4567-9fa4-00e3ddb3383e"},{"targetRef":"2025.c9bece30-ee23-455f-a1f9-fdbc3274182b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get Request Number ,CBE And ECM Folder","declaredType":"sequenceFlow","id":"2027.1bd1cf32-7c29-467c-a632-d8e6f8aa6643","sourceRef":"2025.70bc81e9-24b8-4567-9fa4-00e3ddb3383e"},{"targetRef":"2025.6d76e0dd-17bb-4e8d-98d1-82e8d0682c86","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.system.coachValidation.validationErrors.length\t  &gt;\t  0"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Create IDC Request","declaredType":"sequenceFlow","id":"2027.09f07a5e-4dcf-4ede-a465-3f7689a9bb0f","sourceRef":"2025.70bc81e9-24b8-4567-9fa4-00e3ddb3383e"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"false"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"retrieveRequest","isCollection":false,"declaredType":"dataObject","id":"2056.d3da5538-d691-497a-b5ad-e3a8951ba8b5"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"false"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"retrieveCustomer","isCollection":false,"declaredType":"dataObject","id":"2056.6f519687-e7ad-4934-baf4-8351a36aff47"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"advancePaymentsUsedVis","isCollection":false,"declaredType":"dataObject","id":"2056.ab1441ac-969b-44c3-9838-bef5dca3e81f"},{"outgoing":["2027.a2702278-c88d-4b12-ab10-f056ce0ae66a"],"incoming":["2027.1bd1cf32-7c29-467c-a632-d8e6f8aa6643"],"extensionElements":{"nodeVisualInfo":[{"color":"#A5B7CD","width":95,"x":492,"y":90,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"declaredType":"callActivity","startQuantity":1,"default":"2027.a2702278-c88d-4b12-ab10-f056ce0ae66a","name":"Get Request Number ,CBE And ECM Folder","dataInputAssociation":[{"targetRef":"2055.495b518d-03ae-4920-82cf-eeb04812beb2","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.idcRequest.customerInformation.CIFNumber"]}}]},{"targetRef":"2055.87745665-8435-4ac5-85f1-420de9e01cd0","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.idcRequest.appInfo.branch.value"]}}]},{"targetRef":"2055.e3b36fc9-0cb9-453d-8275-14fa1f734703","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression"}}]},{"targetRef":"2055.9eabaff4-3e88-47e4-bbbe-5cc34cf37cb2","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","declaredType":"TFormalExpression","content":["tw.local.idcRequest"]}}]},{"targetRef":"2055.6e39d6c4-152e-4681-9ccb-b3d2ea326441","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","declaredType":"TFormalExpression","content":["tw.local.tmpIDCrequest"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.c9bece30-ee23-455f-a1f9-fdbc3274182b","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.idcRequest.customerInformation.isCustomeSanctionedbyCBE"]}}],"sourceRef":["2055.f4a7754b-fcbe-4f1f-aaaf-65abfffad2aa"]},{"assignment":[{"to":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.idcRequest.appInfo.instanceID"]}}],"sourceRef":["2055.d22b4a18-865c-4b84-b879-26ee9e3f322f"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.folderID"]}}],"sourceRef":["2055.77c3cc2f-ce0f-473c-8551-a2f1093abb8f"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentPath"]}}],"sourceRef":["2055.31e2d85c-18d8-4756-82c5-efad7879177e"]},{"assignment":[{"to":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.ECMproperties.fullPath"]}}],"sourceRef":["2055.b304617e-98f3-4070-b457-59e470497a2f"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","declaredType":"TFormalExpression","content":["tw.local.Accounts"]}}],"sourceRef":["2055.3027fb96-9129-4507-8aa8-f177c64f509b"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.4ae2afa0-cdd1-4d71-869e-7ea34e15f8aa"]}],"calledElement":"1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13"},{"targetRef":"2025.896d855b-8f91-4a12-befb-45fa77355eb8","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To IDC Request Data","declaredType":"sequenceFlow","id":"2027.a2702278-c88d-4b12-ab10-f056ce0ae66a","sourceRef":"2025.c9bece30-ee23-455f-a1f9-fdbc3274182b"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"no\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"havePaymentTerms","isCollection":false,"declaredType":"dataObject","id":"2056.eaf2f7a4-527e-4089-8c56-23bd9239dd5b"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"false"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"haseApprovals","isCollection":false,"declaredType":"dataObject","id":"2056.64e76842-552e-421e-8aa0-7210a82b8744"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"false"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"hasReturnReason","isCollection":false,"declaredType":"dataObject","id":"2056.1bae6db0-3223-425c-b9c5-e4a94981cf78"},{"startQuantity":1,"outgoing":["2027.9cecf069-47c9-41c6-9247-cc005b368801"],"incoming":["2027.a2702278-c88d-4b12-ab10-f056ce0ae66a"],"default":"2027.9cecf069-47c9-41c6-9247-cc005b368801","extensionElements":{"nodeVisualInfo":[{"width":95,"x":492,"y":200,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set State and Stage","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.896d855b-8f91-4a12-befb-45fa77355eb8","scriptFormat":"text\/x-javascript","script":{"content":["if ((tw.local.idcRoutingDetails.hubCode != null &amp;&amp; tw.local.idcRoutingDetails.hubCode != \"\" &amp;&amp; tw.local.idcRoutingDetails.hubCode != undefined) &amp;&amp; (tw.local.idcRequest.IDCRequestType.englishdescription == \"IDC Amendment\" || tw.local.idcRequest.IDCRequestType.englishdescription == \"IDC Payment\") ) {\r\n\ttw.local.action[2] = tw.epv.Action.submitRequestToHubDirectly+\"\";\r\n\r\n}\r\nif (tw.local.idcRequest.IDCRequestType.englishdescription == \"IDC Acknowledgement\") {\r\n\ttw.local.idcRequest.IDCRequestStage = tw.epv.IDCstage.initial;\r\n\ttw.local.idcRequest.IDCRequestState = tw.epv.IDCState.draft;\r\n} else {\r\n\ttw.local.idcRequest.IDCRequestStage = tw.epv.IDCstage.fiinal;\r\n\ttw.local.idcRequest.IDCRequestState = tw.epv.IDCState.draft;\r\n}\r\n\r\n\r\ntw.local.ECMproperties.fullPath = tw.env.FILENET_ROOT_PATH +\"\/\"+ tw.local.ECMproperties.fullPath;\r\ntw.local.default_properties = [];\r\ntw.local.default_properties[0]={};\r\ntw.local.default_properties[0].editable = false;\r\ntw.local.default_properties[0].hidden = false;\r\ntw.local.default_properties[0].name = \"Customer_Number_CIF\";\r\ntw.local.default_properties[0].value = tw.local.idcRequest.customerInformation.CIFNumber;\r\ntw.local.default_properties[1]={};\r\ntw.local.default_properties[1].editable = false;\r\ntw.local.default_properties[1].hidden = false;\r\ntw.local.default_properties[1].name = \"BPMInstanceId\";\r\ntw.local.default_properties[1].value = tw.local.idcRequest.appInfo.appID;\r\ntw.local.default_properties[2]={};\r\ntw.local.default_properties[2].editable = false;\r\ntw.local.default_properties[2].hidden = false;\r\ntw.local.default_properties[2].name = \"RequestReferenceNumber\";\r\ntw.local.default_properties[2].value = tw.local.idcRequest.appInfo.instanceID;\r\ntw.local.default_properties[3]={};\r\ntw.local.default_properties[3].editable = false;\r\ntw.local.default_properties[3].hidden = true;\r\ntw.local.default_properties[3].name = \"DocumentType\";\r\ntw.local.default_properties[3].value = \"\";\r\n\r\ntw.local.default_properties[4]={};\r\ntw.local.default_properties[4].editable = false;\r\ntw.local.default_properties[4].hidden = true;\r\ntw.local.default_properties[4].name = \"DocumentTitle\";\r\ntw.local.default_properties[4].value = \"\";\r\n\r\ntw.local.default_properties[5]={};\r\ntw.local.default_properties[5].editable = false;\r\ntw.local.default_properties[5].hidden = true;\r\ntw.local.default_properties[5].name = \"RecordInformation\";\r\ntw.local.default_properties[5].value = \"\";\r\n\r\ntw.local.default_properties[6]={};\r\ntw.local.default_properties[6].editable = false;\r\ntw.local.default_properties[6].hidden = true;\r\ntw.local.default_properties[6].name = \"CmFederatedLockStatus\";\r\ntw.local.default_properties[6].value = \"\";\r\n\r\n\r\n\r\n\/\/tw.local.ECMproperties.cmisQuery = \"SELECT * FROM IDCDocument WHERE IN_TREE('\"+tw.local.ECMproperties.fullPath+\"')\";\r\ntw.local.ECMproperties.defaultProperties = tw.local.default_properties;\r\n"]}},{"targetRef":"2025.49ca7e00-8853-4d3f-8d52-119f94ff3e10","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To IDC Request Data","declaredType":"sequenceFlow","id":"2027.9cecf069-47c9-41c6-9247-cc005b368801","sourceRef":"2025.896d855b-8f91-4a12-befb-45fa77355eb8"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"accountNumber","isCollection":true,"declaredType":"dataObject","id":"2056.bbb188e8-3cfc-495a-b29e-c6a152b21295"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"exist","isCollection":true,"declaredType":"dataObject","id":"2056.7881b472-e7ab-4ee6-8e33-39e9338d4c35"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"errorExist","isCollection":false,"declaredType":"dataObject","id":"2056.fbce89f3-dbca-44b8-bab6-96b02e317dc9"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.88ea9ce0-bc9e-46c3-94ee-1cec1bbaca80"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"validation","isCollection":false,"declaredType":"dataObject","id":"2056.bd63dfb5-84a3-4d46-9896-559942870dc1"},{"itemSubjectRef":"itm.12.eac829db-66fe-43f5-810c-6faa514533a2","name":"default_properties","isCollection":true,"declaredType":"dataObject","id":"2056.460773e0-c5ba-4752-aeb7-072f2e8f5748"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"cmisQuery","isCollection":false,"declaredType":"dataObject","id":"2056.1d26dfd7-611d-4088-abc8-66887fe7511d"},{"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"documentsTypesSelected","isCollection":true,"declaredType":"dataObject","id":"2056.acae7137-6267-4e31-bc9a-e8c6bc42e93b"},{"startQuantity":1,"outgoing":["2027.4aa6a2cc-3485-4d95-8093-0e7dd7afac2a"],"default":"2027.4aa6a2cc-3485-4d95-8093-0e7dd7afac2a","extensionElements":{"postAssignmentScript":["if (tw.local.errorExist) {\r\n\talert(tw.local.errorMessage);\r\n}"],"nodeVisualInfo":[{"color":"#95D087","width":95,"x":739,"y":-135,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["tw.local.documentsTypesSelected = tw.local.attachment.listAllSelected;"]},"name":"Get Required Documents","dataInputAssociation":[{"targetRef":"2055.a4a1317a-bc51-4643-b432-1d1c7cbaba1c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.idcRequest.documentsSource.englishdescription"]}}]},{"targetRef":"2055.aae56053-3bba-40b1-abc9-6a441a93f307","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.idcRequest.IDCRequestType.englishdescription"]}}]},{"targetRef":"2055.cad5cf23-0f7f-41ba-b9eb-34cab62cb7f8","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","declaredType":"TFormalExpression","content":["tw.local.documentsTypesSelected"]}}]},{"targetRef":"2055.8e6a0739-a7ac-4f62-b871-969d0ca951ef","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.folderID"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"2025.de4e528f-4cdb-425d-8c24-2def6866abf4","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.1e09e24d-eb4a-4474-96f7-d5075b4fc749"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.errorExist"]}}],"sourceRef":["2055.3c7c7a8a-43f7-4745-b05a-3c52b99703d6"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.74cb9e94-45c3-4ba9-8862-d45286d425b6"]}],"calledElement":"1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2"},{"targetRef":"2025.456c9026-c887-42e9-aa46-c2db3a83a3f6","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Check Customer Accounts","declaredType":"sequenceFlow","id":"2027.4aa6a2cc-3485-4d95-8093-0e7dd7afac2a","sourceRef":"2025.de4e528f-4cdb-425d-8c24-2def6866abf4"},{"outgoing":["2027.8303ffc2-34a7-4d7f-8bd7-b43829b1c16d","2027.739a0624-71ef-4e60-8c99-f33d23dd579d"],"incoming":["2027.d029fbf9-335e-4245-b721-500bb668b62b"],"default":"2027.8303ffc2-34a7-4d7f-8bd7-b43829b1c16d","gatewayDirection":"Unspecified","extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":32,"x":259,"y":275,"declaredType":"TNodeVisualInfo","height":32}],"preAssignmentScript":[]},"name":"Exclusive Gateway 1","declaredType":"exclusiveGateway","id":"2025.6745a6d9-029a-4342-833a-dd2d269c35ae"},{"targetRef":"2025.6d76e0dd-17bb-4e8d-98d1-82e8d0682c86","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Create IDC Request","declaredType":"sequenceFlow","id":"2027.8303ffc2-34a7-4d7f-8bd7-b43829b1c16d","sourceRef":"2025.6745a6d9-029a-4342-833a-dd2d269c35ae"},{"targetRef":"2025.********-78b9-46e1-88b9-048aac7d8178","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.idcRequest.appInfo.subStatus\t  ==\t  tw.epv.IDCsubStatus.returnedtoInitiator"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Client-Side Script","declaredType":"sequenceFlow","id":"2027.739a0624-71ef-4e60-8c99-f33d23dd579d","sourceRef":"2025.6745a6d9-029a-4342-833a-dd2d269c35ae"},{"itemSubjectRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","name":"accountsList","isCollection":true,"declaredType":"dataObject","id":"2056.0a63ba65-76ed-4878-8c1a-16415bfd58e2"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"role","isCollection":false,"declaredType":"dataObject","id":"2056.25f650ed-491e-4700-81bf-82ba316c9a93"},{"startQuantity":1,"outgoing":["2027.0fd60f96-4c02-43df-831d-0328a3497a99"],"incoming":["2027.739a0624-71ef-4e60-8c99-f33d23dd579d"],"default":"2027.0fd60f96-4c02-43df-831d-0328a3497a99","extensionElements":{"nodeVisualInfo":[{"width":95,"x":490,"y":292,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Client-Side Script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.********-78b9-46e1-88b9-048aac7d8178","scriptFormat":"text\/x-javascript","script":{"content":["if (tw.local.idcRoutingDetails.hubCode != null &amp;&amp; tw.local.idcRoutingDetails.hubCode != \"\" &amp;&amp; tw.local.idcRoutingDetails.hubCode != undefined) {\r\n\tif (tw.local.idcRequest.IDCRequestType.englishdescription != \"IDC Amendment\" &amp;&amp; tw.local.idcRequest.IDCRequestType.englishdescription != \"IDC Payment\" ) {\r\n\t\ttw.local.action.pop();\r\n\t}\r\n}"]}},{"targetRef":"2025.49ca7e00-8853-4d3f-8d52-119f94ff3e10","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To IDC Request Data","declaredType":"sequenceFlow","id":"2027.0fd60f96-4c02-43df-831d-0328a3497a99","sourceRef":"2025.********-78b9-46e1-88b9-048aac7d8178"},{"itemSubjectRef":"itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67","name":"tmpUsedAdvancePayment","isCollection":false,"declaredType":"dataObject","id":"2056.1bf58584-0f2a-4931-835d-b9ef5bbf376f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"validationMessage","isCollection":false,"declaredType":"dataObject","id":"2056.a73650fd-39fa-4ce8-8a8c-f608030db340"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"invalidTabs","isCollection":true,"declaredType":"dataObject","id":"2056.63b05af4-**************-a37d4e052126"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"DEFAULT\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"currencyVis","isCollection":false,"declaredType":"dataObject","id":"2056.da608b97-9e34-4771-873c-5246044a4dfa"},{"parallelMultiple":false,"outgoing":["2027.4bcbadc8-b130-420d-8602-6d990aaa271b"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2025.aeee0fe8-60f6-40a1-851d-76d61b23c7bd"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition"}],"attachedToRef":"2025.bf1e6e71-4a30-4575-9747-448f0c391594","extensionElements":{"default":["2027.4bcbadc8-b130-420d-8602-6d990aaa271b"],"nodeVisualInfo":[{"width":24,"x":111,"y":143,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"2025.f754aed6-2a32-4ce9-845b-32329ea22ee1","outputSet":{}},{"parallelMultiple":false,"outgoing":["2027.ccf1f906-2fbb-4596-82f3-8dc809fe8fe0"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2025.7157d29f-c30d-4260-8b00-43b209f7f743"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition"}],"attachedToRef":"2025.c9bece30-ee23-455f-a1f9-fdbc3274182b","extensionElements":{"default":["2027.ccf1f906-2fbb-4596-82f3-8dc809fe8fe0"],"nodeVisualInfo":[{"width":24,"x":575,"y":95,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error 1","declaredType":"boundaryEvent","id":"2025.bcb0053f-0732-424e-8cd9-b40e826fea73","outputSet":{}},{"parallelMultiple":false,"outgoing":["2027.afed332a-cc39-49e6-8c72-923fb2b5fa05"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2025.0afdc17b-6546-429a-8f94-c120ae169e69"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition"}],"attachedToRef":"2025.de4e528f-4cdb-425d-8c24-2def6866abf4","extensionElements":{"default":["2027.afed332a-cc39-49e6-8c72-923fb2b5fa05"],"nodeVisualInfo":[{"width":24,"x":774,"y":-147,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error 2","declaredType":"boundaryEvent","id":"2025.178c4832-15e6-4cce-85c2-2f2f22c02145","outputSet":{}},{"startQuantity":1,"outgoing":["2027.567761a7-71b4-4fc0-826a-480bf1bdd744"],"incoming":["2027.b9c5230e-dcb1-410c-8d81-02113271f906","2027.afed332a-cc39-49e6-8c72-923fb2b5fa05","2027.ccf1f906-2fbb-4596-82f3-8dc809fe8fe0"],"default":"2027.567761a7-71b4-4fc0-826a-480bf1bdd744","extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":487,"y":-225,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Handling Error","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.ceb3afb5-605d-4ab2-898c-d02e0044a9b3","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.errorMSG = String(tw.error.data);\r\n\r\ntw.local.errorVIS = \"EDITABLE\";\r\n\/\/alert(tw.local.errorMSG);"]}},{"targetRef":"2025.ceb3afb5-605d-4ab2-898c-d02e0044a9b3","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Handling Error","declaredType":"sequenceFlow","id":"2027.afed332a-cc39-49e6-8c72-923fb2b5fa05","sourceRef":"2025.178c4832-15e6-4cce-85c2-2f2f22c02145"},{"startQuantity":1,"outgoing":["2027.ce0e6f47-7011-472e-89da-87ef254aa439"],"incoming":["2027.4bcbadc8-b130-420d-8602-6d990aaa271b"],"default":"2027.ce0e6f47-7011-472e-89da-87ef254aa439","extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":220,"y":-185,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Handling Error2","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.f5c8d6bf-5a19-4259-8c7b-39c71f5c7c12","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.errorMSG = String(tw.error.data);\r\ntw.local.errorVIS = \"EDITABLE\";\r\n\/\/alert(tw.local.errorMSG);"]}},{"targetRef":"2025.f5c8d6bf-5a19-4259-8c7b-39c71f5c7c12","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Handling Error2","declaredType":"sequenceFlow","id":"2027.4bcbadc8-b130-420d-8602-6d990aaa271b","sourceRef":"2025.f754aed6-2a32-4ce9-845b-32329ea22ee1"},{"incoming":["2027.ce0e6f47-7011-472e-89da-87ef254aa439"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":230,"y":-238,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page","declaredType":"intermediateThrowEvent","id":"2025.402cc521-ef49-4ce0-8f6a-578039314372"},{"targetRef":"2025.402cc521-ef49-4ce0-8f6a-578039314372","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Stay on page","declaredType":"sequenceFlow","id":"2027.ce0e6f47-7011-472e-89da-87ef254aa439","sourceRef":"2025.f5c8d6bf-5a19-4259-8c7b-39c71f5c7c12"},{"targetRef":"2025.ceb3afb5-605d-4ab2-898c-d02e0044a9b3","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightBottom","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false,"customBendPoint":[{"x":617,"y":-108}]}]},"name":"To Handling Error","declaredType":"sequenceFlow","id":"2027.ccf1f906-2fbb-4596-82f3-8dc809fe8fe0","sourceRef":"2025.bcb0053f-0732-424e-8cd9-b40e826fea73"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.c4827fb2-0617-41f5-880c-2a35a1d2c2ff"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorVIS","isCollection":false,"declaredType":"dataObject","id":"2056.779bfb1d-d3ca-4074-86ca-cd45a72f60e4"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"alertMessage","isCollection":false,"declaredType":"dataObject","id":"2056.88008abd-7153-488a-8b25-60048fe80e0b"},{"outgoing":["2027.f7d79e2e-d4f5-4ec4-8194-c9455b758a1c"],"incoming":["2027.d4c507ee-6622-4a63-86c2-2d8c273474d5"],"extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":95,"x":1097,"y":100,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"declaredType":"callActivity","startQuantity":1,"default":"2027.f7d79e2e-d4f5-4ec4-8194-c9455b758a1c","name":"Database Integration","dataInputAssociation":[{"targetRef":"2055.79f352fc-0629-430a-87cd-5b10dbdc4454","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["false"]}}]},{"targetRef":"2055.4144ba7c-0a79-4853-8fd2-6aa9481cd0bb","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","declaredType":"TFormalExpression","content":["tw.local.idcRequest"]}}]},{"targetRef":"2055.3fafd6ca-c323-45ea-8653-0015f902d198","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.selectedAction"]}}]},{"targetRef":"2055.a7f76cb6-349a-4e05-84c4-a0307bd1074b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.role"]}}]},{"targetRef":"2055.d3b53e19-8f75-427c-8e43-fe25518ef721","assignment":[{"from":{"evaluatesToTypeRef":"12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.idcRequest.appLog"]}}]},{"targetRef":"2055.96de5ca4-16df-4f86-8d6d-dc1ae75db0f9","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1","declaredType":"TFormalExpression","content":["tw.local.idcContract"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.0c9a7cd3-c644-472f-8c23-976def594caa","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","declaredType":"TFormalExpression","content":["tw.local.idcRequest"]}}],"sourceRef":["2055.9ad81a8c-1b47-4f45-81b1-c0c0120c97d1"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.e92b92ce-41f1-49d7-82cf-acf4e366b0e0"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.idcRequest.appLog"]}}],"sourceRef":["2055.b08a2cbe-96ec-4d2d-8efe-ec915a097b44"]}],"calledElement":"1.9f0a859b-5010-4ab6-947a-81ad99803cf1"},{"targetRef":"ced35e6d-1321-4fd5-84dc-fc0e81d91c45","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.f7d79e2e-d4f5-4ec4-8194-c9455b758a1c","sourceRef":"2025.0c9a7cd3-c644-472f-8c23-976def594caa"},{"incoming":["2027.567761a7-71b4-4fc0-826a-480bf1bdd744"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":390,"y":-201,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page 1","declaredType":"intermediateThrowEvent","id":"2025.22d2d42d-9cfe-4daa-8ab6-a8698af5f739"},{"targetRef":"2025.22d2d42d-9cfe-4daa-8ab6-a8698af5f739","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Stay on page 1","declaredType":"sequenceFlow","id":"2027.567761a7-71b4-4fc0-826a-480bf1bdd744","sourceRef":"2025.ceb3afb5-605d-4ab2-898c-d02e0044a9b3"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.errorText = \"\";\nautoObject.errorCode = \"\";\nautoObject.serviceInError = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"declaredType":"dataObject","id":"2056.287a216f-5a65-40e7-88eb-9db5fcf7201c"},{"parallelMultiple":false,"outgoing":["2027.b9c5230e-dcb1-410c-8d81-02113271f906"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2025.d6cc900a-72df-4c20-8bc2-f1024e90270e"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition"}],"attachedToRef":"2025.0c9a7cd3-c644-472f-8c23-976def594caa","extensionElements":{"default":["2027.b9c5230e-dcb1-410c-8d81-02113271f906"],"nodeVisualInfo":[{"width":24,"x":1132,"y":88,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error 3","declaredType":"boundaryEvent","id":"2025.b511a75b-f4b1-4cc4-8784-1c4cb412944e","outputSet":{}},{"targetRef":"2025.ceb3afb5-605d-4ab2-898c-d02e0044a9b3","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Handling Error","declaredType":"sequenceFlow","id":"2027.b9c5230e-dcb1-410c-8d81-02113271f906","sourceRef":"2025.b511a75b-f4b1-4cc4-8784-1c4cb412944e"},{"itemSubjectRef":"itm.12.e9f65280-afe9-44dc-9616-f95c2a14629e","name":"InterestAndChargesList","isCollection":true,"declaredType":"dataObject","id":"2056.f9281cb6-b1ec-4a1a-828d-253437b3aaa9"},{"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"exRate","isCollection":false,"declaredType":"dataObject","id":"2056.320557b4-d664-425e-87a3-ce20cbf518ab"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"adviceCodeList","isCollection":true,"declaredType":"dataObject","id":"2056.63336b9a-7a4a-4480-8a36-7b06021879c4"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"7938c5b4-8a9c-4382-9b07-4173d4ff0f41","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"d85d6e76-b1c5-4700-b87d-ce4d6728aa18","processType":"None"}],"exposedAs":["NotExposed"]},"implementation":"##unspecified","documentation":[{"textFormat":"text\/plain"}],"name":"Create IDC Request","declaredType":"globalUserTask","id":"1.19d05118-4f9d-482a-afd5-662663bc3612","ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.feda83ff-2ac0-4dbb-8941-6c929d776d96"},{"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"attachment","isCollection":true,"id":"2055.f616fc43-d293-443a-afb4-37d7d3abd8b7"},{"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"folderID","isCollection":false,"id":"2055.02e6c09f-afcc-4424-92d7-0e28498532af"},{"itemSubjectRef":"itm.12.ae22157e-8e19-4a60-a294-712ff5dc96df","name":"ECMproperties","isCollection":false,"id":"2055.6c128f72-716e-4f76-b73f-dad75e5d3156"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentPath","isCollection":false,"id":"2055.51acf25f-3a3a-4f2c-9681-d2fb03b38a02"},{"itemSubjectRef":"itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1","name":"idcContract","isCollection":false,"id":"2055.f0530dfd-3e46-4485-8c28-93a4424136c0"}],"extensionElements":{"localizationResourceLinks":[{"resourceRef":[{"resourceBundleGroupID":"50.95ec9bf9-2d5a-4fc5-8dd1-e6d6a55a836c","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef","id":"69.8e814082-d8dc-4953-8463-29a46de80662"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks"}],"envProcessLinks":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEnvProcessLinks","envProcessLinkRef":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEnvProcessLinkRef","envProcessLinkId":"5fba7752-ac23-4861-8c76-41df40906fe1","envId":"2094.efef6f5f-c429-4864-8545-77dd664c9ace"}]}],"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.02818ba4-c183-4dfb-8924-18e2d9a515dd","epvProcessLinkId":"30a47594-8e29-494e-8a61-f9890c2e1ff0","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e","epvProcessLinkId":"378669a8-b9bf-4e53-8146-661e37f7dc1e","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.8ce8b34e-54bb-4623-a4c9-ab892efacac6","epvProcessLinkId":"24962ef8-47a2-4543-84d1-7e2ff9eadd95","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.e5829eee-0ab1-4f47-9191-f0f8705bc33e","epvProcessLinkId":"0af371a1-eb34-4b19-8aba-b3f6b5f31688","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.0bb89e09-258a-4bd1-b3a7-137a3219209f","epvProcessLinkId":"d31cff85-497c-4803-83be-19d3d7abfdda","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.IDCRequestState = \"\";\nautoObject.commodityDescription = \"\";\nautoObject.countryOfOrigin = {};\nautoObject.countryOfOrigin.id = 0;\nautoObject.countryOfOrigin.code = \"\";\nautoObject.countryOfOrigin.arabicdescription = \"\";\nautoObject.countryOfOrigin.englishdescription = \"\";\nautoObject.appInfo = {};\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = {};\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.productsDetails = {};\nautoObject.productsDetails.destinationPort = \"\";\nautoObject.productsDetails.shippingDate = new Date();\nautoObject.productsDetails.HSProduct = {};\nautoObject.productsDetails.HSProduct.id = 0;\nautoObject.productsDetails.HSProduct.code = \"\";\nautoObject.productsDetails.HSProduct.arabicdescription = \"\";\nautoObject.productsDetails.HSProduct.englishdescription = \"\";\nautoObject.productsDetails.incoterms = {};\nautoObject.productsDetails.incoterms.id = 0;\nautoObject.productsDetails.incoterms.code = \"\";\nautoObject.productsDetails.incoterms.arabicdescription = \"\";\nautoObject.productsDetails.incoterms.englishdescription = \"\";\nautoObject.productsDetails.ACID = \"\";\nautoObject.productsDetails.CBECommodityClassification = {};\nautoObject.productsDetails.CBECommodityClassification.id = 0;\nautoObject.productsDetails.CBECommodityClassification.code = \"\";\nautoObject.productsDetails.CBECommodityClassification.arabicdescription = \"\";\nautoObject.productsDetails.CBECommodityClassification.englishdescription = \"\";\nautoObject.productsDetails.shipmentMethod = {};\nautoObject.productsDetails.shipmentMethod.id = 0;\nautoObject.productsDetails.shipmentMethod.code = \"\";\nautoObject.productsDetails.shipmentMethod.arabicdescription = \"\";\nautoObject.productsDetails.shipmentMethod.englishdescription = \"\";\nautoObject.financialDetails = {};\nautoObject.financialDetails.isAdvancePaymentsUsed = false;\nautoObject.financialDetails.paymentTerms = [];\nautoObject.financialDetails.paymentTerms[0] = {};\nautoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;\nautoObject.financialDetails.paymentTerms[0].installmentDate = new Date();\nautoObject.financialDetails.usedAdvancePayment = [];\nautoObject.financialDetails.usedAdvancePayment[0] = {};\nautoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = {};\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;\nautoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = \"\";\nautoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;\nautoObject.financialDetails.discountAmt = 0.0;\nautoObject.financialDetails.firstInstallementMaturityDate = new Date();\nautoObject.financialDetails.facilityAmtInDocCurrency = 0.0;\nautoObject.financialDetails.amtSight = 0.0;\nautoObject.financialDetails.beneficiaryDetails = {};\nautoObject.financialDetails.beneficiaryDetails.account = \"\";\nautoObject.financialDetails.beneficiaryDetails.bank = \"\";\nautoObject.financialDetails.beneficiaryDetails.correspondentRefNum = \"\";\nautoObject.financialDetails.beneficiaryDetails.name = \"\";\nautoObject.financialDetails.beneficiaryDetails.country = {};\nautoObject.financialDetails.beneficiaryDetails.country.id = 0;\nautoObject.financialDetails.beneficiaryDetails.country.code = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.arabicdescription = \"\";\nautoObject.financialDetails.beneficiaryDetails.country.englishdescription = \"\";\nautoObject.financialDetails.amtDeferredNoAvalized = 0.0;\nautoObject.financialDetails.amtPayableByNBE = 0.0;\nautoObject.financialDetails.executionHub = {};\nautoObject.financialDetails.executionHub.id = 0;\nautoObject.financialDetails.executionHub.code = \"\";\nautoObject.financialDetails.executionHub.arabicdescription = \"\";\nautoObject.financialDetails.executionHub.englishdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency = {};\nautoObject.financialDetails.sourceOfForeignCurrency.id = 0;\nautoObject.financialDetails.sourceOfForeignCurrency.code = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfForeignCurrency.englishdescription = \"\";\nautoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.documentCurrency = {};\nautoObject.financialDetails.documentCurrency.id = 0;\nautoObject.financialDetails.documentCurrency.code = \"\";\nautoObject.financialDetails.documentCurrency.arabicdescription = \"\";\nautoObject.financialDetails.documentCurrency.englishdescription = \"\";\nautoObject.financialDetails.daysTillMaturity = 0;\nautoObject.financialDetails.tradeFinanceApprovalNumber = \"\";\nautoObject.financialDetails.amountAdvanced = 0.0;\nautoObject.financialDetails.paymentAccount = \"\";\nautoObject.financialDetails.tradeFOReferenceNumber = \"\";\nautoObject.financialDetails.documentAmount = 0.0;\nautoObject.financialDetails.sourceOfFunds = {};\nautoObject.financialDetails.sourceOfFunds.id = 0;\nautoObject.financialDetails.sourceOfFunds.code = \"\";\nautoObject.financialDetails.sourceOfFunds.arabicdescription = \"\";\nautoObject.financialDetails.sourceOfFunds.englishdescription = \"\";\nautoObject.financialDetails.chargesAccount = \"\";\nautoObject.financialDetails.CashAmtWithNoCurrency = 0.0;\nautoObject.financialDetails.amtDeferredAvalized = 0.0;\nautoObject.financialDetails.amtPaidbyOtherBanks = 0.0;\nautoObject.financialDetails.cashAmtInDocCurrency = 0.0;\nautoObject.IDCRequestType = {};\nautoObject.IDCRequestType.id = 0;\nautoObject.IDCRequestType.code = \"\";\nautoObject.IDCRequestType.arabicdescription = \"\";\nautoObject.IDCRequestType.englishdescription = \"\";\nautoObject.isIDCWithdrawn = false;\nautoObject.IDCRequestStage = \"\";\nautoObject.FCContractNumber = \"\";\nautoObject.billOfLading = [];\nautoObject.billOfLading[0] = {};\nautoObject.billOfLading[0].date = new Date();\nautoObject.billOfLading[0].number = \"\";\nautoObject.importPurpose = {};\nautoObject.importPurpose.id = 0;\nautoObject.importPurpose.code = \"\";\nautoObject.importPurpose.arabicdescription = \"\";\nautoObject.importPurpose.englishdescription = \"\";\nautoObject.IDCRequestNature = {};\nautoObject.IDCRequestNature.id = 0;\nautoObject.IDCRequestNature.code = \"\";\nautoObject.IDCRequestNature.arabicdescription = \"\";\nautoObject.IDCRequestNature.englishdescription = \"\";\nautoObject.customerInformation = {};\nautoObject.customerInformation.CIFNumber = \"\";\nautoObject.customerInformation.importCardNumber = \"\";\nautoObject.customerInformation.commercialRegistrationNumber = \"\";\nautoObject.customerInformation.customerName = \"\";\nautoObject.customerInformation.isCustomeSanctionedbyCBE = \"\";\nautoObject.customerInformation.CBENumber = \"\";\nautoObject.customerInformation.customerSector = \"\";\nautoObject.customerInformation.facilityType = \"\";\nautoObject.customerInformation.commercialRegistrationOffice = \"\";\nautoObject.customerInformation.taxCardNumber = \"\";\nautoObject.customerInformation.customerType = \"\";\nautoObject.customerInformation.addressLine1 = \"\";\nautoObject.customerInformation.addressLine2 = \"\";\nautoObject.invoices = [];\nautoObject.invoices[0] = {};\nautoObject.invoices[0].date = new Date();\nautoObject.invoices[0].number = \"\";\nautoObject.productCategory = {};\nautoObject.productCategory.id = 0;\nautoObject.productCategory.code = \"\";\nautoObject.productCategory.arabicdescription = \"\";\nautoObject.productCategory.englishdescription = \"\";\nautoObject.documentsSource = {};\nautoObject.documentsSource.id = 0;\nautoObject.documentsSource.code = \"\";\nautoObject.documentsSource.arabicdescription = \"\";\nautoObject.documentsSource.englishdescription = \"\";\nautoObject.ParentIDCRequestNumber = \"\";\nautoObject.paymentTerms = {};\nautoObject.paymentTerms.id = 0;\nautoObject.paymentTerms.code = \"\";\nautoObject.paymentTerms.arabicdescription = \"\";\nautoObject.paymentTerms.englishdescription = \"\";\nautoObject.approvals = {};\nautoObject.approvals.CAD = false;\nautoObject.approvals.treasury = false;\nautoObject.approvals.compliance = false;\nautoObject.appLog = [];\nautoObject.appLog[0] = {};\nautoObject.appLog[0].startTime = new Date();\nautoObject.appLog[0].endTime = new Date();\nautoObject.appLog[0].userName = \"\";\nautoObject.appLog[0].role = \"\";\nautoObject.appLog[0].step = \"\";\nautoObject.appLog[0].action = \"\";\nautoObject.appLog[0].comment = \"\";\nautoObject.appLog[0].terminateReason = \"\";\nautoObject.appLog[0].returnReason = \"\";\nautoObject.stepLog = {};\nautoObject.stepLog.startTime = new Date();\nautoObject.stepLog.endTime = new Date();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.5ceab549-c986-4d50-8793-1ddb60d1c6c6"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = [];\nautoObject[0] = {};\nautoObject[0].name = \"\";\nautoObject[0].description = \"\";\nautoObject[0].arabicName = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"attachment","isCollection":true,"id":"2055.6961cd54-d200-49ed-97ed-55daead4c5e6"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.hubCode = \"\";\nautoObject.branchCode = \"\";\nautoObject.initatorUser = \"\";\nautoObject.branchName = \"\";\nautoObject.hubName = \"\";\nautoObject.branchSeq = \"\";\nautoObject.creditAdminMakerGroup = \"\";\nautoObject.creditAdminCheckerGroup = \"\";\nautoObject.isLargeCorporate = false;\nautoObject"}]},"itemSubjectRef":"itm.12.9f3bdc5d-083e-4370-b8d4-f0a7541dcf11","name":"idcRoutingDetails","isCollection":false,"id":"2055.afed5162-38c2-4fcb-8a37-14da854cba8f"},{"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"folderID","isCollection":false,"id":"2055.d086cb9c-04dc-49bf-8915-e15ad5f25550"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.fullPath = \"\";\nautoObject.cmisQuery = \"\";\nautoObject.defaultProperties = [];\nautoObject.defaultProperties[0] = {};\nautoObject.defaultProperties[0].name = \"\";\nautoObject.defaultProperties[0].value = null;\nautoObject.defaultProperties[0].editable = false;\nautoObject.defaultProperties[0].hidden = false;\nautoObject"}]},"itemSubjectRef":"itm.12.ae22157e-8e19-4a60-a294-712ff5dc96df","name":"ECMproperties","isCollection":false,"id":"2055.d74ff120-55c7-4ec7-883b-6c355ed5b926"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentPath","isCollection":false,"id":"2055.bc66ce14-d656-4a24-89ef-aba70280cada"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.collateralAmount = 0.0;\nautoObject.userReference = \"\";\nautoObject.settlementAccounts = [];\nautoObject.settlementAccounts[0] = {};\nautoObject.settlementAccounts[0].debitedAccount = {};\nautoObject.settlementAccounts[0].debitedAccount.balanceSign = \"\";\nautoObject.settlementAccounts[0].debitedAccount.GLAccountNumber = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency = {};\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.id = 0;\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.code = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.arabicdescription = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.englishdescription = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountBranchCode = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountBalance = 0.0;\nautoObject.settlementAccounts[0].debitedAccount.accountNumber = \"\";\nautoObject.settlementAccounts[0].debitedAccount.accountClass = \"\";\nautoObject.settlementAccounts[0].debitedAccount.ownerAccounts = \"\";\nautoObject.settlementAccounts[0].debitedAccount.currency = {};\nautoObject.settlementAccounts[0].debitedAccount.currency.name = \"\";\nautoObject.settlementAccounts[0].debitedAccount.currency.value = \"\";\nautoObject.settlementAccounts[0].debitedAccount.isGLFoundC = false;\nautoObject.settlementAccounts[0].debitedAccount.commCIF = \"\";\nautoObject.settlementAccounts[0].debitedAccount.isGLVerifiedC = false;\nautoObject.settlementAccounts[0].debitedAccount.isOverDraft = false;\nautoObject.settlementAccounts[0].debitedAmount = {};\nautoObject.settlementAccounts[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.debitPercentage = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.standardExchangeRate = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;\nautoObject.settlementAccounts[0].debitedAmount.negotiatedExchangeRate = 0.0;\nautoObject.settlementAccounts[0].accountNumberList = [];\nautoObject.settlementAccounts[0].accountNumberList[0] = {};\nautoObject.settlementAccounts[0].accountNumberList[0].name = \"\";\nautoObject.settlementAccounts[0].accountNumberList[0].value = \"\";\nautoObject.settlementAccounts[0].settCIF = \"\";\nautoObject.billAmount = 0.0;\nautoObject.billCurrency = {};\nautoObject.billCurrency.id = 0;\nautoObject.billCurrency.code = \"\";\nautoObject.billCurrency.arabicdescription = \"\";\nautoObject.billCurrency.englishdescription = \"\";\nautoObject.party = [];\nautoObject.party[0] = {};\nautoObject.party[0].partyType = {};\nautoObject.party[0].partyType.name = \"\";\nautoObject.party[0].partyType.value = \"\";\nautoObject.party[0].partyId = \"\";\nautoObject.party[0].name = \"\";\nautoObject.party[0].country = \"\";\nautoObject.party[0].reference = \"\";\nautoObject.party[0].address1 = \"\";\nautoObject.party[0].address2 = \"\";\nautoObject.party[0].address3 = \"\";\nautoObject.party[0].address4 = \"\";\nautoObject.party[0].media = \"\";\nautoObject.party[0].address = \"\";\nautoObject.party[0].phone = \"\";\nautoObject.party[0].fax = \"\";\nautoObject.party[0].email = \"\";\nautoObject.party[0].contactPersonName = \"\";\nautoObject.party[0].mobile = \"\";\nautoObject.party[0].branch = {};\nautoObject.party[0].branch.name = \"\";\nautoObject.party[0].branch.value = \"\";\nautoObject.party[0].language = \"\";\nautoObject.party[0].partyCIF = \"\";\nautoObject.party[0].isNbeCustomer = false;\nautoObject.party[0].isRetrived = false;\nautoObject.sourceReference = \"\";\nautoObject.isLimitsTrackingRequired = false;\nautoObject.liquidationSummary = {};\nautoObject.liquidationSummary.liquidationCurrency = \"\";\nautoObject.liquidationSummary.debitBasisby = \"\";\nautoObject.liquidationSummary.liquidationAmt = 0.0;\nautoObject.liquidationSummary.debitValueDate = new Date();\nautoObject.liquidationSummary.creditValueDate = new Date();\nautoObject.IDCProduct = {};\nautoObject.IDCProduct.id = 0;\nautoObject.IDCProduct.code = \"\";\nautoObject.IDCProduct.arabicdescription = \"\";\nautoObject.IDCProduct.englishdescription = \"\";\nautoObject.interestToDate = new Date();\nautoObject.transactionMaturityDate = new Date();\nautoObject.commissionsAndCharges = [];\nautoObject.commissionsAndCharges[0] = {};\nautoObject.commissionsAndCharges[0].component = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount = {};\nautoObject.commissionsAndCharges[0].debitedAccount.balanceSign = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.GLAccountNumber = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency = {};\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.id = 0;\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.code = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.arabicdescription = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.englishdescription = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountBranchCode = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountBalance = 0.0;\nautoObject.commissionsAndCharges[0].debitedAccount.accountNumber = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.accountClass = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.ownerAccounts = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.currency = {};\nautoObject.commissionsAndCharges[0].debitedAccount.currency.name = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.currency.value = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.isGLFoundC = false;\nautoObject.commissionsAndCharges[0].debitedAccount.commCIF = \"\";\nautoObject.commissionsAndCharges[0].debitedAccount.isGLVerifiedC = false;\nautoObject.commissionsAndCharges[0].debitedAccount.isOverDraft = false;\nautoObject.commissionsAndCharges[0].chargeAmount = 0.0;\nautoObject.commissionsAndCharges[0].waiver = false;\nautoObject.commissionsAndCharges[0].debitedAmount = {};\nautoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.debitPercentage = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.standardExchangeRate = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;\nautoObject.commissionsAndCharges[0].debitedAmount.negotiatedExchangeRate = 0.0;\nautoObject.commissionsAndCharges[0].defaultCurrency = {};\nautoObject.commissionsAndCharges[0].defaultCurrency.id = 0;\nautoObject.commissionsAndCharges[0].defaultCurrency.code = \"\";\nautoObject.commissionsAndCharges[0].defaultCurrency.arabicdescription = \"\";\nautoObject.commissionsAndCharges[0].defaultCurrency.englishdescription = \"\";\nautoObject.commissionsAndCharges[0].defaultAmount = 0.0;\nautoObject.commissionsAndCharges[0].commAccountList = [];\nautoObject.commissionsAndCharges[0].commAccountList[0] = {};\nautoObject.commissionsAndCharges[0].commAccountList[0].name = \"\";\nautoObject.commissionsAndCharges[0].commAccountList[0].value = \"\";\nautoObject.transactionBaseDate = new Date();\nautoObject.tradeFinanceApprovalNumber = \"\";\nautoObject.FCContractNumber = \"\";\nautoObject.collateralCurrency = {};\nautoObject.collateralCurrency.id = 0;\nautoObject.collateralCurrency.code = \"\";\nautoObject.collateralCurrency.arabicdescription = \"\";\nautoObject.collateralCurrency.englishdescription = \"\";\nautoObject.interestRate = 0.0;\nautoObject.transactionTransitDays = 0;\nautoObject.swiftMessageData = {};\nautoObject.swiftMessageData.intermediary = {};\nautoObject.swiftMessageData.intermediary.line1 = \"\";\nautoObject.swiftMessageData.intermediary.line2 = \"\";\nautoObject.swiftMessageData.intermediary.line3 = \"\";\nautoObject.swiftMessageData.intermediary.line4 = \"\";\nautoObject.swiftMessageData.intermediary.line5 = \"\";\nautoObject.swiftMessageData.intermediary.line6 = \"\";\nautoObject.swiftMessageData.detailsOfCharge = \"\";\nautoObject.swiftMessageData.accountWithInstitution = {};\nautoObject.swiftMessageData.accountWithInstitution.line1 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line2 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line3 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line4 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line5 = \"\";\nautoObject.swiftMessageData.accountWithInstitution.line6 = \"\";\nautoObject.swiftMessageData.receiver = \"\";\nautoObject.swiftMessageData.swiftMessageOption = \"\";\nautoObject.swiftMessageData.coverRequired = \"\";\nautoObject.swiftMessageData.transferType = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution = {};\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line1 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line2 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line3 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line4 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line5 = \"\";\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line6 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent = {};\nautoObject.swiftMessageData.receiverCorrespondent.line1 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line2 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line3 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line4 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line5 = \"\";\nautoObject.swiftMessageData.receiverCorrespondent.line6 = \"\";\nautoObject.swiftMessageData.detailsOfPayment = {};\nautoObject.swiftMessageData.detailsOfPayment.line1 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line2 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line3 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line4 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line5 = \"\";\nautoObject.swiftMessageData.detailsOfPayment.line6 = \"\";\nautoObject.swiftMessageData.orderingInstitution = {};\nautoObject.swiftMessageData.orderingInstitution.line1 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line2 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line3 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line4 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line5 = \"\";\nautoObject.swiftMessageData.orderingInstitution.line6 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution = {};\nautoObject.swiftMessageData.beneficiaryInstitution.line1 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line2 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line3 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line4 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line5 = \"\";\nautoObject.swiftMessageData.beneficiaryInstitution.line6 = \"\";\nautoObject.swiftMessageData.receiverOfCover = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary = {};\nautoObject.swiftMessageData.ultimateBeneficiary.line1 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line2 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line3 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line4 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line5 = \"\";\nautoObject.swiftMessageData.ultimateBeneficiary.line6 = \"\";\nautoObject.swiftMessageData.orderingCustomer = {};\nautoObject.swiftMessageData.orderingCustomer.line1 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line2 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line3 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line4 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line5 = \"\";\nautoObject.swiftMessageData.orderingCustomer.line6 = \"\";\nautoObject.swiftMessageData.senderToReciever = {};\nautoObject.swiftMessageData.senderToReciever.line1 = \"\";\nautoObject.swiftMessageData.senderToReciever.line2 = \"\";\nautoObject.swiftMessageData.senderToReciever.line3 = \"\";\nautoObject.swiftMessageData.senderToReciever.line4 = \"\";\nautoObject.swiftMessageData.senderToReciever.line5 = \"\";\nautoObject.swiftMessageData.senderToReciever.line6 = \"\";\nautoObject.swiftMessageData.RTGS = \"\";\nautoObject.swiftMessageData.RTGSNetworkType = \"\";\nautoObject.advices = [];\nautoObject.advices[0] = {};\nautoObject.advices[0].adviceCode = \"\";\nautoObject.advices[0].suppressed = false;\nautoObject.advices[0].advicelines = {};\nautoObject.advices[0].advicelines.line1 = \"\";\nautoObject.advices[0].advicelines.line2 = \"\";\nautoObject.advices[0].advicelines.line3 = \"\";\nautoObject.advices[0].advicelines.line4 = \"\";\nautoObject.advices[0].advicelines.line5 = \"\";\nautoObject.advices[0].advicelines.line6 = \"\";\nautoObject.cashCollateralAccounts = [];\nautoObject.cashCollateralAccounts[0] = {};\nautoObject.cashCollateralAccounts[0].accountCurrency = \"\";\nautoObject.cashCollateralAccounts[0].accountClass = \"\";\nautoObject.cashCollateralAccounts[0].debitedAmtinCollateralCurrency = 0.0;\nautoObject.cashCollateralAccounts[0].GLAccountNumber = \"\";\nautoObject.cashCollateralAccounts[0].accountBranchCode = \"\";\nautoObject.cashCollateralAccounts[0].accountNumber = {};\nautoObject.cashCollateralAccounts[0].accountNumber.name = \"\";\nautoObject.cashCollateralAccounts[0].accountNumber.value = \"\";\nautoObject.cashCollateralAccounts[0].isGLFound = false;\nautoObject.cashCollateralAccounts[0].isGLVerified = false;\nautoObject.IDCRequestStage = \"\";\nautoObject.transactionValueDate = new Date();\nautoObject.transactionTenorDays = 0;\nautoObject.contractLimitsTracking = [];\nautoObject.contractLimitsTracking[0] = {};\nautoObject.contractLimitsTracking[0].partyType = {};\nautoObject.contractLimitsTracking[0].partyType.name = \"\";\nautoObject.contractLimitsTracking[0].partyType.value = \"\";\nautoObject.contractLimitsTracking[0].type = \"\";\nautoObject.contractLimitsTracking[0].jointVentureParent = \"\";\nautoObject.contractLimitsTracking[0].customerNo = \"\";\nautoObject.contractLimitsTracking[0].linkageRefNum = \"\";\nautoObject.contractLimitsTracking[0].amountTag = \"\";\nautoObject.contractLimitsTracking[0].contributionPercentage = 0.0;\nautoObject.contractLimitsTracking[0].isCIFfound = false;\nautoObject.interestFromDate = new Date();\nautoObject.interestAmount = 0.0;\nautoObject.haveInterest = false;\nautoObject.accountNumberList = [];\nautoObject.accountNumberList[0] = {};\nautoObject.accountNumberList[0].name = \"\";\nautoObject.accountNumberList[0].value = \"\";\nautoObject.facilities = [];\nautoObject.facilities[0] = {};\nautoObject.facilities[0].facilityCode = \"\";\nautoObject.facilities[0].overallLimit = 0.0;\nautoObject.facilities[0].limitAmount = 0.0;\nautoObject.facilities[0].effectiveLimitAmount = 0.0;\nautoObject.facilities[0].availableAmount = 0.0;\nautoObject.facilities[0].expiryDate = new Date();\nautoObject.facilities[0].availableFlag = false;\nautoObject.facilities[0].authorizedFlag = false;\nautoObject.facilities[0].Utilization = 0.0;\nautoObject.facilities[0].returnCode = \"\";\nautoObject.facilities[0].facilityLines = [];\nautoObject.facilities[0].facilityLines[0] = {};\nautoObject.facilities[0].facilityLines[0].lineCode = \"\";\nautoObject.facilities[0].facilityLines[0].lineAmount = 0.0;\nautoObject.facilities[0].facilityLines[0].availableAmount = 0.0;\nautoObject.facilities[0].facilityLines[0].effectiveLineAmount = 0.0;\nautoObject.facilities[0].facilityLines[0].expiryDate = new Date();\nautoObject.facilities[0].facilityLines[0].facilityBranch = {};\nautoObject.facilities[0].facilityLines[0].facilityBranch.name = \"\";\nautoObject.facilities[0].facilityLines[0].facilityBranch.value = \"\";\nautoObject.facilities[0].facilityLines[0].availableFlag = false;\nautoObject.facilities[0].facilityLines[0].authorizedFlag = false;\nautoObject.facilities[0].facilityLines[0].facilityPercentageToBook = 0;\nautoObject.facilities[0].facilityLines[0].internalRemarks = \"\";\nautoObject.facilities[0].facilityLines[0].purpose = \"\";\nautoObject.facilities[0].facilityLines[0].LCCommissionPercentage = 0;\nautoObject.facilities[0].facilityLines[0].LCDef = \"\";\nautoObject.facilities[0].facilityLines[0].LCCashCover = \"\";\nautoObject.facilities[0].facilityLines[0].IDCCommission = \"\";\nautoObject.facilities[0].facilityLines[0].IDCAvalCommPercentage = 0;\nautoObject.facilities[0].facilityLines[0].IDCCashCoverPercentage = 0;\nautoObject.facilities[0].facilityLines[0].debitAccountNumber = \"\";\nautoObject.facilities[0].facilityLines[0].lineCurrency = \"\";\nautoObject.facilities[0].facilityLines[0].lineSerialNumber = \"\";\nautoObject.facilities[0].facilityLines[0].returnCode = \"\";\nautoObject.facilities[0].facilityLines[0].LGCommission = 0;\nautoObject.facilities[0].facilityLines[0].LGCashCover_BidBond = 0;\nautoObject.facilities[0].facilityLines[0].LGCashCover_Performance = 0;\nautoObject.facilities[0].facilityLines[0].LGCashCover_AdvancedPayment = 0;\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies = [];\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0] = {};\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].name = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].code = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].source = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].status = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedBranches = [];\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0] = {};\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0].name = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0].code = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0].source = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0].status = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedProducts = [];\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0] = {};\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0].name = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0].code = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0].source = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0].status = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCustomers = [];\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0] = {};\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0].name = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0].code = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0].source = \"\";\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0].status = \"\";\nautoObject.facilities[0].facilityLines[0].exposureRestrictions = [];\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0] = {};\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0].name = \"\";\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0].code = \"\";\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0].source = \"\";\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0].status = \"\";\nautoObject.facilities[0].facilityLines[0].CIF = \"\";\nautoObject.facilities[0].facilityLines[0].partyType = {};\nautoObject.facilities[0].facilityLines[0].partyType.name = \"\";\nautoObject.facilities[0].facilityLines[0].partyType.value = \"\";\nautoObject.facilities[0].facilityLines[0].facilityID = \"\";\nautoObject.facilities[0].status = \"\";\nautoObject.facilities[0].facilityCurrency = {};\nautoObject.facilities[0].facilityCurrency.name = \"\";\nautoObject.facilities[0].facilityCurrency.value = \"\";\nautoObject.facilities[0].facilityID = \"\";\nautoObject.limitsTrackingVIs = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1","name":"idcContract","isCollection":false,"id":"2055.8d2b550b-2372-465b-84f6-203516e6a702"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"1.3d1f2af1-88e6-4d5b-b525-040e705d4ab4"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5ceab549-c986-4d50-8793-1ddb60d1c6c6</processParameterId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>05e76502-3778-4c62-97ad-4e5dc793039d</guid>
            <versionId>50170d9c-aaf0-4dca-ada8-9eab88ca9b88</versionId>
        </processParameter>
        <processParameter name="attachment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.6961cd54-d200-49ed-97ed-55daead4c5e6</processParameterId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>89a28ed6-7274-4315-a149-15f3f23e5186</guid>
            <versionId>56925bbe-7a3e-46df-98e7-09139b5c863e</versionId>
        </processParameter>
        <processParameter name="idcRoutingDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.afed5162-38c2-4fcb-8a37-14da854cba8f</processParameterId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.9f3bdc5d-083e-4370-b8d4-f0a7541dcf11</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>43933d81-319d-402f-b8e4-3d7403103c63</guid>
            <versionId>12436dec-f4e2-4eab-9c1c-e6db04777794</versionId>
        </processParameter>
        <processParameter name="folderID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d086cb9c-04dc-49bf-8915-e15ad5f25550</processParameterId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>9dcf7386-5b37-4141-90a4-800be22a0df8</guid>
            <versionId>04cfa062-1d04-44e3-9c44-ece6c68be6d5</versionId>
        </processParameter>
        <processParameter name="ECMproperties">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d74ff120-55c7-4ec7-883b-6c355ed5b926</processParameterId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.ae22157e-8e19-4a60-a294-712ff5dc96df</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a5e9481f-bfac-43c2-a5b0-4c2bde4a79db</guid>
            <versionId>403599dd-6504-4e8a-a10b-a834fe29c9d0</versionId>
        </processParameter>
        <processParameter name="parentPath">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.bc66ce14-d656-4a24-89ef-aba70280cada</processParameterId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d8c6f6b1-64da-4c94-b445-0b8abf050a35</guid>
            <versionId>e7ddcf7b-a757-439f-a294-e7ee24e86151</versionId>
        </processParameter>
        <processParameter name="idcContract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.8d2b550b-2372-465b-84f6-203516e6a702</processParameterId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classId>
            <seq>7</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>acd389b2-e43e-444b-991d-f83b8bdce37d</guid>
            <versionId>95c4ea71-c2ac-451a-9ab4-460d56a2dc3b</versionId>
        </processParameter>
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.feda83ff-2ac0-4dbb-8941-6c929d776d96</processParameterId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>8</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>64037418-bb94-432e-b4ff-4e09c2b388e5</guid>
            <versionId>c94e3941-6eed-4922-b30f-552a2e190e65</versionId>
        </processParameter>
        <processParameter name="attachment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.f616fc43-d293-443a-afb4-37d7d3abd8b7</processParameterId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <seq>9</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>eb10eb7d-7114-491c-96ab-b88450d3e714</guid>
            <versionId>ee577bed-6aed-4cc0-ba59-9b727042939f</versionId>
        </processParameter>
        <processParameter name="folderID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.02e6c09f-afcc-4424-92d7-0e28498532af</processParameterId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>10</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>3da6d7bd-4494-4ce9-9f80-4a9c7069303c</guid>
            <versionId>d61fe9b3-a8ca-48b6-bde4-b18647c25e8f</versionId>
        </processParameter>
        <processParameter name="ECMproperties">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.6c128f72-716e-4f76-b73f-dad75e5d3156</processParameterId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.ae22157e-8e19-4a60-a294-712ff5dc96df</classId>
            <seq>11</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>0209f55c-6582-4a4f-96fc-992935c95036</guid>
            <versionId>e0a09d9e-0881-46b6-b454-bd738611862d</versionId>
        </processParameter>
        <processParameter name="parentPath">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.51acf25f-3a3a-4f2c-9681-d2fb03b38a02</processParameterId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>12</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>97e2eaf4-0788-4df8-9042-efaf742e8103</guid>
            <versionId>41ffe68a-ce1e-46b6-8ed3-f61afdd39ea4</versionId>
        </processParameter>
        <processParameter name="idcContract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.f0530dfd-3e46-4485-8c28-93a4424136c0</processParameterId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classId>
            <seq>13</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>609057a8-2f4d-4feb-aa74-65e70d5bf223</guid>
            <versionId>16a5a2f2-ef91-4432-947e-b1b6ca32eba5</versionId>
        </processParameter>
        <processVariable name="tmpIDCrequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c034f8c0-6cfd-445e-a683-0a33aa97f71f</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>65727fbe-6e4d-4051-a99a-60b0f4ce04a8</guid>
            <versionId>811e0b07-0b55-43c7-b93e-c2ff3063cdfd</versionId>
        </processVariable>
        <processVariable name="message">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5487b7be-c459-4495-9b9b-2f847681ad5a</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1584a3cd-3c75-4527-952f-6b5c345b72d6</guid>
            <versionId>e1445219-20fc-4dd1-96ea-8d007626e008</versionId>
        </processVariable>
        <processVariable name="action">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.74e58948-7016-4ea7-837f-9c0a9564659c</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ac15162d-787f-46ec-8a99-c31ab6a63c16</guid>
            <versionId>95fd9979-20f1-4ef9-8f93-348b507621fd</versionId>
        </processVariable>
        <processVariable name="selectedAction">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.af984f2d-e341-4e5d-b5c4-a1d7d94df897</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>16a1a605-def2-43e7-9ba3-41523a2d24cc</guid>
            <versionId>31ba684f-5653-4b1b-8384-5a447f977c02</versionId>
        </processVariable>
        <processVariable name="retrieveRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d3da5538-d691-497a-b5ad-e3a8951ba8b5</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>58c72258-141d-4d73-99be-ab73be610d7c</guid>
            <versionId>a2a69ee2-6bea-422c-916f-0bc9766609f7</versionId>
        </processVariable>
        <processVariable name="retrieveCustomer">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6f519687-e7ad-4934-baf4-8351a36aff47</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>eb74847d-4648-47fd-a35c-fc8e608e7454</guid>
            <versionId>432c9d42-05e7-4de1-a802-a1179c5f664f</versionId>
        </processVariable>
        <processVariable name="advancePaymentsUsedVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ab1441ac-969b-44c3-9838-bef5dca3e81f</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0eb4aab9-5b0f-4822-bef4-fd363272c01b</guid>
            <versionId>887ee86a-88cd-4662-b529-c8f1d460ea5d</versionId>
        </processVariable>
        <processVariable name="havePaymentTerms">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.eaf2f7a4-527e-4089-8c56-23bd9239dd5b</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3129cae6-6163-4300-81b6-e47588daf5f1</guid>
            <versionId>d8a09248-64bc-4615-97fe-25a7ba82c0de</versionId>
        </processVariable>
        <processVariable name="haseApprovals">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.64e76842-552e-421e-8aa0-7210a82b8744</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>fc2425ba-512b-44d3-9f21-12aeb49f0bc9</guid>
            <versionId>22c8dd95-410f-4662-8200-1119dd676e45</versionId>
        </processVariable>
        <processVariable name="hasReturnReason">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.1bae6db0-3223-425c-b9c5-e4a94981cf78</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b1197cdb-45d1-425c-b881-90aaef7135f9</guid>
            <versionId>62efc59c-add8-443d-8259-ca465b6ef768</versionId>
        </processVariable>
        <processVariable name="accountNumber">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.bbb188e8-3cfc-495a-b29e-c6a152b21295</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d8c99df2-ddb5-49cd-80f7-aa08b3ed6ae8</guid>
            <versionId>80b4ad92-131c-4d2e-a49a-001c636892f9</versionId>
        </processVariable>
        <processVariable name="exist">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7881b472-e7ab-4ee6-8e33-39e9338d4c35</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>12</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>78e84711-84b4-4afe-a10f-9625f301de96</guid>
            <versionId>92288526-cb7c-4f46-835b-3361de8993e5</versionId>
        </processVariable>
        <processVariable name="errorExist">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.fbce89f3-dbca-44b8-bab6-96b02e317dc9</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>13</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c4f485ad-2c34-4853-b035-d1cf3735bdda</guid>
            <versionId>ec9ecc0a-fb68-4277-9ac6-0ab6ff05901b</versionId>
        </processVariable>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.88ea9ce0-bc9e-46c3-94ee-1cec1bbaca80</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>14</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>181071ae-73be-41eb-9932-7f71d779e8f1</guid>
            <versionId>b0aef1ac-96e4-4fba-8aac-fbaa3910cad9</versionId>
        </processVariable>
        <processVariable name="validation">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.bd63dfb5-84a3-4d46-9896-559942870dc1</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>15</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d43f0c03-bff7-444a-b17c-38643e768913</guid>
            <versionId>0679077d-7e9a-48e3-9711-96d815f61a11</versionId>
        </processVariable>
        <processVariable name="default_properties">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.460773e0-c5ba-4752-aeb7-072f2e8f5748</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>16</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.eac829db-66fe-43f5-810c-6faa514533a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>74a83382-46ae-449e-8a91-bdb52c723361</guid>
            <versionId>7f81f979-8096-4e7c-b892-471c595dc960</versionId>
        </processVariable>
        <processVariable name="cmisQuery">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.1d26dfd7-611d-4088-abc8-66887fe7511d</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>17</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>fa0d926b-eaa4-4ecf-bb09-e1486380fc84</guid>
            <versionId>c7277570-6adb-4c15-bcb3-6298a8bff947</versionId>
        </processVariable>
        <processVariable name="documentsTypesSelected">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.acae7137-6267-4e31-bc9a-e8c6bc42e93b</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>18</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>293fb5ba-145e-49c3-a0f1-b1f9a1169a64</guid>
            <versionId>dd25f61e-e657-4bca-8a6f-5c6423821e12</versionId>
        </processVariable>
        <processVariable name="accountsList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0a63ba65-76ed-4878-8c1a-16415bfd58e2</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>19</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>933a56fd-01a2-49d8-a6a2-bd505de1957c</guid>
            <versionId>b8aad5ae-d3e3-4481-b84e-3f76cc84ee64</versionId>
        </processVariable>
        <processVariable name="role">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.25f650ed-491e-4700-81bf-82ba316c9a93</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>20</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8323b6ee-cea6-455b-a40a-174fb0a73c81</guid>
            <versionId>81ba9d15-945d-41c5-abff-fc8fd74710dd</versionId>
        </processVariable>
        <processVariable name="tmpUsedAdvancePayment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.1bf58584-0f2a-4931-835d-b9ef5bbf376f</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>21</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.fc23c5c1-8fd3-4584-89be-8352a606da67</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>03c72cda-edb8-4d4a-8fda-eecea6cc428a</guid>
            <versionId>579dc9bd-3d83-4696-97b2-c802ef19bbd1</versionId>
        </processVariable>
        <processVariable name="validationMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a73650fd-39fa-4ce8-8a8c-f608030db340</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>22</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a94b8f63-41f3-4b4a-85d0-b31ffc92a5ab</guid>
            <versionId>f7ef3f7e-dccd-4530-a38f-ea4303a68178</versionId>
        </processVariable>
        <processVariable name="invalidTabs">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.63b05af4-**************-a37d4e052126</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>23</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>eb055013-9a3f-408c-ac89-c1367b3508aa</guid>
            <versionId>cbe52453-c552-47c9-9ce3-fffc22a9e65d</versionId>
        </processVariable>
        <processVariable name="currencyVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.da608b97-9e34-4771-873c-5246044a4dfa</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>24</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e8a14979-5310-414c-82cd-9bf1793f1264</guid>
            <versionId>b85e9b29-db09-473b-8391-33b8abbac8bf</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c4827fb2-0617-41f5-880c-2a35a1d2c2ff</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>25</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d21f7052-b005-46e2-a059-e47f2fbe2442</guid>
            <versionId>db90df07-2318-4097-b280-fc04d1472e72</versionId>
        </processVariable>
        <processVariable name="errorVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.779bfb1d-d3ca-4074-86ca-cd45a72f60e4</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>26</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8c9d3f7e-0216-4df1-af99-89b88463aa72</guid>
            <versionId>ae8b4764-c402-455e-8c5a-054da8f15198</versionId>
        </processVariable>
        <processVariable name="alertMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.88008abd-7153-488a-8b25-60048fe80e0b</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>27</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>7f8ba50b-1817-4191-843e-dfe862abc0b0</guid>
            <versionId>b53f499e-e0dd-40ad-947b-aa7b9fc1741b</versionId>
        </processVariable>
        <processVariable name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.287a216f-5a65-40e7-88eb-9db5fcf7201c</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>28</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bc2318b2-99c7-4635-8af7-249f829fbb4b</guid>
            <versionId>c30b903d-d9dc-473b-89af-0739056f86a7</versionId>
        </processVariable>
        <processVariable name="InterestAndChargesList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f9281cb6-b1ec-4a1a-828d-253437b3aaa9</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>29</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.e9f65280-afe9-44dc-9616-f95c2a14629e</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>376f511e-0722-4a66-881f-b79f8b123341</guid>
            <versionId>a046d6f2-e276-4712-bcd9-2edf336f8272</versionId>
        </processVariable>
        <processVariable name="exRate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.320557b4-d664-425e-87a3-ce20cbf518ab</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>30</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>36065e81-1b09-4f0d-9cea-5cbcb3966f7f</guid>
            <versionId>7913f9ab-9b4d-44a7-aeb1-ab3e1822317e</versionId>
        </processVariable>
        <processVariable name="adviceCodeList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.63336b9a-7a4a-4480-8a36-7b06021879c4</processVariableId>
            <description isNull="true" />
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <namespace>2</namespace>
            <seq>31</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>799605b1-c790-44db-98ee-2a1c972c9158</guid>
            <versionId>024f9ec3-5304-45fa-b3e3-26eb77cee55c</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.bf1e6e71-4a30-4575-9747-448f0c391594</processItemId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <name>Initialization Service</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.b4c85f7c-1244-42a5-b179-f29f5856fde7</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61e8</guid>
            <versionId>699c8d2f-7ddb-4fe8-a0d4-45760a95b0f2</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.b4c85f7c-1244-42a5-b179-f29f5856fde7</subProcessId>
                <attachedProcessRef>/1.bd478cbf-52d1-4705-9fe4-6868408a5256</attachedProcessRef>
                <guid>6278afa6-23be-4a31-a602-ea1df02325dd</guid>
                <versionId>262442c5-f8b0-4c57-8c13-0a987f8dc664</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.788acd72-219c-464c-b855-520f2a9e257b</processItemId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.adc54aba-c305-45ca-a2e0-39104688d53c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61e9</guid>
            <versionId>85346c7b-47eb-41e4-8d5d-a153dd2e2234</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.adc54aba-c305-45ca-a2e0-39104688d53c</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>520cff0d-89e2-447d-a821-d6f02e0c8d24</guid>
                <versionId>f2244ac5-c737-4f58-96b1-1047b20410ae</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.7d9060a3-db6f-4f1e-991c-a7b4c03053aa</processItemId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.ce5495f6-662b-4e2a-b1b3-4b536b134bc0</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61ec</guid>
            <versionId>ac80cad6-e119-4751-af3b-1c29f3e580de</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.0c9a7cd3-c644-472f-8c23-976def594caa</processItemId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <name>Database Integration</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.8ee6fad5-77fd-4922-a1cc-3446c47b230c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:83e1efe624431d49:-7084ad56:189defc53f4:-5016</guid>
            <versionId>ec431648-3bdc-4795-b1c1-06a8b7d3d8b5</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.8ee6fad5-77fd-4922-a1cc-3446c47b230c</subProcessId>
                <attachedProcessRef>/1.9f0a859b-5010-4ab6-947a-81ad99803cf1</attachedProcessRef>
                <guid>6b5d3d8a-3c02-4d51-85fe-9e8a321022a2</guid>
                <versionId>adc6e423-eeb5-453a-89ed-301e1153759d</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c9bece30-ee23-455f-a1f9-fdbc3274182b</processItemId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <name>Get Request Number ,CBE And ECM Folder</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.c6c94936-1744-4377-b2b5-e7569723755e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61eb</guid>
            <versionId>f08d17d7-8b05-4bbd-b64d-92cd0976f79a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.c6c94936-1744-4377-b2b5-e7569723755e</subProcessId>
                <attachedProcessRef>/1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13</attachedProcessRef>
                <guid>1dc796e9-c596-4d12-8fac-368717f21c69</guid>
                <versionId>72ed6c8c-a6d7-4985-bd2e-80f4f31cb40a</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.de4e528f-4cdb-425d-8c24-2def6866abf4</processItemId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <name>Get Required Documents</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.14894d52-b7f3-4da6-9ef7-e333720c7a19</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61ea</guid>
            <versionId>fd2665db-b939-4f45-994e-3cc81144925c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.14894d52-b7f3-4da6-9ef7-e333720c7a19</subProcessId>
                <attachedProcessRef>/1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2</attachedProcessRef>
                <guid>2dea0896-550a-4c7d-880d-7127123bfefb</guid>
                <versionId>b384aeeb-0d83-4d5c-a8c7-dff3a076860b</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.a4ae78a0-6abe-409a-98fc-84814612482d</epvProcessLinkId>
            <epvId>/21.0bb89e09-258a-4bd1-b3a7-137a3219209f</epvId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <guid>e58c5ac7-bb0d-45e5-bf1d-567096c20ea3</guid>
            <versionId>430317f2-8a14-41aa-bc09-89882bf74558</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.7032fb94-efbf-4ec7-b027-8fc20c9901b1</epvProcessLinkId>
            <epvId>/21.8ce8b34e-54bb-4623-a4c9-ab892efacac6</epvId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <guid>5aa8310a-e06d-4104-b35d-6bde9046622c</guid>
            <versionId>6d76fc71-7475-478d-8978-a17f1b94ada5</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.bd25e06e-c269-40e2-a70e-0d1b88db49ec</epvProcessLinkId>
            <epvId>/21.e5829eee-0ab1-4f47-9191-f0f8705bc33e</epvId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <guid>cae842e3-7b03-4b69-8bac-bc837f194b8a</guid>
            <versionId>baf4378d-f312-481a-ae0b-55b809d5274b</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.45e63237-3b06-4340-bf73-a5d0b312c9fe</epvProcessLinkId>
            <epvId>/21.02818ba4-c183-4dfb-8924-18e2d9a515dd</epvId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <guid>12391ed1-207c-427c-a603-dbf1c36e0858</guid>
            <versionId>ca9a0ad6-aa8c-4c47-9ed5-270f5b173569</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.a3a7e71f-ef8a-4a26-8d17-37975e072452</epvProcessLinkId>
            <epvId>/21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e</epvId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <guid>de95f853-7d0e-4a3e-8986-260f70e95df6</guid>
            <versionId>e344f52d-fdd4-4bc7-b53d-eee737e2c850</versionId>
        </EPV_PROCESS_LINK>
        <RESOURCE_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <resourceProcessLinkId>2059.2ff8b1db-e91e-4afa-9460-21ab3e886bca</resourceProcessLinkId>
            <resourceBundleGroupId>/50.95ec9bf9-2d5a-4fc5-8dd1-e6d6a55a836c</resourceBundleGroupId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <guid>2221bb82-8c82-45d2-aaf1-9d5785d9ded1</guid>
            <versionId>5db08482-3505-42e0-aa7e-bdf5d640f22e</versionId>
        </RESOURCE_PROCESS_LINK>
        <startingProcessItemId>2025.7d9060a3-db6f-4f1e-991c-a7b4c03053aa</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="1.3d1f2af1-88e6-4d5b-b525-040e705d4ab4" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                <ns16:globalUserTask implementation="##unspecified" name="Create IDC Request" id="1.19d05118-4f9d-482a-afd5-662663bc3612">
                    <ns16:documentation textFormat="text/plain" />
                    <ns16:extensionElements>
                        <ns3:userTaskImplementation processType="None" isClosed="false" id="d85d6e76-b1c5-4700-b87d-ce4d6728aa18">
                            <ns16:startEvent name="Start" id="141c9309-b3d1-422b-a948-c8d0d4a9c81b">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="12" y="178" width="24" height="24" color="#F8F8F8" />
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.ed651f9e-edd3-4ef0-b049-225616fba16f</ns16:outgoing>
                            </ns16:startEvent>
                            <ns16:endEvent name="End" id="ced35e6d-1321-4fd5-84dc-fc0e81d91c45">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1295" y="123" width="24" height="24" color="#F8F8F8" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.f7d79e2e-d4f5-4ec4-8194-c9455b758a1c</ns16:incoming>
                            </ns16:endEvent>
                            <ns16:sequenceFlow sourceRef="141c9309-b3d1-422b-a948-c8d0d4a9c81b" targetRef="2025.6d76e0dd-17bb-4e8d-98d1-82e8d0682c86" name="To Initialization Service" id="2027.ed651f9e-edd3-4ef0-b049-225616fba16f">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.491dcb01-2d8e-4999-a6f0-8b87817c909a" name="Set Step Name" id="2025.ce3318bc-ad0a-4809-b3e5-8d0a8f3f6929">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="212" y="57" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.1ad2c476-2429-4849-b48d-e8dc071b0173</ns16:incoming>
                                <ns16:outgoing>2027.491dcb01-2d8e-4999-a6f0-8b87817c909a</ns16:outgoing>
                                <ns16:script>tw.local.idcRequest.appInfo.stepName = tw.system.processInstance.task.subject;&#xD;
tw.local.idcRequest.appInfo.appID = tw.system.processInstance.id;&#xD;
&#xD;
</ns16:script>
                            </ns16:scriptTask>
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.d4c507ee-6622-4a63-86c2-2d8c273474d5" name="Set Status " id="2025.48434093-edd9-4b92-8b57-feca505a198d">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="929" y="100" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.37c1287e-8454-48d6-8236-610248cf002a</ns16:incoming>
                                <ns16:outgoing>2027.d4c507ee-6622-4a63-86c2-2d8c273474d5</ns16:outgoing>
                                <ns16:script>if (tw.local.idcRequest.IDCRequestType.englishdescription == "IDC Payment" &amp;&amp; tw.local.selectedAction == tw.epv.Action.submitRequestToHubDirectly) {&#xD;
	tw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingExecutionHubLiquidation;&#xD;
	tw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inExecution;&#xD;
}&#xD;
else if (tw.local.idcRequest.IDCRequestType.englishdescription == "IDC Amendment" &amp;&amp; tw.local.selectedAction == tw.epv.Action.submitRequestToHubDirectly) {&#xD;
	tw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingExecutionHubInitiation;	&#xD;
	tw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inExecution;&#xD;
}&#xD;
else if (tw.local.selectedAction == tw.epv.Action.cancelReques) {&#xD;
	tw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingBranchComplianceCancelationConfirmation;&#xD;
	tw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.initiated;&#xD;
} &#xD;
else {	&#xD;
	tw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingBranchComplianceInitiationReview;&#xD;
	tw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.initiated;&#xD;
}&#xD;
&#xD;
tw.local.idcRequest.stepLog.action = tw.local.selectedAction;</ns16:script>
                            </ns16:scriptTask>
                            <ns16:sequenceFlow sourceRef="2025.48434093-edd9-4b92-8b57-feca505a198d" targetRef="2025.0c9a7cd3-c644-472f-8c23-976def594caa" name="OK To End" id="2027.d4c507ee-6622-4a63-86c2-2d8c273474d5">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns3:formTask name="Create IDC Request" id="2025.6d76e0dd-17bb-4e8d-98d1-82e8d0682c86">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="381" y="155" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.53e9259e-a120-43ef-99ca-fb822db0f39d</ns16:incoming>
                                <ns16:incoming>2027.09f07a5e-4dcf-4ede-a465-3f7689a9bb0f</ns16:incoming>
                                <ns16:incoming>2027.8303ffc2-34a7-4d7f-8bd7-b43829b1c16d</ns16:incoming>
                                <ns16:incoming>2027.ed651f9e-edd3-4ef0-b049-225616fba16f</ns16:incoming>
                                <ns16:outgoing>2027.5b57c95f-3145-4ff1-b121-54b1b660ed84</ns16:outgoing>
                                <ns16:outgoing>2027.58aa3cd1-28e5-4d95-a115-9a0418d3e55b</ns16:outgoing>
                                <ns3:formDefinition>
                                    <ns19:coachDefinition>
                                        <ns19:layout>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                <ns19:id>99a5568f-10e0-475e-8e05-e82e4c291385</ns19:id>
                                                <ns19:layoutItemId>Error_Message1</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>0abe9db1-c47f-4505-8b0f-8d6bec43aea6</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>Error Message</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>be9ac253-c385-4322-8ad8-90afab999685</ns19:id>
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    <ns19:value />
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>d7e682ad-c27e-477c-87fc-7de40826e05b</ns19:id>
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    <ns19:value>SHOW</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>dda57087-e7be-473b-8482-62dd45c24ded</ns19:id>
                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>9c625c6f-aeb7-4b4a-8b02-61f3be54b3b7</ns19:id>
                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>e124408f-3c0f-4ac0-8dce-fcc1599409b0</ns19:id>
                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.46690ca1-5521-4e5c-bc9e-ecb22d75ca97</ns19:viewUUID>
                                            </ns19:layoutItem>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                <ns19:id>c1b815ac-7bf9-4c2e-869f-27d9c10b5fd8</ns19:id>
                                                <ns19:layoutItemId>Header_View2</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>06142723-9d7f-44b4-8539-98dc930d0ff6</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>Header View 2</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>abc3519b-23da-4236-8105-821292ae3e60</ns19:id>
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    <ns19:value />
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>196b2815-bed1-406a-8a45-68bb58783e39</ns19:id>
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    <ns19:value>SHOW</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>1bef3b9a-20bb-43e9-835a-d6b0268e16ca</ns19:id>
                                                    <ns19:optionName>buttonName</ns19:optionName>
                                                    <ns19:value>Create Request </ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>e81cc769-c8c6-4368-828e-fe82753deae0</ns19:id>
                                                    <ns19:optionName>stepLog</ns19:optionName>
                                                    <ns19:value>tw.local.idcRequest.stepLog</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>897334ee-922d-4806-8df7-ff8df6640773</ns19:id>
                                                    <ns19:optionName>approvals</ns19:optionName>
                                                    <ns19:value>tw.local.idcRequest.approvals</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>be146911-37e3-46f3-855b-5499bded5c9e</ns19:id>
                                                    <ns19:optionName>action</ns19:optionName>
                                                    <ns19:value>tw.local.action[]</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>0a9e11f8-e507-421b-8e02-64c340d9ce57</ns19:id>
                                                    <ns19:optionName>selectedAction</ns19:optionName>
                                                    <ns19:value>tw.local.selectedAction</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>398485b1-048e-4648-8494-d1532eb3ff7d</ns19:id>
                                                    <ns19:optionName>hasReturnReason</ns19:optionName>
                                                    <ns19:value>tw.local.hasReturnReason</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>29c86d27-aae9-495b-8856-bbf65a95b110</ns19:id>
                                                    <ns19:optionName>hasApprovals</ns19:optionName>
                                                    <ns19:value>tw.local.haseApprovals</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>943a19ee-d356-4fc2-8bb8-6d99e644457d</ns19:id>
                                                    <ns19:optionName>approvalsReadOnly</ns19:optionName>
                                                    <ns19:value>false</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>a3f4388f-cb67-4d56-8c20-35495703d4a0</ns19:id>
                                                    <ns19:optionName>invalidTabs</ns19:optionName>
                                                    <ns19:value>tw.local.invalidTabs[]</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>6ff6f5fc-885c-4528-801a-fa2fb92cf982</ns19:id>
                                                    <ns19:optionName>validationMessage</ns19:optionName>
                                                    <ns19:value>tw.local.validationMessage</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>a46181df-e6ee-4163-8e58-a8d92e8405cc</ns19:id>
                                                    <ns19:optionName>isCAD</ns19:optionName>
                                                    <ns19:value>false</ns19:value>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</ns19:viewUUID>
                                                <ns19:binding>tw.local.idcRequest.appInfo</ns19:binding>
                                                <ns19:contentBoxContrib>
                                                    <ns19:id>5712c7a3-1da3-468a-8220-8dd1cb7e2ce9</ns19:id>
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        <ns19:id>59820df2-db91-4b97-863e-68284c5efa03</ns19:id>
                                                        <ns19:layoutItemId>Start_New_Request</ns19:layoutItemId>
                                                        <ns19:configData>
                                                            <ns19:id>fa3af240-c889-4cfd-80f1-f904807acdf0</ns19:id>
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            <ns19:value>Start New Request</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>e38a8ba6-5e3e-407b-8642-4dd77d3beeea</ns19:id>
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            <ns19:value />
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>813d1354-f541-4412-8719-2ac60972e5d6</ns19:id>
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            <ns19:value>SHOW</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>f10559d5-59ab-4c56-89c8-87426ad84a75</ns19:id>
                                                            <ns19:optionName>parentIDCRequest</ns19:optionName>
                                                            <ns19:value>tw.local.tmpIDCrequest</ns19:value>
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>40c924ee-dd90-4e23-8f48-ab84d28ff4a0</ns19:id>
                                                            <ns19:optionName>retrieveRequest</ns19:optionName>
                                                            <ns19:value>tw.local.retrieveRequest</ns19:value>
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>f08220c7-8fce-4893-8bfd-8a69e4c49641</ns19:id>
                                                            <ns19:optionName>retrieveCustomer</ns19:optionName>
                                                            <ns19:value>tw.local.retrieveCustomer</ns19:value>
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>273e0b48-a16b-4ba4-8fc8-98f32c4d30e5</ns19:id>
                                                            <ns19:optionName>alertMessage</ns19:optionName>
                                                            <ns19:value>tw.local.errorMSG</ns19:value>
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>a6fdef21-d133-48c7-828b-df2a9f18ef59</ns19:id>
                                                            <ns19:optionName>errorVis</ns19:optionName>
                                                            <ns19:value>tw.local.errorVIS</ns19:value>
                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                        </ns19:configData>
                                                        <ns19:viewUUID>64.f547d7e6-17be-4187-b0e1-14f05f5fdfd4</ns19:viewUUID>
                                                        <ns19:binding>tw.local.idcRequest</ns19:binding>
                                                    </ns19:contributions>
                                                </ns19:contentBoxContrib>
                                            </ns19:layoutItem>
                                        </ns19:layout>
                                    </ns19:coachDefinition>
                                </ns3:formDefinition>
                            </ns3:formTask>
                            <ns16:sequenceFlow sourceRef="2025.6d76e0dd-17bb-4e8d-98d1-82e8d0682c86" targetRef="2025.5dab97c3-4bdd-4631-a0f6-e602a9c3ba8c" name="To IDC Request Data" id="2027.5b57c95f-3145-4ff1-b121-54b1b660ed84">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topLeft</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftBottom</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:coachEventBinding id="7b24b46d-3335-4df8-9a4a-01c1b6b8b32f">
                                        <ns3:coachEventPath>Header_View2/submit</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.ce3318bc-ad0a-4809-b3e5-8d0a8f3f6929" targetRef="2025.baab68ba-c57c-48fb-ad2d-8499438e3995" name="To Attachment" id="2027.491dcb01-2d8e-4999-a6f0-8b87817c909a">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:intermediateThrowEvent name="Postpone" id="2025.5c073fba-612b-44a2-b49b-d5bad4a9bc05">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="420" y="258" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                    <ns3:default>2027.53e9259e-a120-43ef-99ca-fb822db0f39d</ns3:default>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.58aa3cd1-28e5-4d95-a115-9a0418d3e55b</ns16:incoming>
                                <ns16:outgoing>2027.53e9259e-a120-43ef-99ca-fb822db0f39d</ns16:outgoing>
                                <ns3:postponeTaskEventDefinition />
                            </ns16:intermediateThrowEvent>
                            <ns16:sequenceFlow sourceRef="2025.6d76e0dd-17bb-4e8d-98d1-82e8d0682c86" targetRef="2025.5c073fba-612b-44a2-b49b-d5bad4a9bc05" name="To Postpone" id="2027.58aa3cd1-28e5-4d95-a115-9a0418d3e55b">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomRight</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:coachEventBinding id="4dd49ae0-3f50-45e2-919b-9dc6b06dcffc">
                                        <ns3:coachEventPath>Header_View2/saveState</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.5c073fba-612b-44a2-b49b-d5bad4a9bc05" targetRef="2025.6d76e0dd-17bb-4e8d-98d1-82e8d0682c86" name="To Create IDC Request" id="2027.53e9259e-a120-43ef-99ca-fb822db0f39d">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>bottomLeft</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" name="tmpIDCrequest" id="2056.c034f8c0-6cfd-445e-a683-0a33aa97f71f">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="false">var autoObject = {};
autoObject.IDCRequestState = "";
autoObject.commodityDescription = "";
autoObject.countryOfOrigin = {};
autoObject.countryOfOrigin.id = 0;
autoObject.countryOfOrigin.code = "";
autoObject.countryOfOrigin.arabicdescription = "";
autoObject.countryOfOrigin.englishdescription = "";
autoObject.appInfo = {};
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = {};
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.productsDetails = {};
autoObject.productsDetails.destinationPort = "";
autoObject.productsDetails.shippingDate = new Date();
autoObject.productsDetails.HSProduct = {};
autoObject.productsDetails.HSProduct.id = 0;
autoObject.productsDetails.HSProduct.code = "";
autoObject.productsDetails.HSProduct.arabicdescription = "";
autoObject.productsDetails.HSProduct.englishdescription = "";
autoObject.productsDetails.incoterms = {};
autoObject.productsDetails.incoterms.id = 0;
autoObject.productsDetails.incoterms.code = "";
autoObject.productsDetails.incoterms.arabicdescription = "";
autoObject.productsDetails.incoterms.englishdescription = "";
autoObject.productsDetails.ACID = "";
autoObject.productsDetails.CBECommodityClassification = {};
autoObject.productsDetails.CBECommodityClassification.id = 0;
autoObject.productsDetails.CBECommodityClassification.code = "";
autoObject.productsDetails.CBECommodityClassification.arabicdescription = "";
autoObject.productsDetails.CBECommodityClassification.englishdescription = "";
autoObject.productsDetails.shipmentMethod = {};
autoObject.productsDetails.shipmentMethod.id = 0;
autoObject.productsDetails.shipmentMethod.code = "";
autoObject.productsDetails.shipmentMethod.arabicdescription = "";
autoObject.productsDetails.shipmentMethod.englishdescription = "";
autoObject.financialDetails = {};
autoObject.financialDetails.isAdvancePaymentsUsed = false;
autoObject.financialDetails.paymentTerms = [];
autoObject.financialDetails.paymentTerms[0] = {};
autoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;
autoObject.financialDetails.paymentTerms[0].installmentDate = new Date();
autoObject.financialDetails.usedAdvancePayment = [];
autoObject.financialDetails.usedAdvancePayment[0] = {};
autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = {};
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = "";
autoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;
autoObject.financialDetails.discountAmt = 0.0;
autoObject.financialDetails.firstInstallementMaturityDate = new Date();
autoObject.financialDetails.facilityAmtInDocCurrency = 0.0;
autoObject.financialDetails.amtSight = 0.0;
autoObject.financialDetails.beneficiaryDetails = {};
autoObject.financialDetails.beneficiaryDetails.account = "";
autoObject.financialDetails.beneficiaryDetails.bank = "";
autoObject.financialDetails.beneficiaryDetails.correspondentRefNum = "";
autoObject.financialDetails.beneficiaryDetails.name = "";
autoObject.financialDetails.beneficiaryDetails.country = {};
autoObject.financialDetails.beneficiaryDetails.country.id = 0;
autoObject.financialDetails.beneficiaryDetails.country.code = "";
autoObject.financialDetails.beneficiaryDetails.country.arabicdescription = "";
autoObject.financialDetails.beneficiaryDetails.country.englishdescription = "";
autoObject.financialDetails.amtDeferredNoAvalized = 0.0;
autoObject.financialDetails.amtPayableByNBE = 0.0;
autoObject.financialDetails.executionHub = {};
autoObject.financialDetails.executionHub.id = 0;
autoObject.financialDetails.executionHub.code = "";
autoObject.financialDetails.executionHub.arabicdescription = "";
autoObject.financialDetails.executionHub.englishdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency = {};
autoObject.financialDetails.sourceOfForeignCurrency.id = 0;
autoObject.financialDetails.sourceOfForeignCurrency.code = "";
autoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency.englishdescription = "";
autoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;
autoObject.financialDetails.documentCurrency = {};
autoObject.financialDetails.documentCurrency.id = 0;
autoObject.financialDetails.documentCurrency.code = "";
autoObject.financialDetails.documentCurrency.arabicdescription = "";
autoObject.financialDetails.documentCurrency.englishdescription = "";
autoObject.financialDetails.daysTillMaturity = 0;
autoObject.financialDetails.tradeFinanceApprovalNumber = "";
autoObject.financialDetails.amountAdvanced = 0.0;
autoObject.financialDetails.paymentAccount = "";
autoObject.financialDetails.tradeFOReferenceNumber = "";
autoObject.financialDetails.documentAmount = 0.0;
autoObject.financialDetails.sourceOfFunds = {};
autoObject.financialDetails.sourceOfFunds.id = 0;
autoObject.financialDetails.sourceOfFunds.code = "";
autoObject.financialDetails.sourceOfFunds.arabicdescription = "";
autoObject.financialDetails.sourceOfFunds.englishdescription = "";
autoObject.financialDetails.chargesAccount = "";
autoObject.financialDetails.CashAmtWithNoCurrency = 0.0;
autoObject.financialDetails.amtDeferredAvalized = 0.0;
autoObject.financialDetails.amtPaidbyOtherBanks = 0.0;
autoObject.financialDetails.cashAmtInDocCurrency = 0.0;
autoObject.IDCRequestType = {};
autoObject.IDCRequestType.id = 0;
autoObject.IDCRequestType.code = "";
autoObject.IDCRequestType.arabicdescription = "";
autoObject.IDCRequestType.englishdescription = "";
autoObject.isIDCWithdrawn = false;
autoObject.IDCRequestStage = "";
autoObject.FCContractNumber = "";
autoObject.billOfLading = [];
autoObject.billOfLading[0] = {};
autoObject.billOfLading[0].date = new Date();
autoObject.billOfLading[0].number = "";
autoObject.importPurpose = {};
autoObject.importPurpose.id = 0;
autoObject.importPurpose.code = "";
autoObject.importPurpose.arabicdescription = "";
autoObject.importPurpose.englishdescription = "";
autoObject.IDCRequestNature = {};
autoObject.IDCRequestNature.id = 0;
autoObject.IDCRequestNature.code = "";
autoObject.IDCRequestNature.arabicdescription = "";
autoObject.IDCRequestNature.englishdescription = "";
autoObject.customerInformation = {};
autoObject.customerInformation.CIFNumber = "";
autoObject.customerInformation.importCardNumber = "";
autoObject.customerInformation.commercialRegistrationNumber = "";
autoObject.customerInformation.customerName = "";
autoObject.customerInformation.isCustomeSanctionedbyCBE = false;
autoObject.customerInformation.CBENumber = "";
autoObject.customerInformation.customerSector = "";
autoObject.customerInformation.facilityType = "";
autoObject.customerInformation.commercialRegistrationOffice = "";
autoObject.customerInformation.taxCardNumber = "";
autoObject.customerInformation.customerType = "";
autoObject.invoices = [];
autoObject.invoices[0] = {};
autoObject.invoices[0].date = new Date();
autoObject.invoices[0].number = "";
autoObject.productCategory = {};
autoObject.productCategory.id = 0;
autoObject.productCategory.code = "";
autoObject.productCategory.arabicdescription = "";
autoObject.productCategory.englishdescription = "";
autoObject.documentsSource = {};
autoObject.documentsSource.id = 0;
autoObject.documentsSource.code = "";
autoObject.documentsSource.arabicdescription = "";
autoObject.documentsSource.englishdescription = "";
autoObject.ParentIDCRequestNumber = "";
autoObject.paymentTerms = {};
autoObject.paymentTerms.id = 0;
autoObject.paymentTerms.code = "";
autoObject.paymentTerms.arabicdescription = "";
autoObject.paymentTerms.englishdescription = "";
autoObject.approvals = {};
autoObject.approvals.CAD = false;
autoObject.approvals.treasury = false;
autoObject.approvals.compliance = false;
autoObject</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns3:formTask name="IDC Request Data" id="2025.49ca7e00-8853-4d3f-8d52-119f94ff3e10">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="659" y="100" width="95" height="70" />
                                    <ns3:validationStayOnPagePaths>DC_Templete1/submit</ns3:validationStayOnPagePaths>
                                    <ns3:preAssignmentScript>tw.local.idcRequest.appInfo.subStatus = "final";</ns3:preAssignmentScript>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.1e0bb62b-d40a-4cb4-8c63-2d4d15c326b6</ns16:incoming>
                                <ns16:incoming>2027.ff715148-f279-4371-849f-de7043fa1d26</ns16:incoming>
                                <ns16:incoming>2027.9cecf069-47c9-41c6-9247-cc005b368801</ns16:incoming>
                                <ns16:incoming>2027.0fd60f96-4c02-43df-831d-0328a3497a99</ns16:incoming>
                                <ns16:outgoing>2027.7c77eb27-c50f-469c-a575-96530c0ae69c</ns16:outgoing>
                                <ns16:outgoing>2027.7d5b995d-f6f6-4fa4-8ed5-7dd8fa484da7</ns16:outgoing>
                                <ns3:formDefinition>
                                    <ns19:coachDefinition>
                                        <ns19:layout>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                <ns19:id>8df2d008-cc3e-4dbd-8aaa-995669d21c9e</ns19:id>
                                                <ns19:layoutItemId>Error_Message1</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>2ea8235b-224e-48ca-80fa-71f8340a23cd</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>Error Message</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>16718ba5-09ec-4457-8d65-dfe5330e33af</ns19:id>
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    <ns19:value />
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>fe4f9c40-1fa3-43a5-89c1-4d90915bc74e</ns19:id>
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    <ns19:value>SHOW</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>a3b0c6ca-29d0-4c37-8e6a-7c28d3c96cc8</ns19:id>
                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>97f90d72-13f5-4223-82ea-b96d5e4890fe</ns19:id>
                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>98c5ce44-992e-4069-8278-7ee7b4308abe</ns19:id>
                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.46690ca1-5521-4e5c-bc9e-ecb22d75ca97</ns19:viewUUID>
                                            </ns19:layoutItem>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                <ns19:id>b6af9762-fe4c-41cb-8e81-7cecfddcec7a</ns19:id>
                                                <ns19:layoutItemId>DC_Templete1</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>f5d4d26d-dd96-49ab-88e1-aa01877abce4</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>DC Templete</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>d66194f3-b584-49c9-8e5f-4c1b4f2a0129</ns19:id>
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    <ns19:value />
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>79b8f2b1-85b1-4366-8a15-1b8caabaf62a</ns19:id>
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    <ns19:value>SHOW</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>8e14b6cf-991e-4974-8d12-8d543892c039</ns19:id>
                                                    <ns19:optionName>buttonName</ns19:optionName>
                                                    <ns19:value>Submit</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>398671fe-2bf8-4d0b-8f11-9bfaad8ed4d2</ns19:id>
                                                    <ns19:optionName>stepLog</ns19:optionName>
                                                    <ns19:value>tw.local.idcRequest.stepLog</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>18d6a862-352f-4bd8-88db-f62a6d5154c1</ns19:id>
                                                    <ns19:optionName>action</ns19:optionName>
                                                    <ns19:value>tw.local.action[]</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>8b4c710e-eefe-41b9-8b31-5c1de12d70c9</ns19:id>
                                                    <ns19:optionName>selectedAction</ns19:optionName>
                                                    <ns19:value>tw.local.selectedAction</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>841c020c-78f8-4408-80c8-137819769042</ns19:id>
                                                    <ns19:optionName>hasApprovals</ns19:optionName>
                                                    <ns19:value>tw.local.haseApprovals</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>c296b536-aea9-42ea-8660-8b14151154bb</ns19:id>
                                                    <ns19:optionName>hasReturnReason</ns19:optionName>
                                                    <ns19:value>tw.local.hasReturnReason</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>e7a842a3-6fcf-4a52-8fdf-bdcf506e80a0</ns19:id>
                                                    <ns19:optionName>approvals</ns19:optionName>
                                                    <ns19:value>tw.local.idcRequest.approvals</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>71503b8f-7e42-4118-8a41-8d8afeab6895</ns19:id>
                                                    <ns19:optionName>validationTabs</ns19:optionName>
                                                    <ns19:value>tw.local.invalidTabs[]</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>c0fc8584-3380-4c61-88ce-25574416c45e</ns19:id>
                                                    <ns19:optionName>approvalsReadOnly</ns19:optionName>
                                                    <ns19:value>false</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>b298adb9-b84a-4941-8652-b8f966f93b51</ns19:id>
                                                    <ns19:optionName>invalidTabs</ns19:optionName>
                                                    <ns19:value>tw.local.invalidTabs[]</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>6827a42b-3438-4420-8d60-812211e849d1</ns19:id>
                                                    <ns19:optionName>validationMessage</ns19:optionName>
                                                    <ns19:value>tw.local.validationMessage</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>8696f490-7a82-4247-8129-e3ba4e007c2b</ns19:id>
                                                    <ns19:optionName>isCAD</ns19:optionName>
                                                    <ns19:value>false</ns19:value>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</ns19:viewUUID>
                                                <ns19:binding>tw.local.idcRequest.appInfo</ns19:binding>
                                                <ns19:contentBoxContrib>
                                                    <ns19:id>a1e05320-692a-4d33-81ab-c07215777f1b</ns19:id>
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        <ns19:id>0717c90f-3cf5-4c4f-8185-215410509b7a</ns19:id>
                                                        <ns19:layoutItemId>Tab_section1</ns19:layoutItemId>
                                                        <ns19:configData>
                                                            <ns19:id>d10ec0a1-3058-49e2-8daf-9739f827296b</ns19:id>
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            <ns19:value>Tab section</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>87494c05-2452-4cb6-83b2-6181bd7a43f5</ns19:id>
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            <ns19:value />
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>53ba771a-b691-4cb0-80f3-810b5146b50e</ns19:id>
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            <ns19:value>SHOW</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>bfe7bb53-ff9c-49f3-87e3-a02f905a0c70</ns19:id>
                                                            <ns19:optionName>colorStyle</ns19:optionName>
                                                            <ns19:value>D</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>3efa1206-81c7-4b0e-8c30-6f86b2a2f49e</ns19:id>
                                                            <ns19:optionName>@visibility</ns19:optionName>
                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"REQUIRED"}]}</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>02538a2c-30b1-4d99-8ed4-76587e836ca1</ns19:id>
                                                            <ns19:optionName>defaultPaneIdx</ns19:optionName>
                                                            <ns19:value />
                                                            <ns19:valueType>static</ns19:valueType>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>9740a58b-386d-4555-84bd-83f7f74eff37</ns19:id>
                                                            <ns19:optionName>eventON_LOAD</ns19:optionName>
                                                            <ns19:value>&#xD;
&#xD;
</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>1b0e4b67-780a-44da-8d63-4a9cbf33bbd4</ns19:id>
                                                            <ns19:optionName>tabsStyle</ns19:optionName>
                                                            <ns19:value>{"isResponsiveData":true,"values":[]}</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>c87d0c28-2cdc-411c-8e92-6c54981a36f4</ns19:id>
                                                            <ns19:optionName>sizeStyle</ns19:optionName>
                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"X"}]}</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:viewUUID>64.c05b439f-a4bd-48b0-8644-fa3b59052217</ns19:viewUUID>
                                                        <ns19:binding />
                                                        <ns19:contentBoxContrib>
                                                            <ns19:id>678ab998-1239-4d5c-8cd0-3fd22ed49140</ns19:id>
                                                            <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>6de938d3-7ad8-4abb-8950-153cb0fa0086</ns19:id>
                                                                <ns19:layoutItemId>0</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>e96d13a1-5f98-440c-864a-51419fee15ac</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Customer Information</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>06bc7d1e-9ed0-440f-8308-aab6bd2e23c1</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>08401cec-**************-d49cdbda2f3b</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>88754e9c-ff6f-43f5-8f49-6510c65d158c</ns19:id>
                                                                    <ns19:optionName>validationTabs</ns19:optionName>
                                                                    <ns19:value>tw.local.invalidTabs[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>0f4ad413-662d-43ae-8511-38782f201238</ns19:id>
                                                                    <ns19:optionName>instanceview</ns19:optionName>
                                                                    <ns19:value>false</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>40d98c16-f213-4ef9-8b7a-491d90c43643</ns19:id>
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    <ns19:value>tw.local.alertMessage</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.656cc232-8247-43c3-9481-3cc7a9aec2e6</ns19:viewUUID>
                                                                <ns19:binding>tw.local.idcRequest.customerInformation</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>0568f87f-5223-4be5-8f75-18301042981a</ns19:id>
                                                                <ns19:layoutItemId>1</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>007b54be-3009-4e40-8063-66006bd2c4d7</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Basic Details</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>02805ab2-bc79-4baa-89e4-930e049cbd02</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>84bdf173-e130-44c2-8baf-d75c0e9a8c48</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>cbaefb68-8984-40da-879e-e3cb662f9290</ns19:id>
                                                                    <ns19:optionName>addBill</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>901a5aa3-a8fc-46bd-831c-b7f701bc32d4</ns19:id>
                                                                    <ns19:optionName>deleteBill</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>2d0a7b55-0937-4a6d-8158-876a1c17c108</ns19:id>
                                                                    <ns19:optionName>addInvoice</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>8217ac1e-fe7b-46c0-81cd-53db4efc4242</ns19:id>
                                                                    <ns19:optionName>deleteInvoice</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>4e7ab343-6319-4368-894e-4a6719f0bf3e</ns19:id>
                                                                    <ns19:optionName>hasWithdraw</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>7a044f62-b215-443d-8d00-3e0a65b1ce35</ns19:id>
                                                                    <ns19:optionName>havePaymentTerm</ns19:optionName>
                                                                    <ns19:value>tw.local.havePaymentTerms</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>6aa1e8a2-77b2-4b27-85fa-354122df599d</ns19:id>
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>40ae9c21-85d1-4e2a-8824-dd3a7188d758</ns19:id>
                                                                    <ns19:optionName>billExist</ns19:optionName>
                                                                    <ns19:value>0</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>89ece940-ca07-4064-824e-a5a38cb503a9</ns19:id>
                                                                    <ns19:optionName>invoiceExist</ns19:optionName>
                                                                    <ns19:value>0</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>7c92d517-81d8-4d78-875f-5d142e9d81b0</ns19:id>
                                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>1776b765-29be-4f72-8b2a-faebfa909f30</ns19:id>
                                                                    <ns19:optionName>idcContract</ns19:optionName>
                                                                    <ns19:value>tw.local.idcContract</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>6f3497ed-2f96-4db7-81c0-a9e2ff71d2c2</ns19:id>
                                                                    <ns19:optionName>InterestAndChargesList</ns19:optionName>
                                                                    <ns19:value>tw.local.InterestAndChargesList[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>cf1607f3-4591-49b0-861e-fc39e936a675</ns19:id>
                                                                    <ns19:optionName>stage</ns19:optionName>
                                                                    <ns19:value>tw.local.idcRequest.IDCRequestStage</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>563876d1-1725-4e50-8846-3c5278d7c00f</ns19:id>
                                                                    <ns19:optionName>exRate</ns19:optionName>
                                                                    <ns19:value>tw.local.exRate</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>299237f5-44b6-4df2-871d-38f4084318b7</ns19:id>
                                                                    <ns19:optionName>adviceCodeList</ns19:optionName>
                                                                    <ns19:value>tw.local.adviceCodeList[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.3d13e9b9-bcc9-4fe9-ac80-3c287115f705</ns19:viewUUID>
                                                                <ns19:binding>tw.local.idcRequest</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>99bde13d-42c9-4107-8f3b-3e288f058005</ns19:id>
                                                                <ns19:layoutItemId>2</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>5b442036-93a2-488f-8f14-08ef7166c4bb</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Financial Details - Branch</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>4362b91a-f56f-4f8e-855d-06c65df3e0fb</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>634753d7-f4e5-4594-81d5-1dfb5d6b598e</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>46bff10b-24a0-4203-80fe-f7a5cee682b5</ns19:id>
                                                                    <ns19:optionName>@overflow</ns19:optionName>
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"visible"}]}</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>527f7fb4-d9ae-49d3-8874-aac7dcdcd3da</ns19:id>
                                                                    <ns19:optionName>@visibility.script</ns19:optionName>
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>c7f708ed-de24-4756-83c4-60940332531d</ns19:id>
                                                                    <ns19:optionName>advancePaymentsUsedOption</ns19:optionName>
                                                                    <ns19:value>tw.local.advancePaymentsUsedVis</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>3d15c48c-a5da-4be2-80df-1750e9be38f9</ns19:id>
                                                                    <ns19:optionName>docAmount</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>eee8f66f-f156-49ec-8e97-0ee29e5e5e03</ns19:id>
                                                                    <ns19:optionName>currncy</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>cf34bb0e-415f-477b-86ae-58118b761e99</ns19:id>
                                                                    <ns19:optionName>CIF</ns19:optionName>
                                                                    <ns19:value>tw.local.idcRequest.customerInformation.CIFNumber</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>604d05ac-df9b-4e73-8e1f-************</ns19:id>
                                                                    <ns19:optionName>accountsList</ns19:optionName>
                                                                    <ns19:value>tw.local.accountsList[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>39404d9b-de4d-4ef2-8cea-1d97691da137</ns19:id>
                                                                    <ns19:optionName>requestType</ns19:optionName>
                                                                    <ns19:value>tw.local.idcRequest.IDCRequestType.englishdescription</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>f457b721-663a-47b5-8dbb-905ff6248c5b</ns19:id>
                                                                    <ns19:optionName>haveAmountAdvanced</ns19:optionName>
                                                                    <ns19:value>DEFAULT</ns19:value>
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>7652ca7b-05a1-4b0b-8764-cd495f7acb1b</ns19:id>
                                                                    <ns19:optionName>tmpUsedAdvancePayment</ns19:optionName>
                                                                    <ns19:value>tw.local.tmpUsedAdvancePayment</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>7d49223a-d4bd-4c0a-87f2-cd3c4e6d915a</ns19:id>
                                                                    <ns19:optionName>isChecker</ns19:optionName>
                                                                    <ns19:value>false</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>7fd12a0c-0d63-4879-8fcf-6492a7baef4b</ns19:id>
                                                                    <ns19:optionName>currencyVis</ns19:optionName>
                                                                    <ns19:value>tw.local.currencyVis</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>6e25e78c-2f01-41cd-8352-57645f283106</ns19:id>
                                                                    <ns19:optionName>requestID</ns19:optionName>
                                                                    <ns19:value>tw.local.idcRequest.appInfo.instanceID</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>4c464311-1f0d-4011-8ba4-dbb1e40ceee1</ns19:id>
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>47513ac0-aba3-49d3-8516-7e73fa4fd709</ns19:id>
                                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.74d3cb97-ad59-4249-847b-a21122e44b22</ns19:viewUUID>
                                                                <ns19:binding>tw.local.idcRequest.financialDetails</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>de643479-1690-4c6d-8f83-ad6e0681badf</ns19:id>
                                                                <ns19:layoutItemId>3</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>446d8e7a-19f4-4eb6-8ccd-8c627254440e</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Attachment</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>5c709e85-3d08-4826-8fef-213d52428b21</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>9c1fde63-9e17-4aa0-88cf-06f50fa1fdd2</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>91550808-62ee-439b-8d49-d43d230cd559</ns19:id>
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"EDITABLE"}]}</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>d56ef36b-c2ed-4cae-8567-2b85ddbc7994</ns19:id>
                                                                    <ns19:optionName>canUpdate</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>a71fc15f-e77a-433b-87bc-b64fea8cc2d4</ns19:id>
                                                                    <ns19:optionName>canCreate</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>5e016d6f-d59d-42c3-8d68-d00de21ee175</ns19:id>
                                                                    <ns19:optionName>fullPath</ns19:optionName>
                                                                    <ns19:value>tw.local.fullPath</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>b639b6be-bc96-4d93-88fe-7324dc9a4df1</ns19:id>
                                                                    <ns19:optionName>canDelete</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>2f5ac9c7-8d5f-4693-8639-c9e87a91dd43</ns19:id>
                                                                    <ns19:optionName>default_properties</ns19:optionName>
                                                                    <ns19:value>tw.local.default_properties[]</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>43c07595-f33b-49fa-8e3a-ea1de02fb75a</ns19:id>
                                                                    <ns19:optionName>parentPath</ns19:optionName>
                                                                    <ns19:value>tw.local.parentPath</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>62e77e7e-fa86-4836-8c1e-34e8e6ed6fe0</ns19:id>
                                                                    <ns19:optionName>cmisQuery</ns19:optionName>
                                                                    <ns19:value>tw.local.cmisQuery</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>62fbdc76-3a7e-4759-840f-b13909a83bef</ns19:id>
                                                                    <ns19:optionName>ECMproperties</ns19:optionName>
                                                                    <ns19:value>tw.local.ECMproperties</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>2ae19864-5592-4b0b-84d3-1ef7c1dbdd21</ns19:id>
                                                                    <ns19:optionName>visiable</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7</ns19:viewUUID>
                                                                <ns19:binding>tw.local.attachment[]</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>40d96360-b51c-4f35-8f35-f015834bcd92</ns19:id>
                                                                <ns19:layoutItemId>Party1</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>fcb76d01-c067-4f7f-8115-acdb8284ec27</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Party</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>f831e657-2429-46bd-88c5-fd09f0fbfd18</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>a5caac1b-7e5e-4256-876a-323948c663e3</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.42200ef0-f4f7-4d44-aa41-e48db0e2da1f</ns19:viewUUID>
                                                                <ns19:binding>tw.local.idcContract.party[]</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>2cff5a7e-bdf6-414e-8045-7682a651df97</ns19:id>
                                                                <ns19:layoutItemId>4</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>93f9dfcb-a756-4925-8c74-a3d1c2e14b73</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>History </ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>fad74bc8-047f-4278-86ba-95105c74445d</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>097a0a47-4a65-463e-8342-8cc0f3025534</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>88cc43f7-f3f4-4bf9-8b01-33c086a02fdc</ns19:id>
                                                                    <ns19:optionName>panelCollapse</ns19:optionName>
                                                                    <ns19:value>false</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>dd280358-ec16-48df-8681-23efab9ac302</ns19:id>
                                                                    <ns19:optionName>historyVisFlag</ns19:optionName>
                                                                    <ns19:value>None</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>19215b9b-b70a-40c2-86d2-b131e36a3ac5</ns19:id>
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.6ac8a533-b05a-45a1-9e46-1c4542e6b8be</ns19:viewUUID>
                                                                <ns19:binding>tw.local.idcRequest.appLog[]</ns19:binding>
                                                            </ns19:contributions>
                                                        </ns19:contentBoxContrib>
                                                    </ns19:contributions>
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        <ns19:id>8e0e729d-33d4-47f1-87d3-c174871b8d66</ns19:id>
                                                        <ns19:layoutItemId>Data1</ns19:layoutItemId>
                                                        <ns19:configData>
                                                            <ns19:id>0c139319-0582-491d-8bbe-e304059e0717</ns19:id>
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            <ns19:value>Data</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>c605b490-0ba1-4790-82e0-c97e37f5e69d</ns19:id>
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            <ns19:value />
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>3d05a0f0-6063-48d5-844b-a8529df88913</ns19:id>
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            <ns19:value>SHOW</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:viewUUID>64.9b679256-e93b-4400-89f2-bd15b0c5578d</ns19:viewUUID>
                                                        <ns19:binding>tw.local.invalidTabs[]</ns19:binding>
                                                    </ns19:contributions>
                                                </ns19:contentBoxContrib>
                                            </ns19:layoutItem>
                                        </ns19:layout>
                                    </ns19:coachDefinition>
                                </ns3:formDefinition>
                            </ns3:formTask>
                            <ns16:sequenceFlow sourceRef="2025.49ca7e00-8853-4d3f-8d52-119f94ff3e10" targetRef="2025.6f1cd112-951d-4cfc-af36-d0c02ea94f18" name="To Set Status " id="2027.7c77eb27-c50f-469c-a575-96530c0ae69c">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomRight</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:coachEventBinding id="77a742fe-7df4-4267-9eb8-dfe2a48c754c">
                                        <ns3:coachEventPath>DC_Templete1/saveState</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.49ca7e00-8853-4d3f-8d52-119f94ff3e10" targetRef="2025.456c9026-c887-42e9-aa46-c2db3a83a3f6" name="To validate accounts and attachment" id="2027.7d5b995d-f6f6-4fa4-8ed5-7dd8fa484da7">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:coachEventBinding id="015d572e-2ba2-45a0-8728-866353b792cd">
                                        <ns3:coachEventPath>DC_Templete1/submit</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.70bea525-3044-47fb-af1d-24678fd9aa53" name="validation" id="2025.456c9026-c887-42e9-aa46-c2db3a83a3f6">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="739" y="-5" width="95" height="70" color="#95D087" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.4aa6a2cc-3485-4d95-8093-0e7dd7afac2a</ns16:incoming>
                                <ns16:incoming>2027.7d5b995d-f6f6-4fa4-8ed5-7dd8fa484da7</ns16:incoming>
                                <ns16:outgoing>2027.70bea525-3044-47fb-af1d-24678fd9aa53</ns16:outgoing>
                                <ns16:script>tw.local.message = "";&#xD;
tw.local.validationMessage = "";&#xD;
var mandatoryTriggered = false;&#xD;
var tempLength = 0 ;&#xD;
tw.local.invalidTabs = [];&#xD;
/*&#xD;
* =========================================================================================================&#xD;
*  &#xD;
* Add a coach validation error &#xD;
* 		&#xD;
* EX:	addError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');&#xD;
*&#xD;
* =========================================================================================================&#xD;
*/&#xD;
&#xD;
function addError(fieldName , controlMessage , validationMessage , fromMandatory)&#xD;
{&#xD;
	tw.system.coachValidation.addValidationError(fieldName, controlMessage);&#xD;
	fromMandatory &amp;&amp; mandatoryTriggered ? "" : tw.local.message += "&lt;li dir='rtl'&gt;" + validationMessage + "&lt;/li&gt;";&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is in the past and the last variable to exclude today&#xD;
*	&#xD;
* EX:	notPastDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)&#xD;
*&#xD;
* =============================================================================================================================&#xD;
*/&#xD;
&#xD;
function notPastDate(date , fieldName , controlMessage , validationMessage , exclude)&#xD;
{&#xD;
	if (exclude)&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &lt;= new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;	&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &lt; new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is between two dates&#xD;
*	&#xD;
* EX:	dateBetween(tw.local.date , date1 , date2 , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =============================================================================================================================&#xD;
*/&#xD;
&#xD;
function dateBetween(field , date1 , date2 , fieldName , controlMessage , validationMessage)&#xD;
{&#xD;
	if(field &lt; date1 &amp;&amp; field &gt; date2)&#xD;
	{&#xD;
	 	return true;&#xD;
	}&#xD;
	addError(fieldName , controlMessage , validationMessage);&#xD;
	return false;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ===============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is in the future and the last varaible to exculde today&#xD;
*	&#xD;
* EX:	notFutureDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)&#xD;
*&#xD;
* ===============================================================================================================================&#xD;
*/&#xD;
&#xD;
function notFutureDate(date , fieldName ,  controlMessage , validationMessage , exculde)&#xD;
{&#xD;
	if (exculde)&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &gt;= new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;	&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &gt; new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
&#xD;
/*&#xD;
* =================================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the string length is less than given length&#xD;
*	&#xD;
* EX:	minLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =================================================================================================================================&#xD;
*/&#xD;
&#xD;
function minLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the string length is greater than given length&#xD;
*	&#xD;
* EX:	maxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function maxLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &gt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the number is greater than given value&#xD;
*	&#xD;
* EX:	maxNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function maxNumber(field , fieldName , max , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field &gt; max)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the number is less than given value&#xD;
*	&#xD;
* EX:	minNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function minNumber(field , fieldName , min , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field &lt; min)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the field is null 'Mandatory'&#xD;
*	&#xD;
* EX:	notNull(tw.local.name , 'tw.local.name')&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
&#xD;
function mandatory(field , fieldName)&#xD;
{&#xD;
	if (field == null || field == undefined )&#xD;
	{&#xD;
		addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
		mandatoryTriggered = true;&#xD;
		return false;&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		switch (typeof field)&#xD;
		{&#xD;
			case "string":&#xD;
				if (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)&#xD;
				{&#xD;
					addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
			case "number":&#xD;
				if (field == 0.0)&#xD;
				{&#xD;
					addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
		}&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a validation mark on the tab if there is an error&#xD;
*	&#xD;
* EX:	validateTab(1)&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
&#xD;
function validateTab(index , tabName)&#xD;
{&#xD;
	if (tw.system.coachValidation.validationErrors.length &gt; tempLength)&#xD;
	{&#xD;
		if (tw.local.validationMessage.length == 0) {&#xD;
			tw.local.validationMessage += "&lt;p&gt;" + "Please complete fields in the following tabs:" + "&lt;/p&gt;";&#xD;
		}&#xD;
		tw.local.validationMessage += "&lt;li&gt;" + tabName + "&lt;/li&gt;";&#xD;
		tempLength = tw.system.coachValidation.validationErrors.length ;&#xD;
		tw.local.invalidTabs[tw.local.invalidTabs.length] = index;&#xD;
	}	&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a validation mark on the tab if there is an error&#xD;
*	&#xD;
* EX:	validateTab(1)&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
//function validateDecimal(field, fieldName, controlMessage , validationMessage) {&#xD;
//   regexString = `^\\d{1,12}(\\.\\d{1,12})?$`;&#xD;
//   regex = new RegExp(regexString);&#xD;
//&#xD;
//  if (!regex.test(field))&#xD;
//	{&#xD;
//		addError(fieldName , controlMessage , validationMessage);&#xD;
//		return false;&#xD;
//	}&#xD;
//	return true;&#xD;
//}&#xD;
&#xD;
//-----------------------------------customer info[0]--------------------------------------------------------&#xD;
mandatory(tw.local.idcRequest.customerInformation.facilityType.id,"tw.local.idcRequest.customerInformation.facilityType.id");&#xD;
mandatory(tw.local.idcRequest.customerInformation.importCardNumber,"tw.local.idcRequest.customerInformation.importCardNumber");&#xD;
//validateTab(0, "Customer Information");&#xD;
&#xD;
//--------------------------------------basic details[1]----------------------------------------------------&#xD;
&#xD;
//mandatory(tw.local.idcRequest.importPurpose.englishdescription , "tw.local.idcRequest.importPurpose.englishdescription");&#xD;
//mandatory(tw.local.idcRequest.paymentTerms.englishdescription,"tw.local.idcRequest.paymentTerms.englishdescription");&#xD;
//mandatory(tw.local.idcRequest.documentsSource.englishdescription,"tw.local.idcRequest.documentsSource.englishdescription");&#xD;
//mandatory(tw.local.idcRequest.productCategory.englishdescription,"tw.local.idcRequest.productCategory.englishdescription");&#xD;
//mandatory(tw.local.idcRequest.commodityDescription,"tw.local.idcRequest.commodityDescription");&#xD;
//if (tw.local.idcRequest.invoices.length&gt;0){&#xD;
//	for (var i=0; i&lt;tw.local.idcRequest.invoices.length; i++) {&#xD;
//		mandatory(tw.local.idcRequest.invoices[i].number,"tw.local.idcRequest.invoices["+i+"].number");&#xD;
//		mandatory(tw.local.idcRequest.invoices[i].date,"tw.local.idcRequest.invoices["+i+"].date");&#xD;
//	}&#xD;
//}&#xD;
//if(tw.local.idcRequest.billOfLading.length&gt;0){&#xD;
//	for (var i=0; i&lt;tw.local.idcRequest.billOfLading.length; i++) {&#xD;
//		mandatory(tw.local.idcRequest.billOfLading[i].number,"tw.local.idcRequest.billOfLading["+i+"].number");&#xD;
//		mandatory(tw.local.idcRequest.billOfLading[i].date,"tw.local.idcRequest.billOfLading["+i+"].date");&#xD;
//	}&#xD;
//}&#xD;
//mandatory(tw.local.idcRequest.countryOfOrigin.code,"tw.local.idcRequest.countryOfOrigin.code");&#xD;
//validateTab(1, "Basic Details");&#xD;
&#xD;
//-------------------------------------financial Details[2]---------------------------------------------------------&#xD;
mandatory(tw.local.idcRequest.financialDetails.documentAmount,"tw.local.idcRequest.financialDetails.documentAmount");&#xD;
//validateDecimal(tw.local.idcRequest.financialDetails.documentAmount, "tw.local.idcRequest.financialDetails.documentAmount", "max length is 14" , "max length is 14");&#xD;
//minNumber(tw.local.idcRequest.financialDetails.documentAmount , "tw.local.idcRequest.financialDetails.documentAmount" , 0.01 , "must be more than 0" , "must be more than 0");&#xD;
//&#xD;
//mandatory(tw.local.idcRequest.financialDetails.chargesAccount,"tw.local.idcRequest.financialDetails.chargesAccount");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.paymentAccount,"tw.local.idcRequest.financialDetails.paymentAccount");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.documentCurrency.code,"tw.local.idcRequest.financialDetails.documentCurrency.code");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.id,"tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.id");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.sourceOfFunds.id,"tw.local.idcRequest.financialDetails.sourceOfFunds.id");&#xD;
//validateTab(2,"Financial Details Branch");&#xD;
&#xD;
//-------------------------------------app info--------------------------------------------------------------&#xD;
//mandatory(tw.local.selectedAction,"tw.local.selectedAction");&#xD;
//if (tw.local.selectedAction == tw.epv.Action.cancelReques+"") {&#xD;
//	mandatory(tw.local.idcRequest.stepLog.comment , "tw.local.idcRequest.stepLog.comment");&#xD;
//}</ns16:script>
                            </ns16:scriptTask>
                            <ns16:sequenceFlow sourceRef="2025.456c9026-c887-42e9-aa46-c2db3a83a3f6" targetRef="2025.533faa47-959b-43f4-b1dd-4360bad3ecc5" name="To Have Errors" id="2027.70bea525-3044-47fb-af1d-24678fd9aa53">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:exclusiveGateway default="2027.37c1287e-8454-48d6-8236-610248cf002a" name="Have Errors" id="2025.533faa47-959b-43f4-b1dd-4360bad3ecc5">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="861" y="14" width="32" height="32" />
                                    <ns3:preAssignmentScript>if (tw.local.errorExist || (tw.system.coachValidation.validationErrors.length &gt; 0 )) {&#xD;
	tw.local.validation = false;&#xD;
}&#xD;
else{&#xD;
	tw.local.validation = true;&#xD;
}</ns3:preAssignmentScript>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.70bea525-3044-47fb-af1d-24678fd9aa53</ns16:incoming>
                                <ns16:outgoing>2027.37c1287e-8454-48d6-8236-610248cf002a</ns16:outgoing>
                                <ns16:outgoing>2027.1e0bb62b-d40a-4cb4-8c63-2d4d15c326b6</ns16:outgoing>
                            </ns16:exclusiveGateway>
                            <ns16:sequenceFlow sourceRef="2025.533faa47-959b-43f4-b1dd-4360bad3ecc5" targetRef="2025.48434093-edd9-4b92-8b57-feca505a198d" name="No" id="2027.37c1287e-8454-48d6-8236-610248cf002a">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.533faa47-959b-43f4-b1dd-4360bad3ecc5" targetRef="2025.49ca7e00-8853-4d3f-8d52-119f94ff3e10" name="Yes" id="2027.1e0bb62b-d40a-4cb4-8c63-2d4d15c326b6">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.validation	  ==	  false</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="message" id="2056.5487b7be-c459-4495-9b9b-2f847681ad5a">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="false">""</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.d029fbf9-335e-4245-b721-500bb668b62b" name="Attachment" id="2025.baab68ba-c57c-48fb-ad2d-8499438e3995">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="212" y="155" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.491dcb01-2d8e-4999-a6f0-8b87817c909a</ns16:incoming>
                                <ns16:outgoing>2027.d029fbf9-335e-4245-b721-500bb668b62b</ns16:outgoing>
                                <ns16:script>&#xD;
if (tw.local.attachment == null || tw.local.attachment == undefined) {&#xD;
	tw.local.attachment = []; &#xD;
tw.local.attachment[0] = {};&#xD;
tw.local.attachment[0].name = "Customer Request";&#xD;
tw.local.attachment[0].description = "Customer Request";&#xD;
tw.local.attachment[0].arabicName = "طلب العميل" ;&#xD;
&#xD;
tw.local.attachment[1] = {};&#xD;
tw.local.attachment[1].name = "Correspondent cover letter";&#xD;
tw.local.attachment[1].description = "Correspondent cover letter";&#xD;
tw.local.attachment[1].arabicName = "خطاب المراسل" ;&#xD;
&#xD;
tw.local.attachment[2] = {};&#xD;
tw.local.attachment[2].name = "Invoice";&#xD;
tw.local.attachment[2].description = "Invoice";&#xD;
tw.local.attachment[2].arabicName = "فاتورة" ;&#xD;
&#xD;
tw.local.attachment[3] = {};&#xD;
tw.local.attachment[3].name = "Transport document";&#xD;
tw.local.attachment[3].description = "Transport document";&#xD;
tw.local.attachment[3].arabicName = "مستند النقل" ;&#xD;
&#xD;
tw.local.attachment[4] = {};&#xD;
tw.local.attachment[4].name = "Packing list";&#xD;
tw.local.attachment[4].description = "Packing list";&#xD;
tw.local.attachment[4].arabicName = "قائمة التعبئة" ;&#xD;
&#xD;
tw.local.attachment[5] = {};&#xD;
tw.local.attachment[5].name = "Weight list";&#xD;
tw.local.attachment[5].description = "Weight list"&#xD;
tw.local.attachment[5].arabicName = "قائمة الاوزان" ;&#xD;
&#xD;
tw.local.attachment[6] = {};&#xD;
tw.local.attachment[6].name = "Certificate of origin";&#xD;
tw.local.attachment[6].description = "Certificate of origin";&#xD;
tw.local.attachment[6].arabicName = "شهادة المنشأ" ;&#xD;
&#xD;
tw.local.attachment[7] = {};&#xD;
tw.local.attachment[7].name = "Certificate of analysis";&#xD;
tw.local.attachment[7].description = "Certificate of analysis";&#xD;
tw.local.attachment[7].arabicName = "شهادة التحليل" ;&#xD;
&#xD;
tw.local.attachment[8] = {};&#xD;
tw.local.attachment[8].name = "Inspection certificate";&#xD;
tw.local.attachment[8].description = "Inspection certificate";&#xD;
tw.local.attachment[8].arabicName = "شهادة التفتيش" ;&#xD;
&#xD;
tw.local.attachment[9] = {};&#xD;
tw.local.attachment[9].name = "Insurance policy / certificate";&#xD;
tw.local.attachment[9].description = "Insurance policy / certificate";&#xD;
tw.local.attachment[9].arabicName = "شهادة / بوليصة التأمين" ;&#xD;
&#xD;
tw.local.attachment[10] = {};&#xD;
tw.local.attachment[10].name = "Bill of exchange/draft";&#xD;
tw.local.attachment[10].description = "Bill of exchange/draft";&#xD;
tw.local.attachment[10].arabicName = "الكمبيالة" ;&#xD;
&#xD;
tw.local.attachment[11] = {};&#xD;
tw.local.attachment[11].name = "Compliance ticket";&#xD;
tw.local.attachment[11].description = "Compliance ticket";&#xD;
tw.local.attachment[11].arabicName = "موافقة الإلتزام" ;&#xD;
&#xD;
tw.local.attachment[12] = {};&#xD;
tw.local.attachment[12].name = "Form 4";&#xD;
tw.local.attachment[12].description = "Form 4";&#xD;
tw.local.attachment[12].arabicName = "نموذج 4 للمستوردين" ;&#xD;
&#xD;
tw.local.attachment[13] = {};&#xD;
tw.local.attachment[13].name = "Customs Letter";&#xD;
tw.local.attachment[13].description = "Customs Letter";&#xD;
tw.local.attachment[13].arabicName = "خطاب الجمارك" ;&#xD;
}</ns16:script>
                            </ns16:scriptTask>
                            <ns16:sequenceFlow sourceRef="2025.baab68ba-c57c-48fb-ad2d-8499438e3995" targetRef="2025.6745a6d9-029a-4342-833a-dd2d269c35ae" name="To Coach" id="2027.d029fbf9-335e-4245-b721-500bb668b62b">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:intermediateThrowEvent name="Postpone " id="2025.6f1cd112-951d-4cfc-af36-d0c02ea94f18">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="701" y="227" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                    <ns3:default>2027.ff715148-f279-4371-849f-de7043fa1d26</ns3:default>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.7c77eb27-c50f-469c-a575-96530c0ae69c</ns16:incoming>
                                <ns16:outgoing>2027.ff715148-f279-4371-849f-de7043fa1d26</ns16:outgoing>
                                <ns3:postponeTaskEventDefinition />
                            </ns16:intermediateThrowEvent>
                            <ns16:sequenceFlow sourceRef="2025.6f1cd112-951d-4cfc-af36-d0c02ea94f18" targetRef="2025.49ca7e00-8853-4d3f-8d52-119f94ff3e10" name="To IDC Request Data" id="2027.ff715148-f279-4371-849f-de7043fa1d26">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>leftCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:callActivity calledElement="1.bd478cbf-52d1-4705-9fe4-6868408a5256" default="2027.6afcb867-973e-4371-9949-30f99d014018" name="Initialization Service" id="2025.bf1e6e71-4a30-4575-9747-448f0c391594">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="76" y="155" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.6afcb867-973e-4371-9949-30f99d014018</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.cd125a62-2dbd-4b4b-b101-03ec36dbab19</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67">tw.local.idcRequest</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.634377a2-f8f1-4a92-8d1c-99eaae72b7bf</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.action</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.23a2f68c-da9d-492b-8b22-d384b35cdca5</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1">tw.local.idcContract</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.4d4ced80-1e82-4190-b046-e12e920ce53c</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67">tw.local.idcRequest</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.2df5550b-b2e8-4f83-8655-1a7230625849</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.action</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.cdee38a2-a864-497e-8737-402567eae371</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.8e43443b-2886-4150-8230-41491f5d9a6b</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1">tw.local.idcContract</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:sequenceFlow sourceRef="2025.bf1e6e71-4a30-4575-9747-448f0c391594" targetRef="2025.74724f89-c330-4e84-afe5-49cce26e863b" name="To Client-Side Script" id="2027.6afcb867-973e-4371-9949-30f99d014018">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.20df3315-b455-438a-bae4-cf74708c912a" name="validation and set request name" id="2025.5dab97c3-4bdd-4631-a0f6-e602a9c3ba8c">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="486" y="-117" width="95" height="70" color="#95D087" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.5b57c95f-3145-4ff1-b121-54b1b660ed84</ns16:incoming>
                                <ns16:outgoing>2027.20df3315-b455-438a-bae4-cf74708c912a</ns16:outgoing>
                                <ns16:script>tw.local.idcRequest.appInfo.requestName = tw.local.idcRequest.IDCRequestNature.englishdescription +" - "+ tw.local.idcRequest.IDCRequestType.englishdescription&#xD;
if (tw.local.idcRequest.IDCRequestType.englishdescription == "Advance Payment" || tw.local.idcRequest.IDCRequestType.englishdescription == "IDC Acknowledgement" || tw.local.idcRequest.IDCRequestType.englishdescription == "IDC Payment") {&#xD;
	tw.local.advancePaymentsUsedVis = true;&#xD;
}else{&#xD;
	tw.local.advancePaymentsUsedVis = false;&#xD;
}&#xD;
&#xD;
tw.local.message = "";&#xD;
var mandatoryTriggered = false;&#xD;
var tempLength = 0 ;&#xD;
//tw.local.invalidTabs = [];&#xD;
/*&#xD;
* =========================================================================================================&#xD;
*  &#xD;
* Add a coach validation error &#xD;
* 		&#xD;
* EX:	addError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');&#xD;
*&#xD;
* =========================================================================================================&#xD;
*/&#xD;
&#xD;
function addError(fieldName , controlMessage , validationMessage , fromMandatory)&#xD;
{&#xD;
	tw.system.coachValidation.addValidationError(fieldName, controlMessage);&#xD;
	fromMandatory &amp;&amp; mandatoryTriggered ? "" : tw.local.message += "&lt;li dir='rtl'&gt;" + validationMessage + "&lt;/li&gt;";&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is in the past and the last variable to exclude today&#xD;
*	&#xD;
* EX:	notPastDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)&#xD;
*&#xD;
* =============================================================================================================================&#xD;
*/&#xD;
&#xD;
function notPastDate(date , fieldName , controlMessage , validationMessage , exclude)&#xD;
{&#xD;
	if (exclude)&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &lt;= new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;	&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &lt; new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is between two dates&#xD;
*	&#xD;
* EX:	dateBetween(tw.local.date , date1 , date2 , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =============================================================================================================================&#xD;
*/&#xD;
&#xD;
function dateBetween(field , date1 , date2 , fieldName , controlMessage , validationMessage)&#xD;
{&#xD;
	if(field &lt; date1 &amp;&amp; field &gt; date2)&#xD;
	{&#xD;
	 	return true;&#xD;
	}&#xD;
	addError(fieldName , controlMessage , validationMessage);&#xD;
	return false;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ===============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is in the future and the last varaible to exculde today&#xD;
*	&#xD;
* EX:	notFutureDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)&#xD;
*&#xD;
* ===============================================================================================================================&#xD;
*/&#xD;
&#xD;
function notFutureDate(date , fieldName ,  controlMessage , validationMessage , exculde)&#xD;
{&#xD;
	if (exculde)&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &gt;= new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;	&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &gt; new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
&#xD;
/*&#xD;
* =================================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the string length is less than given length&#xD;
*	&#xD;
* EX:	minLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =================================================================================================================================&#xD;
*/&#xD;
&#xD;
function minLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the string length is greater than given length&#xD;
*	&#xD;
* EX:	maxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function maxLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &gt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the number is greater than given value&#xD;
*	&#xD;
* EX:	maxNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function maxNumber(field , fieldName , max , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field &gt; max)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the number is less than given value&#xD;
*	&#xD;
* EX:	minNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function minNumber(field , fieldName , min , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field &lt; min)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the field is null 'Mandatory'&#xD;
*	&#xD;
* EX:	notNull(tw.local.name , 'tw.local.name')&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
&#xD;
function mandatory(field , fieldName)&#xD;
{&#xD;
	if (field == null || field == undefined )&#xD;
	{&#xD;
		addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
		mandatoryTriggered = true;&#xD;
		return false;&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		switch (typeof field)&#xD;
		{&#xD;
			case "string":&#xD;
				if (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)&#xD;
				{&#xD;
					addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
			case "number":&#xD;
				if (field == 0.0)&#xD;
				{&#xD;
					addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
		}&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a validation mark on the tab if there is an error&#xD;
*	&#xD;
* EX:	validateTab(1)&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
&#xD;
function validateTab(index)&#xD;
{&#xD;
	if (tw.system.coachValidation.validationErrors.length &gt; tempLength)&#xD;
	{&#xD;
		tempLength = tw.system.coachValidation.validationErrors.length ;&#xD;
		tw.local.invalidTabs[tw.local.invalidTabs.length] = index;&#xD;
	}	&#xD;
}&#xD;
//--------------------------------------------------------------------------------------------------&#xD;
mandatory(tw.local.idcRequest.IDCRequestNature.id,"tw.local.idcRequest.IDCRequestNature.id");&#xD;
mandatory(tw.local.idcRequest.IDCRequestType.id,"tw.local.idcRequest.IDCRequestType.id");&#xD;
mandatory(tw.local.idcRequest.customerInformation.CIFNumber,"tw.local.idcRequest.customerInformation.CIFNumber");&#xD;
minLength(tw.local.idcRequest.customerInformation.CIFNumber , "tw.local.idcRequest.customerInformation.CIFNumber" , 8 , "CIF can't be empty and must be 8 digits" , "CIF can't be empty and must be 8 digits")&#xD;
maxLength(tw.local.idcRequest.customerInformation.CIFNumber , "tw.local.idcRequest.customerInformation.CIFNumber" , 8 , "" , "")&#xD;
if(!tw.local.retrieveCustomer || tw.local.idcRequest.customerInformation.customerName =="" || tw.local.idcRequest.customerInformation.customerName ==null ){&#xD;
&#xD;
	addError("tw.local.idcRequest.customerInformation.customerName" , "you must retrieve customer data" , "you must retrieve customer data");&#xD;
//addError("tw.local.retrieveCustomer" , "you must retrive customer data" , "you must retrive customer data");&#xD;
}&#xD;
&#xD;
if (tw.local.idcRequest.IDCRequestNature.englishdescription == "Update Request") {&#xD;
	mandatory(tw.local.idcRequest.ParentIDCRequestNumber,"tw.local.idcRequest.ParentIDCRequestNumber");&#xD;
	minLength(tw.local.idcRequest.ParentIDCRequestNumber , "tw.local.idcRequest.ParentIDCRequestNumber" , 14 , "Parent IDC Request Number must be 14 digits" , "Parent IDC Request Number must be 14 digits")&#xD;
	maxLength(tw.local.idcRequest.ParentIDCRequestNumber , "tw.local.idcRequest.ParentIDCRequestNumber" , 14 , "" , "")&#xD;
	if(!tw.local.retrieveRequest || tw.local.tmpIDCrequest.financialDetails.beneficiaryDetails.name =="" || tw.local.tmpIDCrequest.financialDetails.beneficiaryDetails.name == null){&#xD;
		addError("tw.local.tmpIDCrequest.financialDetails.beneficiaryDetails.name" , "you must retrieve parent IDC request data" , "you must retrieve parent IDC request data");&#xD;
		addError("tw.local.tmpIDCrequest.appInfo.requestDate" , "you must retrieve parent IDC request data" , "you must retrieve parent IDC request data");&#xD;
	}&#xD;
}&#xD;
&#xD;
</ns16:script>
                            </ns16:scriptTask>
                            <ns16:sequenceFlow sourceRef="2025.5dab97c3-4bdd-4631-a0f6-e602a9c3ba8c" targetRef="2025.70bc81e9-24b8-4567-9fa4-00e3ddb3383e" name="To Exclusive Gateway" id="2027.20df3315-b455-438a-bae4-cf74708c912a">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="action" id="2056.74e58948-7016-4ea7-837f-9c0a9564659c" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="selectedAction" id="2056.af984f2d-e341-4e5d-b5c4-a1d7d94df897" />
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.1ad2c476-2429-4849-b48d-e8dc071b0173" name="Initialization Script" id="2025.74724f89-c330-4e84-afe5-49cce26e863b">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="211" y="-56" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.6afcb867-973e-4371-9949-30f99d014018</ns16:incoming>
                                <ns16:outgoing>2027.1ad2c476-2429-4849-b48d-e8dc071b0173</ns16:outgoing>
                                <ns16:script>tw.local.idcRequest.stepLog = {};&#xD;
tw.local.idcRequest.stepLog.startTime = new Date();&#xD;
&#xD;
tw.local.advancePaymentsUsedVis=true;&#xD;
if (tw.local.ECMproperties==null || tw.local.ECMproperties==undefined) {&#xD;
	tw.local.ECMproperties= {};&#xD;
&#xD;
}&#xD;
&#xD;
tw.local.errorVIS = "NONE";&#xD;
&#xD;
tw.local.action = [];&#xD;
&#xD;
tw.local.action[0] = tw.epv.Action.submitRequest+"";&#xD;
tw.local.action[1] = tw.epv.Action.cancelReques+"";&#xD;
if (tw.local.idcRoutingDetails.hubCode != null &amp;&amp; tw.local.idcRoutingDetails.hubCode != "" &amp;&amp; tw.local.idcRoutingDetails.hubCode != undefined) {&#xD;
	&#xD;
	tw.local.idcRequest.appInfo.branch.name = tw.local.idcRoutingDetails.hubName;&#xD;
	tw.local.idcRequest.appInfo.branch.value = tw.local.idcRoutingDetails.hubCode;&#xD;
	tw.local.role = "Hub Maker";&#xD;
}else{&#xD;
	tw.local.role = "Branch Maker"&#xD;
	tw.local.idcRequest.appInfo.branch.name = tw.local.idcRoutingDetails.branchName;&#xD;
	tw.local.idcRequest.appInfo.branch.value = tw.local.idcRoutingDetails.branchSeq;&#xD;
}&#xD;
&#xD;
</ns16:script>
                            </ns16:scriptTask>
                            <ns16:sequenceFlow sourceRef="2025.74724f89-c330-4e84-afe5-49cce26e863b" targetRef="2025.ce3318bc-ad0a-4809-b3e5-8d0a8f3f6929" name="To Set Step Name" id="2027.1ad2c476-2429-4849-b48d-e8dc071b0173">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:exclusiveGateway default="2027.1bd1cf32-7c29-467c-a632-d8e6f8aa6643" name="Exclusive Gateway" id="2025.70bc81e9-24b8-4567-9fa4-00e3ddb3383e">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="508" y="-3" width="32" height="32" />
                                    <ns3:postAssignmentScript>tw.local.idcRequest.appInfo.status = "New Request";&#xD;
tw.local.idcRequest.appInfo.subStatus = "New Request";</ns3:postAssignmentScript>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.20df3315-b455-438a-bae4-cf74708c912a</ns16:incoming>
                                <ns16:outgoing>2027.1bd1cf32-7c29-467c-a632-d8e6f8aa6643</ns16:outgoing>
                                <ns16:outgoing>2027.09f07a5e-4dcf-4ede-a465-3f7689a9bb0f</ns16:outgoing>
                            </ns16:exclusiveGateway>
                            <ns16:sequenceFlow sourceRef="2025.70bc81e9-24b8-4567-9fa4-00e3ddb3383e" targetRef="2025.c9bece30-ee23-455f-a1f9-fdbc3274182b" name="To Get Request Number ,CBE And ECM Folder" id="2027.1bd1cf32-7c29-467c-a632-d8e6f8aa6643">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.70bc81e9-24b8-4567-9fa4-00e3ddb3383e" targetRef="2025.6d76e0dd-17bb-4e8d-98d1-82e8d0682c86" name="To Create IDC Request" id="2027.09f07a5e-4dcf-4ede-a465-3f7689a9bb0f">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>leftCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.system.coachValidation.validationErrors.length	  &gt;	  0</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="retrieveRequest" id="2056.d3da5538-d691-497a-b5ad-e3a8951ba8b5">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">false</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="retrieveCustomer" id="2056.6f519687-e7ad-4934-baf4-8351a36aff47">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">false</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="advancePaymentsUsedVis" id="2056.ab1441ac-969b-44c3-9838-bef5dca3e81f" />
                            <ns16:callActivity calledElement="1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13" default="2027.a2702278-c88d-4b12-ab10-f056ce0ae66a" name="Get Request Number ,CBE And ECM Folder" id="2025.c9bece30-ee23-455f-a1f9-fdbc3274182b">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="492" y="90" width="95" height="70" color="#A5B7CD" />
                                    <ns3:preAssignmentScript />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.1bd1cf32-7c29-467c-a632-d8e6f8aa6643</ns16:incoming>
                                <ns16:outgoing>2027.a2702278-c88d-4b12-ab10-f056ce0ae66a</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.495b518d-03ae-4920-82cf-eeb04812beb2</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.idcRequest.customerInformation.CIFNumber</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.87745665-8435-4ac5-85f1-420de9e01cd0</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.idcRequest.appInfo.branch.value</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.e3b36fc9-0cb9-453d-8275-14fa1f734703</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" />
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.9eabaff4-3e88-47e4-bbbe-5cc34cf37cb2</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67">tw.local.idcRequest</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.6e39d6c4-152e-4681-9ccb-b3d2ea326441</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67">tw.local.tmpIDCrequest</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.f4a7754b-fcbe-4f1f-aaaf-65abfffad2aa</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.idcRequest.customerInformation.isCustomeSanctionedbyCBE</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.d22b4a18-865c-4b84-b879-26ee9e3f322f</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.idcRequest.appInfo.instanceID</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.77c3cc2f-ce0f-473c-8551-a2f1093abb8f</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.folderID</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.31e2d85c-18d8-4756-82c5-efad7879177e</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.parentPath</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.b304617e-98f3-4070-b457-59e470497a2f</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.ECMproperties.fullPath</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.3027fb96-9129-4507-8aa8-f177c64f509b</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42">tw.local.Accounts</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.4ae2afa0-cdd1-4d71-869e-7ea34e15f8aa</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:sequenceFlow sourceRef="2025.c9bece30-ee23-455f-a1f9-fdbc3274182b" targetRef="2025.896d855b-8f91-4a12-befb-45fa77355eb8" name="To IDC Request Data" id="2027.a2702278-c88d-4b12-ab10-f056ce0ae66a">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" />
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="havePaymentTerms" id="2056.eaf2f7a4-527e-4089-8c56-23bd9239dd5b">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">"no"</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="haseApprovals" id="2056.64e76842-552e-421e-8aa0-7210a82b8744">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">false</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="hasReturnReason" id="2056.1bae6db0-3223-425c-b9c5-e4a94981cf78">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">false</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.9cecf069-47c9-41c6-9247-cc005b368801" name="Set State and Stage" id="2025.896d855b-8f91-4a12-befb-45fa77355eb8">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="492" y="200" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.a2702278-c88d-4b12-ab10-f056ce0ae66a</ns16:incoming>
                                <ns16:outgoing>2027.9cecf069-47c9-41c6-9247-cc005b368801</ns16:outgoing>
                                <ns16:script>if ((tw.local.idcRoutingDetails.hubCode != null &amp;&amp; tw.local.idcRoutingDetails.hubCode != "" &amp;&amp; tw.local.idcRoutingDetails.hubCode != undefined) &amp;&amp; (tw.local.idcRequest.IDCRequestType.englishdescription == "IDC Amendment" || tw.local.idcRequest.IDCRequestType.englishdescription == "IDC Payment") ) {&#xD;
	tw.local.action[2] = tw.epv.Action.submitRequestToHubDirectly+"";&#xD;
&#xD;
}&#xD;
if (tw.local.idcRequest.IDCRequestType.englishdescription == "IDC Acknowledgement") {&#xD;
	tw.local.idcRequest.IDCRequestStage = tw.epv.IDCstage.initial;&#xD;
	tw.local.idcRequest.IDCRequestState = tw.epv.IDCState.draft;&#xD;
} else {&#xD;
	tw.local.idcRequest.IDCRequestStage = tw.epv.IDCstage.fiinal;&#xD;
	tw.local.idcRequest.IDCRequestState = tw.epv.IDCState.draft;&#xD;
}&#xD;
&#xD;
&#xD;
tw.local.ECMproperties.fullPath = tw.env.FILENET_ROOT_PATH +"/"+ tw.local.ECMproperties.fullPath;&#xD;
tw.local.default_properties = [];&#xD;
tw.local.default_properties[0]={};&#xD;
tw.local.default_properties[0].editable = false;&#xD;
tw.local.default_properties[0].hidden = false;&#xD;
tw.local.default_properties[0].name = "Customer_Number_CIF";&#xD;
tw.local.default_properties[0].value = tw.local.idcRequest.customerInformation.CIFNumber;&#xD;
tw.local.default_properties[1]={};&#xD;
tw.local.default_properties[1].editable = false;&#xD;
tw.local.default_properties[1].hidden = false;&#xD;
tw.local.default_properties[1].name = "BPMInstanceId";&#xD;
tw.local.default_properties[1].value = tw.local.idcRequest.appInfo.appID;&#xD;
tw.local.default_properties[2]={};&#xD;
tw.local.default_properties[2].editable = false;&#xD;
tw.local.default_properties[2].hidden = false;&#xD;
tw.local.default_properties[2].name = "RequestReferenceNumber";&#xD;
tw.local.default_properties[2].value = tw.local.idcRequest.appInfo.instanceID;&#xD;
tw.local.default_properties[3]={};&#xD;
tw.local.default_properties[3].editable = false;&#xD;
tw.local.default_properties[3].hidden = true;&#xD;
tw.local.default_properties[3].name = "DocumentType";&#xD;
tw.local.default_properties[3].value = "";&#xD;
&#xD;
tw.local.default_properties[4]={};&#xD;
tw.local.default_properties[4].editable = false;&#xD;
tw.local.default_properties[4].hidden = true;&#xD;
tw.local.default_properties[4].name = "DocumentTitle";&#xD;
tw.local.default_properties[4].value = "";&#xD;
&#xD;
tw.local.default_properties[5]={};&#xD;
tw.local.default_properties[5].editable = false;&#xD;
tw.local.default_properties[5].hidden = true;&#xD;
tw.local.default_properties[5].name = "RecordInformation";&#xD;
tw.local.default_properties[5].value = "";&#xD;
&#xD;
tw.local.default_properties[6]={};&#xD;
tw.local.default_properties[6].editable = false;&#xD;
tw.local.default_properties[6].hidden = true;&#xD;
tw.local.default_properties[6].name = "CmFederatedLockStatus";&#xD;
tw.local.default_properties[6].value = "";&#xD;
&#xD;
&#xD;
&#xD;
//tw.local.ECMproperties.cmisQuery = "SELECT * FROM IDCDocument WHERE IN_TREE('"+tw.local.ECMproperties.fullPath+"')";&#xD;
tw.local.ECMproperties.defaultProperties = tw.local.default_properties;&#xD;
</ns16:script>
                            </ns16:scriptTask>
                            <ns16:sequenceFlow sourceRef="2025.896d855b-8f91-4a12-befb-45fa77355eb8" targetRef="2025.49ca7e00-8853-4d3f-8d52-119f94ff3e10" name="To IDC Request Data" id="2027.9cecf069-47c9-41c6-9247-cc005b368801">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>bottomLeft</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="accountNumber" id="2056.bbb188e8-3cfc-495a-b29e-c6a152b21295">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="false" />
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="true" name="exist" id="2056.7881b472-e7ab-4ee6-8e33-39e9338d4c35" />
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="errorExist" id="2056.fbce89f3-dbca-44b8-bab6-96b02e317dc9" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.88ea9ce0-bc9e-46c3-94ee-1cec1bbaca80" />
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="validation" id="2056.bd63dfb5-84a3-4d46-9896-559942870dc1" />
                            <ns16:dataObject itemSubjectRef="itm.12.eac829db-66fe-43f5-810c-6faa514533a2" isCollection="true" name="default_properties" id="2056.460773e0-c5ba-4752-aeb7-072f2e8f5748" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="cmisQuery" id="2056.1d26dfd7-611d-4088-abc8-66887fe7511d" />
                            <ns16:dataObject itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" name="documentsTypesSelected" id="2056.acae7137-6267-4e31-bc9a-e8c6bc42e93b" />
                            <ns16:callActivity calledElement="1.95226f60-a3cc-4d6c-9b80-5bca5cc430e2" default="2027.4aa6a2cc-3485-4d95-8093-0e7dd7afac2a" name="Get Required Documents" id="2025.de4e528f-4cdb-425d-8c24-2def6866abf4">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="739" y="-135" width="95" height="70" color="#95D087" />
                                    <ns3:preAssignmentScript>tw.local.documentsTypesSelected = tw.local.attachment.listAllSelected;</ns3:preAssignmentScript>
                                    <ns3:postAssignmentScript>if (tw.local.errorExist) {&#xD;
	alert(tw.local.errorMessage);&#xD;
}</ns3:postAssignmentScript>
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.4aa6a2cc-3485-4d95-8093-0e7dd7afac2a</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.a4a1317a-bc51-4643-b432-1d1c7cbaba1c</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.idcRequest.documentsSource.englishdescription</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.aae56053-3bba-40b1-abc9-6a441a93f307</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.idcRequest.IDCRequestType.englishdescription</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.cad5cf23-0f7f-41ba-b9eb-34cab62cb7f8</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f">tw.local.documentsTypesSelected</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.8e6a0739-a7ac-4f62-b871-969d0ca951ef</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.folderID</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.1e09e24d-eb4a-4474-96f7-d5075b4fc749</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.3c7c7a8a-43f7-4745-b05a-3c52b99703d6</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.errorExist</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.74cb9e94-45c3-4ba9-8862-d45286d425b6</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:sequenceFlow sourceRef="2025.de4e528f-4cdb-425d-8c24-2def6866abf4" targetRef="2025.456c9026-c887-42e9-aa46-c2db3a83a3f6" name="To Check Customer Accounts" id="2027.4aa6a2cc-3485-4d95-8093-0e7dd7afac2a">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:exclusiveGateway default="2027.8303ffc2-34a7-4d7f-8bd7-b43829b1c16d" name="Exclusive Gateway 1" id="2025.6745a6d9-029a-4342-833a-dd2d269c35ae">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="259" y="275" width="32" height="32" />
                                    <ns3:postAssignmentScript />
                                    <ns3:preAssignmentScript />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.d029fbf9-335e-4245-b721-500bb668b62b</ns16:incoming>
                                <ns16:outgoing>2027.8303ffc2-34a7-4d7f-8bd7-b43829b1c16d</ns16:outgoing>
                                <ns16:outgoing>2027.739a0624-71ef-4e60-8c99-f33d23dd579d</ns16:outgoing>
                            </ns16:exclusiveGateway>
                            <ns16:sequenceFlow sourceRef="2025.6745a6d9-029a-4342-833a-dd2d269c35ae" targetRef="2025.6d76e0dd-17bb-4e8d-98d1-82e8d0682c86" name="To Create IDC Request" id="2027.8303ffc2-34a7-4d7f-8bd7-b43829b1c16d">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.6745a6d9-029a-4342-833a-dd2d269c35ae" targetRef="2025.********-78b9-46e1-88b9-048aac7d8178" name="To Client-Side Script" id="2027.739a0624-71ef-4e60-8c99-f33d23dd579d">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.idcRequest.appInfo.subStatus	  ==	  tw.epv.IDCsubStatus.returnedtoInitiator</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42" isCollection="true" name="accountsList" id="2056.0a63ba65-76ed-4878-8c1a-16415bfd58e2" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="role" id="2056.25f650ed-491e-4700-81bf-82ba316c9a93">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.0fd60f96-4c02-43df-831d-0328a3497a99" name="Client-Side Script" id="2025.********-78b9-46e1-88b9-048aac7d8178">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="490" y="292" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.739a0624-71ef-4e60-8c99-f33d23dd579d</ns16:incoming>
                                <ns16:outgoing>2027.0fd60f96-4c02-43df-831d-0328a3497a99</ns16:outgoing>
                                <ns16:script>if (tw.local.idcRoutingDetails.hubCode != null &amp;&amp; tw.local.idcRoutingDetails.hubCode != "" &amp;&amp; tw.local.idcRoutingDetails.hubCode != undefined) {&#xD;
	if (tw.local.idcRequest.IDCRequestType.englishdescription != "IDC Amendment" &amp;&amp; tw.local.idcRequest.IDCRequestType.englishdescription != "IDC Payment" ) {&#xD;
		tw.local.action.pop();&#xD;
	}&#xD;
}</ns16:script>
                            </ns16:scriptTask>
                            <ns16:sequenceFlow sourceRef="2025.********-78b9-46e1-88b9-048aac7d8178" targetRef="2025.49ca7e00-8853-4d3f-8d52-119f94ff3e10" name="To IDC Request Data" id="2027.0fd60f96-4c02-43df-831d-0328a3497a99">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>bottomLeft</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67" isCollection="false" name="tmpUsedAdvancePayment" id="2056.1bf58584-0f2a-4931-835d-b9ef5bbf376f" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="validationMessage" id="2056.a73650fd-39fa-4ce8-8a8c-f608030db340" />
                            <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="true" name="invalidTabs" id="2056.63b05af4-**************-a37d4e052126" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="currencyVis" id="2056.da608b97-9e34-4771-873c-5246044a4dfa">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">"DEFAULT"</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:boundaryEvent cancelActivity="true" attachedToRef="2025.bf1e6e71-4a30-4575-9747-448f0c391594" parallelMultiple="false" name="Error" id="2025.f754aed6-2a32-4ce9-845b-32329ea22ee1">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="111" y="143" width="24" height="24" />
                                    <ns3:default>2027.4bcbadc8-b130-420d-8602-6d990aaa271b</ns3:default>
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.4bcbadc8-b130-420d-8602-6d990aaa271b</ns16:outgoing>
                                <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2025.aeee0fe8-60f6-40a1-851d-76d61b23c7bd" />
                                <ns16:outputSet />
                                <ns16:errorEventDefinition>
                                    <ns16:extensionElements>
                                        <ns4:errorEventSettings>
                                            <ns4:catchAll>true</ns4:catchAll>
                                        </ns4:errorEventSettings>
                                    </ns16:extensionElements>
                                </ns16:errorEventDefinition>
                            </ns16:boundaryEvent>
                            <ns16:boundaryEvent cancelActivity="true" attachedToRef="2025.c9bece30-ee23-455f-a1f9-fdbc3274182b" parallelMultiple="false" name="Error 1" id="2025.bcb0053f-0732-424e-8cd9-b40e826fea73">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="575" y="95" width="24" height="24" />
                                    <ns3:default>2027.ccf1f906-2fbb-4596-82f3-8dc809fe8fe0</ns3:default>
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.ccf1f906-2fbb-4596-82f3-8dc809fe8fe0</ns16:outgoing>
                                <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2025.7157d29f-c30d-4260-8b00-43b209f7f743" />
                                <ns16:outputSet />
                                <ns16:errorEventDefinition>
                                    <ns16:extensionElements>
                                        <ns4:errorEventSettings>
                                            <ns4:catchAll>true</ns4:catchAll>
                                        </ns4:errorEventSettings>
                                    </ns16:extensionElements>
                                </ns16:errorEventDefinition>
                            </ns16:boundaryEvent>
                            <ns16:boundaryEvent cancelActivity="true" attachedToRef="2025.de4e528f-4cdb-425d-8c24-2def6866abf4" parallelMultiple="false" name="Error 2" id="2025.178c4832-15e6-4cce-85c2-2f2f22c02145">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="774" y="-147" width="24" height="24" />
                                    <ns3:default>2027.afed332a-cc39-49e6-8c72-923fb2b5fa05</ns3:default>
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.afed332a-cc39-49e6-8c72-923fb2b5fa05</ns16:outgoing>
                                <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2025.0afdc17b-6546-429a-8f94-c120ae169e69" />
                                <ns16:outputSet />
                                <ns16:errorEventDefinition>
                                    <ns16:extensionElements>
                                        <ns4:errorEventSettings>
                                            <ns4:catchAll>true</ns4:catchAll>
                                        </ns4:errorEventSettings>
                                    </ns16:extensionElements>
                                </ns16:errorEventDefinition>
                            </ns16:boundaryEvent>
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.567761a7-71b4-4fc0-826a-480bf1bdd744" name="Handling Error" id="2025.ceb3afb5-605d-4ab2-898c-d02e0044a9b3">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="487" y="-225" width="95" height="70" color="#FF7782" />
                                    <ns3:preAssignmentScript />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.b9c5230e-dcb1-410c-8d81-02113271f906</ns16:incoming>
                                <ns16:incoming>2027.afed332a-cc39-49e6-8c72-923fb2b5fa05</ns16:incoming>
                                <ns16:incoming>2027.ccf1f906-2fbb-4596-82f3-8dc809fe8fe0</ns16:incoming>
                                <ns16:outgoing>2027.567761a7-71b4-4fc0-826a-480bf1bdd744</ns16:outgoing>
                                <ns16:script>tw.local.errorMSG = String(tw.error.data);&#xD;
&#xD;
tw.local.errorVIS = "EDITABLE";&#xD;
//alert(tw.local.errorMSG);</ns16:script>
                            </ns16:scriptTask>
                            <ns16:sequenceFlow sourceRef="2025.178c4832-15e6-4cce-85c2-2f2f22c02145" targetRef="2025.ceb3afb5-605d-4ab2-898c-d02e0044a9b3" name="To Handling Error" id="2027.afed332a-cc39-49e6-8c72-923fb2b5fa05">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.ce0e6f47-7011-472e-89da-87ef254aa439" name="Handling Error2" id="2025.f5c8d6bf-5a19-4259-8c7b-39c71f5c7c12">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="220" y="-185" width="95" height="70" color="#FF7782" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.4bcbadc8-b130-420d-8602-6d990aaa271b</ns16:incoming>
                                <ns16:outgoing>2027.ce0e6f47-7011-472e-89da-87ef254aa439</ns16:outgoing>
                                <ns16:script>tw.local.errorMSG = String(tw.error.data);&#xD;
tw.local.errorVIS = "EDITABLE";&#xD;
//alert(tw.local.errorMSG);</ns16:script>
                            </ns16:scriptTask>
                            <ns16:sequenceFlow sourceRef="2025.f754aed6-2a32-4ce9-845b-32329ea22ee1" targetRef="2025.f5c8d6bf-5a19-4259-8c7b-39c71f5c7c12" name="To Handling Error2" id="2027.4bcbadc8-b130-420d-8602-6d990aaa271b">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:intermediateThrowEvent name="Stay on page" id="2025.402cc521-ef49-4ce0-8f6a-578039314372">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="230" y="-238" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.ce0e6f47-7011-472e-89da-87ef254aa439</ns16:incoming>
                                <ns3:stayOnPageEventDefinition />
                            </ns16:intermediateThrowEvent>
                            <ns16:sequenceFlow sourceRef="2025.f5c8d6bf-5a19-4259-8c7b-39c71f5c7c12" targetRef="2025.402cc521-ef49-4ce0-8f6a-578039314372" name="To Stay on page" id="2027.ce0e6f47-7011-472e-89da-87ef254aa439">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.bcb0053f-0732-424e-8cd9-b40e826fea73" targetRef="2025.ceb3afb5-605d-4ab2-898c-d02e0044a9b3" name="To Handling Error" id="2027.ccf1f906-2fbb-4596-82f3-8dc809fe8fe0">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>rightBottom</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:customBendPoint x="617" y="-108" />
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.c4827fb2-0617-41f5-880c-2a35a1d2c2ff" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorVIS" id="2056.779bfb1d-d3ca-4074-86ca-cd45a72f60e4" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="alertMessage" id="2056.88008abd-7153-488a-8b25-60048fe80e0b" />
                            <ns16:callActivity calledElement="1.9f0a859b-5010-4ab6-947a-81ad99803cf1" default="2027.f7d79e2e-d4f5-4ec4-8194-c9455b758a1c" name="Database Integration" id="2025.0c9a7cd3-c644-472f-8c23-976def594caa">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1097" y="100" width="95" height="70" />
                                    <ns3:postAssignmentScript />
                                    <ns3:preAssignmentScript />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.d4c507ee-6622-4a63-86c2-2d8c273474d5</ns16:incoming>
                                <ns16:outgoing>2027.f7d79e2e-d4f5-4ec4-8194-c9455b758a1c</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.79f352fc-0629-430a-87cd-5b10dbdc4454</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">false</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.4144ba7c-0a79-4853-8fd2-6aa9481cd0bb</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67">tw.local.idcRequest</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.3fafd6ca-c323-45ea-8653-0015f902d198</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.selectedAction</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.a7f76cb6-349a-4e05-84c4-a0307bd1074b</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.role</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.d3b53e19-8f75-427c-8e43-fe25518ef721</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.idcRequest.appLog</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.96de5ca4-16df-4f86-8d6d-dc1ae75db0f9</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1">tw.local.idcContract</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.9ad81a8c-1b47-4f45-81b1-c0c0120c97d1</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67">tw.local.idcRequest</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.e92b92ce-41f1-49d7-82cf-acf4e366b0e0</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.b08a2cbe-96ec-4d2d-8efe-ec915a097b44</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.idcRequest.appLog</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:sequenceFlow sourceRef="2025.0c9a7cd3-c644-472f-8c23-976def594caa" targetRef="ced35e6d-1321-4fd5-84dc-fc0e81d91c45" name="To End" id="2027.f7d79e2e-d4f5-4ec4-8194-c9455b758a1c">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:intermediateThrowEvent name="Stay on page 1" id="2025.22d2d42d-9cfe-4daa-8ab6-a8698af5f739">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="390" y="-201" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.567761a7-71b4-4fc0-826a-480bf1bdd744</ns16:incoming>
                                <ns3:stayOnPageEventDefinition />
                            </ns16:intermediateThrowEvent>
                            <ns16:sequenceFlow sourceRef="2025.ceb3afb5-605d-4ab2-898c-d02e0044a9b3" targetRef="2025.22d2d42d-9cfe-4daa-8ab6-a8698af5f739" name="To Stay on page 1" id="2027.567761a7-71b4-4fc0-826a-480bf1bdd744">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>leftCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" name="error" id="2056.287a216f-5a65-40e7-88eb-9db5fcf7201c">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.errorText = "";
autoObject.errorCode = "";
autoObject.serviceInError = "";
autoObject</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:boundaryEvent cancelActivity="true" attachedToRef="2025.0c9a7cd3-c644-472f-8c23-976def594caa" parallelMultiple="false" name="Error 3" id="2025.b511a75b-f4b1-4cc4-8784-1c4cb412944e">
                                <ns16:extensionElements>
                                    <ns3:default>2027.b9c5230e-dcb1-410c-8d81-02113271f906</ns3:default>
                                    <ns13:nodeVisualInfo x="1132" y="88" width="24" height="24" />
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.b9c5230e-dcb1-410c-8d81-02113271f906</ns16:outgoing>
                                <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2025.d6cc900a-72df-4c20-8bc2-f1024e90270e" />
                                <ns16:outputSet />
                                <ns16:errorEventDefinition>
                                    <ns16:extensionElements>
                                        <ns4:errorEventSettings>
                                            <ns4:catchAll>true</ns4:catchAll>
                                        </ns4:errorEventSettings>
                                    </ns16:extensionElements>
                                </ns16:errorEventDefinition>
                            </ns16:boundaryEvent>
                            <ns16:sequenceFlow sourceRef="2025.b511a75b-f4b1-4cc4-8784-1c4cb412944e" targetRef="2025.ceb3afb5-605d-4ab2-898c-d02e0044a9b3" name="To Handling Error" id="2027.b9c5230e-dcb1-410c-8d81-02113271f906">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>rightTop</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.e9f65280-afe9-44dc-9616-f95c2a14629e" isCollection="true" name="InterestAndChargesList" id="2056.f9281cb6-b1ec-4a1a-828d-253437b3aaa9" />
                            <ns16:dataObject itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" name="exRate" id="2056.320557b4-d664-425e-87a3-ce20cbf518ab" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="adviceCodeList" id="2056.63336b9a-7a4a-4480-8a36-7b06021879c4" />
                            <ns3:htmlHeaderTag id="7938c5b4-8a9c-4382-9b07-4173d4ff0f41">
                                <ns3:tagName>viewport</ns3:tagName>
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                <ns3:enabled>true</ns3:enabled>
                            </ns3:htmlHeaderTag>
                        </ns3:userTaskImplementation>
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                    </ns16:extensionElements>
                    <ns16:ioSpecification>
                        <ns16:extensionElements>
                            <ns3:epvProcessLinks>
                                <ns3:epvProcessLinkRef epvId="21.02818ba4-c183-4dfb-8924-18e2d9a515dd" epvProcessLinkId="30a47594-8e29-494e-8a61-f9890c2e1ff0" />
                                <ns3:epvProcessLinkRef epvId="21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e" epvProcessLinkId="378669a8-b9bf-4e53-8146-661e37f7dc1e" />
                                <ns3:epvProcessLinkRef epvId="21.8ce8b34e-54bb-4623-a4c9-ab892efacac6" epvProcessLinkId="24962ef8-47a2-4543-84d1-7e2ff9eadd95" />
                                <ns3:epvProcessLinkRef epvId="21.e5829eee-0ab1-4f47-9191-f0f8705bc33e" epvProcessLinkId="0af371a1-eb34-4b19-8aba-b3f6b5f31688" />
                                <ns3:epvProcessLinkRef epvId="21.0bb89e09-258a-4bd1-b3a7-137a3219209f" epvProcessLinkId="d31cff85-497c-4803-83be-19d3d7abfdda" />
                            </ns3:epvProcessLinks>
                            <ns3:localizationResourceLinks>
                                <ns3:resourceRef>
                                    <ns3:resourceBundleGroupID>50.95ec9bf9-2d5a-4fc5-8dd1-e6d6a55a836c</ns3:resourceBundleGroupID>
                                    <ns3:id>69.8e814082-d8dc-4953-8463-29a46de80662</ns3:id>
                                </ns3:resourceRef>
                            </ns3:localizationResourceLinks>
                            <ns3:envProcessLinks>
                                <ns3:envProcessLinkRef envId="2094.efef6f5f-c429-4864-8545-77dd664c9ace" envProcessLinkId="5fba7752-ac23-4861-8c76-41df40906fe1" />
                            </ns3:envProcessLinks>
                        </ns16:extensionElements>
                        <ns16:dataInput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.5ceab549-c986-4d50-8793-1ddb60d1c6c6">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.IDCRequestState = "";
autoObject.commodityDescription = "";
autoObject.countryOfOrigin = {};
autoObject.countryOfOrigin.id = 0;
autoObject.countryOfOrigin.code = "";
autoObject.countryOfOrigin.arabicdescription = "";
autoObject.countryOfOrigin.englishdescription = "";
autoObject.appInfo = {};
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = {};
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.productsDetails = {};
autoObject.productsDetails.destinationPort = "";
autoObject.productsDetails.shippingDate = new Date();
autoObject.productsDetails.HSProduct = {};
autoObject.productsDetails.HSProduct.id = 0;
autoObject.productsDetails.HSProduct.code = "";
autoObject.productsDetails.HSProduct.arabicdescription = "";
autoObject.productsDetails.HSProduct.englishdescription = "";
autoObject.productsDetails.incoterms = {};
autoObject.productsDetails.incoterms.id = 0;
autoObject.productsDetails.incoterms.code = "";
autoObject.productsDetails.incoterms.arabicdescription = "";
autoObject.productsDetails.incoterms.englishdescription = "";
autoObject.productsDetails.ACID = "";
autoObject.productsDetails.CBECommodityClassification = {};
autoObject.productsDetails.CBECommodityClassification.id = 0;
autoObject.productsDetails.CBECommodityClassification.code = "";
autoObject.productsDetails.CBECommodityClassification.arabicdescription = "";
autoObject.productsDetails.CBECommodityClassification.englishdescription = "";
autoObject.productsDetails.shipmentMethod = {};
autoObject.productsDetails.shipmentMethod.id = 0;
autoObject.productsDetails.shipmentMethod.code = "";
autoObject.productsDetails.shipmentMethod.arabicdescription = "";
autoObject.productsDetails.shipmentMethod.englishdescription = "";
autoObject.financialDetails = {};
autoObject.financialDetails.isAdvancePaymentsUsed = false;
autoObject.financialDetails.paymentTerms = [];
autoObject.financialDetails.paymentTerms[0] = {};
autoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;
autoObject.financialDetails.paymentTerms[0].installmentDate = new Date();
autoObject.financialDetails.usedAdvancePayment = [];
autoObject.financialDetails.usedAdvancePayment[0] = {};
autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = {};
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = "";
autoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;
autoObject.financialDetails.discountAmt = 0.0;
autoObject.financialDetails.firstInstallementMaturityDate = new Date();
autoObject.financialDetails.facilityAmtInDocCurrency = 0.0;
autoObject.financialDetails.amtSight = 0.0;
autoObject.financialDetails.beneficiaryDetails = {};
autoObject.financialDetails.beneficiaryDetails.account = "";
autoObject.financialDetails.beneficiaryDetails.bank = "";
autoObject.financialDetails.beneficiaryDetails.correspondentRefNum = "";
autoObject.financialDetails.beneficiaryDetails.name = "";
autoObject.financialDetails.beneficiaryDetails.country = {};
autoObject.financialDetails.beneficiaryDetails.country.id = 0;
autoObject.financialDetails.beneficiaryDetails.country.code = "";
autoObject.financialDetails.beneficiaryDetails.country.arabicdescription = "";
autoObject.financialDetails.beneficiaryDetails.country.englishdescription = "";
autoObject.financialDetails.amtDeferredNoAvalized = 0.0;
autoObject.financialDetails.amtPayableByNBE = 0.0;
autoObject.financialDetails.executionHub = {};
autoObject.financialDetails.executionHub.id = 0;
autoObject.financialDetails.executionHub.code = "";
autoObject.financialDetails.executionHub.arabicdescription = "";
autoObject.financialDetails.executionHub.englishdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency = {};
autoObject.financialDetails.sourceOfForeignCurrency.id = 0;
autoObject.financialDetails.sourceOfForeignCurrency.code = "";
autoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency.englishdescription = "";
autoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;
autoObject.financialDetails.documentCurrency = {};
autoObject.financialDetails.documentCurrency.id = 0;
autoObject.financialDetails.documentCurrency.code = "";
autoObject.financialDetails.documentCurrency.arabicdescription = "";
autoObject.financialDetails.documentCurrency.englishdescription = "";
autoObject.financialDetails.daysTillMaturity = 0;
autoObject.financialDetails.tradeFinanceApprovalNumber = "";
autoObject.financialDetails.amountAdvanced = 0.0;
autoObject.financialDetails.paymentAccount = "";
autoObject.financialDetails.tradeFOReferenceNumber = "";
autoObject.financialDetails.documentAmount = 0.0;
autoObject.financialDetails.sourceOfFunds = {};
autoObject.financialDetails.sourceOfFunds.id = 0;
autoObject.financialDetails.sourceOfFunds.code = "";
autoObject.financialDetails.sourceOfFunds.arabicdescription = "";
autoObject.financialDetails.sourceOfFunds.englishdescription = "";
autoObject.financialDetails.chargesAccount = "";
autoObject.financialDetails.CashAmtWithNoCurrency = 0.0;
autoObject.financialDetails.amtDeferredAvalized = 0.0;
autoObject.financialDetails.amtPaidbyOtherBanks = 0.0;
autoObject.financialDetails.cashAmtInDocCurrency = 0.0;
autoObject.IDCRequestType = {};
autoObject.IDCRequestType.id = 0;
autoObject.IDCRequestType.code = "";
autoObject.IDCRequestType.arabicdescription = "";
autoObject.IDCRequestType.englishdescription = "";
autoObject.isIDCWithdrawn = false;
autoObject.IDCRequestStage = "";
autoObject.FCContractNumber = "";
autoObject.billOfLading = [];
autoObject.billOfLading[0] = {};
autoObject.billOfLading[0].date = new Date();
autoObject.billOfLading[0].number = "";
autoObject.importPurpose = {};
autoObject.importPurpose.id = 0;
autoObject.importPurpose.code = "";
autoObject.importPurpose.arabicdescription = "";
autoObject.importPurpose.englishdescription = "";
autoObject.IDCRequestNature = {};
autoObject.IDCRequestNature.id = 0;
autoObject.IDCRequestNature.code = "";
autoObject.IDCRequestNature.arabicdescription = "";
autoObject.IDCRequestNature.englishdescription = "";
autoObject.customerInformation = {};
autoObject.customerInformation.CIFNumber = "";
autoObject.customerInformation.importCardNumber = "";
autoObject.customerInformation.commercialRegistrationNumber = "";
autoObject.customerInformation.customerName = "";
autoObject.customerInformation.isCustomeSanctionedbyCBE = "";
autoObject.customerInformation.CBENumber = "";
autoObject.customerInformation.customerSector = "";
autoObject.customerInformation.facilityType = "";
autoObject.customerInformation.commercialRegistrationOffice = "";
autoObject.customerInformation.taxCardNumber = "";
autoObject.customerInformation.customerType = "";
autoObject.customerInformation.addressLine1 = "";
autoObject.customerInformation.addressLine2 = "";
autoObject.invoices = [];
autoObject.invoices[0] = {};
autoObject.invoices[0].date = new Date();
autoObject.invoices[0].number = "";
autoObject.productCategory = {};
autoObject.productCategory.id = 0;
autoObject.productCategory.code = "";
autoObject.productCategory.arabicdescription = "";
autoObject.productCategory.englishdescription = "";
autoObject.documentsSource = {};
autoObject.documentsSource.id = 0;
autoObject.documentsSource.code = "";
autoObject.documentsSource.arabicdescription = "";
autoObject.documentsSource.englishdescription = "";
autoObject.ParentIDCRequestNumber = "";
autoObject.paymentTerms = {};
autoObject.paymentTerms.id = 0;
autoObject.paymentTerms.code = "";
autoObject.paymentTerms.arabicdescription = "";
autoObject.paymentTerms.englishdescription = "";
autoObject.approvals = {};
autoObject.approvals.CAD = false;
autoObject.approvals.treasury = false;
autoObject.approvals.compliance = false;
autoObject.appLog = [];
autoObject.appLog[0] = {};
autoObject.appLog[0].startTime = new Date();
autoObject.appLog[0].endTime = new Date();
autoObject.appLog[0].userName = "";
autoObject.appLog[0].role = "";
autoObject.appLog[0].step = "";
autoObject.appLog[0].action = "";
autoObject.appLog[0].comment = "";
autoObject.appLog[0].terminateReason = "";
autoObject.appLog[0].returnReason = "";
autoObject.stepLog = {};
autoObject.stepLog.startTime = new Date();
autoObject.stepLog.endTime = new Date();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="attachment" itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" id="2055.6961cd54-d200-49ed-97ed-55daead4c5e6">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = [];
autoObject[0] = {};
autoObject[0].name = "";
autoObject[0].description = "";
autoObject[0].arabicName = "";
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="idcRoutingDetails" itemSubjectRef="itm.12.9f3bdc5d-083e-4370-b8d4-f0a7541dcf11" isCollection="false" id="2055.afed5162-38c2-4fcb-8a37-14da854cba8f">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.hubCode = "";
autoObject.branchCode = "";
autoObject.initatorUser = "";
autoObject.branchName = "";
autoObject.hubName = "";
autoObject.branchSeq = "";
autoObject.creditAdminMakerGroup = "";
autoObject.creditAdminCheckerGroup = "";
autoObject.isLargeCorporate = false;
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="folderID" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.d086cb9c-04dc-49bf-8915-e15ad5f25550" />
                        <ns16:dataInput name="ECMproperties" itemSubjectRef="itm.12.ae22157e-8e19-4a60-a294-712ff5dc96df" isCollection="false" id="2055.d74ff120-55c7-4ec7-883b-6c355ed5b926">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.fullPath = "";
autoObject.cmisQuery = "";
autoObject.defaultProperties = [];
autoObject.defaultProperties[0] = {};
autoObject.defaultProperties[0].name = "";
autoObject.defaultProperties[0].value = null;
autoObject.defaultProperties[0].editable = false;
autoObject.defaultProperties[0].hidden = false;
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="parentPath" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.bc66ce14-d656-4a24-89ef-aba70280cada" />
                        <ns16:dataInput name="idcContract" itemSubjectRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1" isCollection="false" id="2055.8d2b550b-2372-465b-84f6-203516e6a702">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.collateralAmount = 0.0;
autoObject.userReference = "";
autoObject.settlementAccounts = [];
autoObject.settlementAccounts[0] = {};
autoObject.settlementAccounts[0].debitedAccount = {};
autoObject.settlementAccounts[0].debitedAccount.balanceSign = "";
autoObject.settlementAccounts[0].debitedAccount.GLAccountNumber = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency = {};
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.id = 0;
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.code = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.arabicdescription = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.englishdescription = "";
autoObject.settlementAccounts[0].debitedAccount.accountBranchCode = "";
autoObject.settlementAccounts[0].debitedAccount.accountBalance = 0.0;
autoObject.settlementAccounts[0].debitedAccount.accountNumber = "";
autoObject.settlementAccounts[0].debitedAccount.accountClass = "";
autoObject.settlementAccounts[0].debitedAccount.ownerAccounts = "";
autoObject.settlementAccounts[0].debitedAccount.currency = {};
autoObject.settlementAccounts[0].debitedAccount.currency.name = "";
autoObject.settlementAccounts[0].debitedAccount.currency.value = "";
autoObject.settlementAccounts[0].debitedAccount.isGLFoundC = false;
autoObject.settlementAccounts[0].debitedAccount.commCIF = "";
autoObject.settlementAccounts[0].debitedAccount.isGLVerifiedC = false;
autoObject.settlementAccounts[0].debitedAccount.isOverDraft = false;
autoObject.settlementAccounts[0].debitedAmount = {};
autoObject.settlementAccounts[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;
autoObject.settlementAccounts[0].debitedAmount.debitPercentage = 0.0;
autoObject.settlementAccounts[0].debitedAmount.standardExchangeRate = 0.0;
autoObject.settlementAccounts[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;
autoObject.settlementAccounts[0].debitedAmount.negotiatedExchangeRate = 0.0;
autoObject.settlementAccounts[0].accountNumberList = [];
autoObject.settlementAccounts[0].accountNumberList[0] = {};
autoObject.settlementAccounts[0].accountNumberList[0].name = "";
autoObject.settlementAccounts[0].accountNumberList[0].value = "";
autoObject.settlementAccounts[0].settCIF = "";
autoObject.billAmount = 0.0;
autoObject.billCurrency = {};
autoObject.billCurrency.id = 0;
autoObject.billCurrency.code = "";
autoObject.billCurrency.arabicdescription = "";
autoObject.billCurrency.englishdescription = "";
autoObject.party = [];
autoObject.party[0] = {};
autoObject.party[0].partyType = {};
autoObject.party[0].partyType.name = "";
autoObject.party[0].partyType.value = "";
autoObject.party[0].partyId = "";
autoObject.party[0].name = "";
autoObject.party[0].country = "";
autoObject.party[0].reference = "";
autoObject.party[0].address1 = "";
autoObject.party[0].address2 = "";
autoObject.party[0].address3 = "";
autoObject.party[0].address4 = "";
autoObject.party[0].media = "";
autoObject.party[0].address = "";
autoObject.party[0].phone = "";
autoObject.party[0].fax = "";
autoObject.party[0].email = "";
autoObject.party[0].contactPersonName = "";
autoObject.party[0].mobile = "";
autoObject.party[0].branch = {};
autoObject.party[0].branch.name = "";
autoObject.party[0].branch.value = "";
autoObject.party[0].language = "";
autoObject.party[0].partyCIF = "";
autoObject.party[0].isNbeCustomer = false;
autoObject.party[0].isRetrived = false;
autoObject.sourceReference = "";
autoObject.isLimitsTrackingRequired = false;
autoObject.liquidationSummary = {};
autoObject.liquidationSummary.liquidationCurrency = "";
autoObject.liquidationSummary.debitBasisby = "";
autoObject.liquidationSummary.liquidationAmt = 0.0;
autoObject.liquidationSummary.debitValueDate = new Date();
autoObject.liquidationSummary.creditValueDate = new Date();
autoObject.IDCProduct = {};
autoObject.IDCProduct.id = 0;
autoObject.IDCProduct.code = "";
autoObject.IDCProduct.arabicdescription = "";
autoObject.IDCProduct.englishdescription = "";
autoObject.interestToDate = new Date();
autoObject.transactionMaturityDate = new Date();
autoObject.commissionsAndCharges = [];
autoObject.commissionsAndCharges[0] = {};
autoObject.commissionsAndCharges[0].component = "";
autoObject.commissionsAndCharges[0].debitedAccount = {};
autoObject.commissionsAndCharges[0].debitedAccount.balanceSign = "";
autoObject.commissionsAndCharges[0].debitedAccount.GLAccountNumber = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency = {};
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.id = 0;
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.code = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.arabicdescription = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.englishdescription = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountBranchCode = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountBalance = 0.0;
autoObject.commissionsAndCharges[0].debitedAccount.accountNumber = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountClass = "";
autoObject.commissionsAndCharges[0].debitedAccount.ownerAccounts = "";
autoObject.commissionsAndCharges[0].debitedAccount.currency = {};
autoObject.commissionsAndCharges[0].debitedAccount.currency.name = "";
autoObject.commissionsAndCharges[0].debitedAccount.currency.value = "";
autoObject.commissionsAndCharges[0].debitedAccount.isGLFoundC = false;
autoObject.commissionsAndCharges[0].debitedAccount.commCIF = "";
autoObject.commissionsAndCharges[0].debitedAccount.isGLVerifiedC = false;
autoObject.commissionsAndCharges[0].debitedAccount.isOverDraft = false;
autoObject.commissionsAndCharges[0].chargeAmount = 0.0;
autoObject.commissionsAndCharges[0].waiver = false;
autoObject.commissionsAndCharges[0].debitedAmount = {};
autoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.debitPercentage = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.standardExchangeRate = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.negotiatedExchangeRate = 0.0;
autoObject.commissionsAndCharges[0].defaultCurrency = {};
autoObject.commissionsAndCharges[0].defaultCurrency.id = 0;
autoObject.commissionsAndCharges[0].defaultCurrency.code = "";
autoObject.commissionsAndCharges[0].defaultCurrency.arabicdescription = "";
autoObject.commissionsAndCharges[0].defaultCurrency.englishdescription = "";
autoObject.commissionsAndCharges[0].defaultAmount = 0.0;
autoObject.commissionsAndCharges[0].commAccountList = [];
autoObject.commissionsAndCharges[0].commAccountList[0] = {};
autoObject.commissionsAndCharges[0].commAccountList[0].name = "";
autoObject.commissionsAndCharges[0].commAccountList[0].value = "";
autoObject.transactionBaseDate = new Date();
autoObject.tradeFinanceApprovalNumber = "";
autoObject.FCContractNumber = "";
autoObject.collateralCurrency = {};
autoObject.collateralCurrency.id = 0;
autoObject.collateralCurrency.code = "";
autoObject.collateralCurrency.arabicdescription = "";
autoObject.collateralCurrency.englishdescription = "";
autoObject.interestRate = 0.0;
autoObject.transactionTransitDays = 0;
autoObject.swiftMessageData = {};
autoObject.swiftMessageData.intermediary = {};
autoObject.swiftMessageData.intermediary.line1 = "";
autoObject.swiftMessageData.intermediary.line2 = "";
autoObject.swiftMessageData.intermediary.line3 = "";
autoObject.swiftMessageData.intermediary.line4 = "";
autoObject.swiftMessageData.intermediary.line5 = "";
autoObject.swiftMessageData.intermediary.line6 = "";
autoObject.swiftMessageData.detailsOfCharge = "";
autoObject.swiftMessageData.accountWithInstitution = {};
autoObject.swiftMessageData.accountWithInstitution.line1 = "";
autoObject.swiftMessageData.accountWithInstitution.line2 = "";
autoObject.swiftMessageData.accountWithInstitution.line3 = "";
autoObject.swiftMessageData.accountWithInstitution.line4 = "";
autoObject.swiftMessageData.accountWithInstitution.line5 = "";
autoObject.swiftMessageData.accountWithInstitution.line6 = "";
autoObject.swiftMessageData.receiver = "";
autoObject.swiftMessageData.swiftMessageOption = "";
autoObject.swiftMessageData.coverRequired = "";
autoObject.swiftMessageData.transferType = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution = {};
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line1 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line2 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line3 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line4 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line5 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line6 = "";
autoObject.swiftMessageData.receiverCorrespondent = {};
autoObject.swiftMessageData.receiverCorrespondent.line1 = "";
autoObject.swiftMessageData.receiverCorrespondent.line2 = "";
autoObject.swiftMessageData.receiverCorrespondent.line3 = "";
autoObject.swiftMessageData.receiverCorrespondent.line4 = "";
autoObject.swiftMessageData.receiverCorrespondent.line5 = "";
autoObject.swiftMessageData.receiverCorrespondent.line6 = "";
autoObject.swiftMessageData.detailsOfPayment = {};
autoObject.swiftMessageData.detailsOfPayment.line1 = "";
autoObject.swiftMessageData.detailsOfPayment.line2 = "";
autoObject.swiftMessageData.detailsOfPayment.line3 = "";
autoObject.swiftMessageData.detailsOfPayment.line4 = "";
autoObject.swiftMessageData.detailsOfPayment.line5 = "";
autoObject.swiftMessageData.detailsOfPayment.line6 = "";
autoObject.swiftMessageData.orderingInstitution = {};
autoObject.swiftMessageData.orderingInstitution.line1 = "";
autoObject.swiftMessageData.orderingInstitution.line2 = "";
autoObject.swiftMessageData.orderingInstitution.line3 = "";
autoObject.swiftMessageData.orderingInstitution.line4 = "";
autoObject.swiftMessageData.orderingInstitution.line5 = "";
autoObject.swiftMessageData.orderingInstitution.line6 = "";
autoObject.swiftMessageData.beneficiaryInstitution = {};
autoObject.swiftMessageData.beneficiaryInstitution.line1 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line2 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line3 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line4 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line5 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line6 = "";
autoObject.swiftMessageData.receiverOfCover = "";
autoObject.swiftMessageData.ultimateBeneficiary = {};
autoObject.swiftMessageData.ultimateBeneficiary.line1 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line2 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line3 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line4 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line5 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line6 = "";
autoObject.swiftMessageData.orderingCustomer = {};
autoObject.swiftMessageData.orderingCustomer.line1 = "";
autoObject.swiftMessageData.orderingCustomer.line2 = "";
autoObject.swiftMessageData.orderingCustomer.line3 = "";
autoObject.swiftMessageData.orderingCustomer.line4 = "";
autoObject.swiftMessageData.orderingCustomer.line5 = "";
autoObject.swiftMessageData.orderingCustomer.line6 = "";
autoObject.swiftMessageData.senderToReciever = {};
autoObject.swiftMessageData.senderToReciever.line1 = "";
autoObject.swiftMessageData.senderToReciever.line2 = "";
autoObject.swiftMessageData.senderToReciever.line3 = "";
autoObject.swiftMessageData.senderToReciever.line4 = "";
autoObject.swiftMessageData.senderToReciever.line5 = "";
autoObject.swiftMessageData.senderToReciever.line6 = "";
autoObject.swiftMessageData.RTGS = "";
autoObject.swiftMessageData.RTGSNetworkType = "";
autoObject.advices = [];
autoObject.advices[0] = {};
autoObject.advices[0].adviceCode = "";
autoObject.advices[0].suppressed = false;
autoObject.advices[0].advicelines = {};
autoObject.advices[0].advicelines.line1 = "";
autoObject.advices[0].advicelines.line2 = "";
autoObject.advices[0].advicelines.line3 = "";
autoObject.advices[0].advicelines.line4 = "";
autoObject.advices[0].advicelines.line5 = "";
autoObject.advices[0].advicelines.line6 = "";
autoObject.cashCollateralAccounts = [];
autoObject.cashCollateralAccounts[0] = {};
autoObject.cashCollateralAccounts[0].accountCurrency = "";
autoObject.cashCollateralAccounts[0].accountClass = "";
autoObject.cashCollateralAccounts[0].debitedAmtinCollateralCurrency = 0.0;
autoObject.cashCollateralAccounts[0].GLAccountNumber = "";
autoObject.cashCollateralAccounts[0].accountBranchCode = "";
autoObject.cashCollateralAccounts[0].accountNumber = {};
autoObject.cashCollateralAccounts[0].accountNumber.name = "";
autoObject.cashCollateralAccounts[0].accountNumber.value = "";
autoObject.cashCollateralAccounts[0].isGLFound = false;
autoObject.cashCollateralAccounts[0].isGLVerified = false;
autoObject.IDCRequestStage = "";
autoObject.transactionValueDate = new Date();
autoObject.transactionTenorDays = 0;
autoObject.contractLimitsTracking = [];
autoObject.contractLimitsTracking[0] = {};
autoObject.contractLimitsTracking[0].partyType = {};
autoObject.contractLimitsTracking[0].partyType.name = "";
autoObject.contractLimitsTracking[0].partyType.value = "";
autoObject.contractLimitsTracking[0].type = "";
autoObject.contractLimitsTracking[0].jointVentureParent = "";
autoObject.contractLimitsTracking[0].customerNo = "";
autoObject.contractLimitsTracking[0].linkageRefNum = "";
autoObject.contractLimitsTracking[0].amountTag = "";
autoObject.contractLimitsTracking[0].contributionPercentage = 0.0;
autoObject.contractLimitsTracking[0].isCIFfound = false;
autoObject.interestFromDate = new Date();
autoObject.interestAmount = 0.0;
autoObject.haveInterest = false;
autoObject.accountNumberList = [];
autoObject.accountNumberList[0] = {};
autoObject.accountNumberList[0].name = "";
autoObject.accountNumberList[0].value = "";
autoObject.facilities = [];
autoObject.facilities[0] = {};
autoObject.facilities[0].facilityCode = "";
autoObject.facilities[0].overallLimit = 0.0;
autoObject.facilities[0].limitAmount = 0.0;
autoObject.facilities[0].effectiveLimitAmount = 0.0;
autoObject.facilities[0].availableAmount = 0.0;
autoObject.facilities[0].expiryDate = new Date();
autoObject.facilities[0].availableFlag = false;
autoObject.facilities[0].authorizedFlag = false;
autoObject.facilities[0].Utilization = 0.0;
autoObject.facilities[0].returnCode = "";
autoObject.facilities[0].facilityLines = [];
autoObject.facilities[0].facilityLines[0] = {};
autoObject.facilities[0].facilityLines[0].lineCode = "";
autoObject.facilities[0].facilityLines[0].lineAmount = 0.0;
autoObject.facilities[0].facilityLines[0].availableAmount = 0.0;
autoObject.facilities[0].facilityLines[0].effectiveLineAmount = 0.0;
autoObject.facilities[0].facilityLines[0].expiryDate = new Date();
autoObject.facilities[0].facilityLines[0].facilityBranch = {};
autoObject.facilities[0].facilityLines[0].facilityBranch.name = "";
autoObject.facilities[0].facilityLines[0].facilityBranch.value = "";
autoObject.facilities[0].facilityLines[0].availableFlag = false;
autoObject.facilities[0].facilityLines[0].authorizedFlag = false;
autoObject.facilities[0].facilityLines[0].facilityPercentageToBook = 0;
autoObject.facilities[0].facilityLines[0].internalRemarks = "";
autoObject.facilities[0].facilityLines[0].purpose = "";
autoObject.facilities[0].facilityLines[0].LCCommissionPercentage = 0;
autoObject.facilities[0].facilityLines[0].LCDef = "";
autoObject.facilities[0].facilityLines[0].LCCashCover = "";
autoObject.facilities[0].facilityLines[0].IDCCommission = "";
autoObject.facilities[0].facilityLines[0].IDCAvalCommPercentage = 0;
autoObject.facilities[0].facilityLines[0].IDCCashCoverPercentage = 0;
autoObject.facilities[0].facilityLines[0].debitAccountNumber = "";
autoObject.facilities[0].facilityLines[0].lineCurrency = "";
autoObject.facilities[0].facilityLines[0].lineSerialNumber = "";
autoObject.facilities[0].facilityLines[0].returnCode = "";
autoObject.facilities[0].facilityLines[0].LGCommission = 0;
autoObject.facilities[0].facilityLines[0].LGCashCover_BidBond = 0;
autoObject.facilities[0].facilityLines[0].LGCashCover_Performance = 0;
autoObject.facilities[0].facilityLines[0].LGCashCover_AdvancedPayment = 0;
autoObject.facilities[0].facilityLines[0].restrictedCurrencies = [];
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0] = {};
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].status = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches = [];
autoObject.facilities[0].facilityLines[0].restrictedBranches[0] = {};
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].status = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts = [];
autoObject.facilities[0].facilityLines[0].restrictedProducts[0] = {};
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].status = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers = [];
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0] = {};
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].status = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions = [];
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0] = {};
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].name = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].code = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].source = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].status = "";
autoObject.facilities[0].facilityLines[0].CIF = "";
autoObject.facilities[0].facilityLines[0].partyType = {};
autoObject.facilities[0].facilityLines[0].partyType.name = "";
autoObject.facilities[0].facilityLines[0].partyType.value = "";
autoObject.facilities[0].facilityLines[0].facilityID = "";
autoObject.facilities[0].status = "";
autoObject.facilities[0].facilityCurrency = {};
autoObject.facilities[0].facilityCurrency.name = "";
autoObject.facilities[0].facilityCurrency.value = "";
autoObject.facilities[0].facilityID = "";
autoObject.limitsTrackingVIs = "";
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataOutput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.feda83ff-2ac0-4dbb-8941-6c929d776d96" />
                        <ns16:dataOutput name="attachment" itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" id="2055.f616fc43-d293-443a-afb4-37d7d3abd8b7" />
                        <ns16:dataOutput name="folderID" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.02e6c09f-afcc-4424-92d7-0e28498532af" />
                        <ns16:dataOutput name="ECMproperties" itemSubjectRef="itm.12.ae22157e-8e19-4a60-a294-712ff5dc96df" isCollection="false" id="2055.6c128f72-716e-4f76-b73f-dad75e5d3156" />
                        <ns16:dataOutput name="parentPath" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.51acf25f-3a3a-4f2c-9681-d2fb03b38a02" />
                        <ns16:dataOutput name="idcContract" itemSubjectRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1" isCollection="false" id="2055.f0530dfd-3e46-4485-8c28-93a4424136c0" />
                        <ns16:inputSet />
                        <ns16:outputSet />
                    </ns16:ioSpecification>
                </ns16:globalUserTask>
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.e37a8b0e-22c8-4b98-81ae-778ada837a90</processLinkId>
            <processId>1.19d05118-4f9d-482a-afd5-662663bc3612</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.7d9060a3-db6f-4f1e-991c-a7b4c03053aa</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.788acd72-219c-464c-b855-520f2a9e257b</toProcessItemId>
            <guid>b4741579-a120-42ff-b833-c7e7963e57c4</guid>
            <versionId>2d82adf2-7af6-43d3-a37c-e3fa6b3027d4</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.7d9060a3-db6f-4f1e-991c-a7b4c03053aa</fromProcessItemId>
            <toProcessItemId>2025.788acd72-219c-464c-b855-520f2a9e257b</toProcessItemId>
        </link>
    </process>
</teamworks>

