<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.417ae6ff-837a-426f-b651-d8e3e8810d90" name="Check Existing GL Account">
        <lastModified>*************</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.417ae6ff-837a-426f-b651-d8e3e8810d90</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.8b78165b-6fee-4c56-bce5-40ba5d5b9e7b</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>af47f72d-94a5-411f-bc0b-2b3add6c73ee</guid>
        <versionId>64cf0631-8b5b-4884-8a3a-ab390d205a43</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:56df" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.182a52cf-2a33-4b09-95ea-c08f04a37a19"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"7ab9e7eb-9d1e-43e5-bfef-b753b544ad7a"},{"incoming":["141c186f-d673-40e2-b31e-b3156dc37914","f8ab7ee1-c745-4913-89c0-696c86aa7390"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-616c"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"a98b7a2b-acb0-4996-b502-2d7cc99c3bc7"},{"targetRef":"8b78165b-6fee-4c56-bce5-40ba5d5b9e7b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Check Existing GL Account","declaredType":"sequenceFlow","id":"2027.182a52cf-2a33-4b09-95ea-c08f04a37a19","sourceRef":"7ab9e7eb-9d1e-43e5-bfef-b753b544ad7a"},{"startQuantity":1,"outgoing":["141c186f-d673-40e2-b31e-b3156dc37914"],"incoming":["2027.182a52cf-2a33-4b09-95ea-c08f04a37a19"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":150,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[],"activityType":["CalledProcess"]},"name":"Check Existing GL Account","dataInputAssociation":[{"targetRef":"2055.c18a6899-6013-4345-8ce9-739a16ed9634","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.local.data"]}}]},{"targetRef":"2055.4d5b4910-bd3a-47f4-8c11-66d8090a2e50","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.0126d85c-445e-4538-8a6e-601eb37de77a","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user_id"]}}]},{"targetRef":"2055.ed1918e4-41d8-403a-8849-7849e628fc5f","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.currentProcessInstanceID"]}}]},{"targetRef":"2055.9f586901-6b8c-4c19-83f1-35cea5db49bb","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"INWARD DOCUMENTARY COLLECTION\""]}}]},{"targetRef":"2055.9b6a1701-f2e0-4f28-8be3-23c434bd009b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.a520c6ba-6e3b-4054-8f5f-1988df4c9f7c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"8b78165b-6fee-4c56-bce5-40ba5d5b9e7b","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.c2601476-984a-4451-8b74-9e89fd877490"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.f2d60f9e-6b2b-4c46-8e1e-92afe56fe880"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.90a47e75-99e0-4027-8e62-c28e5c779d17"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorCode"]}}],"sourceRef":["2055.dc3d81e0-f34c-4006-8a4a-9b4a01525cf0"]}],"calledElement":"1.8d5bbf92-4c72-4227-82b8-0d9a3444fcf2"},{"targetRef":"a98b7a2b-acb0-4996-b502-2d7cc99c3bc7","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:4532aa1aa99c10af:490ccb33:188befc8bcd:-218"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To is Successful","declaredType":"sequenceFlow","id":"141c186f-d673-40e2-b31e-b3156dc37914","sourceRef":"8b78165b-6fee-4c56-bce5-40ba5d5b9e7b"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"instanceID","isCollection":false,"declaredType":"dataObject","id":"2056.372991e0-39be-4c6d-8f74-2123bc596e35"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"prefix","isCollection":false,"declaredType":"dataObject","id":"2056.5ea2791f-8a2c-482f-83b3-3b2ee8114e43"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"processName","isCollection":false,"declaredType":"dataObject","id":"2056.22db3ab3-4d0b-40d5-8430-2197d9051860"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestAppID","isCollection":false,"declaredType":"dataObject","id":"2056.e6b246b8-686c-4c50-8eb0-33009a6e71f9"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"snapshot","isCollection":false,"declaredType":"dataObject","id":"2056.2c4d52a6-dc4d-4e06-84d1-6489d71eb37f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"userID","isCollection":false,"declaredType":"dataObject","id":"2056.c070116b-7771-400d-8fa2-437115acceda"},{"parallelMultiple":false,"outgoing":["6c8d4335-3097-4d70-83d1-c43f9e480bda"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"a043c561-b18f-4763-8e93-d77ea0db597f"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"ebe79d76-603b-480b-8782-4ed8fe380cf3","otherAttributes":{"eventImplId":"5ba12a84-33c0-472c-85b6-339b1ff74913"}}],"attachedToRef":"8b78165b-6fee-4c56-bce5-40ba5d5b9e7b","extensionElements":{"nodeVisualInfo":[{"width":24,"x":185,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"585a79c4-cb9f-4118-80f2-9846a0098db6","outputSet":{}},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.c7fa1ba0-93ba-4041-80a9-76c91954fd74"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.08a61fad-fc92-4542-8718-7d2a37dab8ad"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.395ab416-b106-41f6-8649-cb7c36cd8763"},{"startQuantity":1,"outgoing":["f8ab7ee1-c745-4913-89c0-696c86aa7390"],"incoming":["6c8d4335-3097-4d70-83d1-c43f9e480bda"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":254,"y":176,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"180d3dbe-1bc1-4671-81a4-f8e420f87e95","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"targetRef":"180d3dbe-1bc1-4671-81a4-f8e420f87e95","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"6c8d4335-3097-4d70-83d1-c43f9e480bda","sourceRef":"585a79c4-cb9f-4118-80f2-9846a0098db6"},{"targetRef":"a98b7a2b-acb0-4996-b502-2d7cc99c3bc7","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"f8ab7ee1-c745-4913-89c0-696c86aa7390","sourceRef":"180d3dbe-1bc1-4671-81a4-f8e420f87e95"}],"laneSet":[{"id":"fee2cf18-b540-4d1e-84cb-3e52f32c83c8","lane":[{"flowNodeRef":["7ab9e7eb-9d1e-43e5-bfef-b753b544ad7a","a98b7a2b-acb0-4996-b502-2d7cc99c3bc7","8b78165b-6fee-4c56-bce5-40ba5d5b9e7b","585a79c4-cb9f-4118-80f2-9846a0098db6","180d3dbe-1bc1-4671-81a4-f8e420f87e95"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"d6acd287-3812-425f-a66f-fb6a2d83e17f","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Check Existing GL Account","declaredType":"process","id":"1.417ae6ff-837a-426f-b651-d8e3e8810d90","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.afd80a03-8f97-415a-a4b4-3b943fefd19a"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.4c77dcb6-94d8-45d1-b6d2-55c6e6dcfcdd"}],"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"222BILC05\""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.cc67ae2c-c123-435d-bbdf-a42cac4e27e1"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.cc67ae2c-c123-435d-bbdf-a42cac4e27e1</processParameterId>
            <processId>1.417ae6ff-837a-426f-b651-d8e3e8810d90</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d3ecfe5c-8aa5-48fc-be4e-f4f6fb08afad</guid>
            <versionId>e932ff39-bfce-4f92-94ff-2c424dbab654</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.afd80a03-8f97-415a-a4b4-3b943fefd19a</processParameterId>
            <processId>1.417ae6ff-837a-426f-b651-d8e3e8810d90</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>01c7a12a-0c99-4c9b-ae94-3341b6262ebb</guid>
            <versionId>bcd9c7c2-43b0-4954-a742-965087e1a279</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.4c77dcb6-94d8-45d1-b6d2-55c6e6dcfcdd</processParameterId>
            <processId>1.417ae6ff-837a-426f-b651-d8e3e8810d90</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>5a2bc9fc-660e-47e7-b77c-1b722577832b</guid>
            <versionId>38dcf440-4bdc-402e-aa22-1283898f6bb5</versionId>
        </processParameter>
        <processVariable name="instanceID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.372991e0-39be-4c6d-8f74-2123bc596e35</processVariableId>
            <description isNull="true" />
            <processId>1.417ae6ff-837a-426f-b651-d8e3e8810d90</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>cf6c9883-ffd8-473f-af36-a57a9b9e578a</guid>
            <versionId>709321fd-ae8e-4583-a3e3-b2456c89472c</versionId>
        </processVariable>
        <processVariable name="prefix">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5ea2791f-8a2c-482f-83b3-3b2ee8114e43</processVariableId>
            <description isNull="true" />
            <processId>1.417ae6ff-837a-426f-b651-d8e3e8810d90</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>edfd780b-02db-4504-bc23-95e28c6e57ae</guid>
            <versionId>c6c9d12e-2c4a-4c58-a8f3-47f50a2cb17c</versionId>
        </processVariable>
        <processVariable name="processName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.22db3ab3-4d0b-40d5-8430-2197d9051860</processVariableId>
            <description isNull="true" />
            <processId>1.417ae6ff-837a-426f-b651-d8e3e8810d90</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6db14d26-2b15-4383-9949-216f96f0ff69</guid>
            <versionId>f53fa1d8-fe3a-44cb-ba0f-417b8a6d6d23</versionId>
        </processVariable>
        <processVariable name="requestAppID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e6b246b8-686c-4c50-8eb0-33009a6e71f9</processVariableId>
            <description isNull="true" />
            <processId>1.417ae6ff-837a-426f-b651-d8e3e8810d90</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e3bc0925-30d8-46a9-a2e6-6e4c41405928</guid>
            <versionId>97e860b7-d9d9-46d5-9a8f-6e4a1f3040a5</versionId>
        </processVariable>
        <processVariable name="snapshot">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2c4d52a6-dc4d-4e06-84d1-6489d71eb37f</processVariableId>
            <description isNull="true" />
            <processId>1.417ae6ff-837a-426f-b651-d8e3e8810d90</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>cf73cec7-f6a4-489c-8ecd-eee6e5e378c5</guid>
            <versionId>faf47f9d-c34d-48e0-bd08-bb967bf1e5d9</versionId>
        </processVariable>
        <processVariable name="userID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c070116b-7771-400d-8fa2-437115acceda</processVariableId>
            <description isNull="true" />
            <processId>1.417ae6ff-837a-426f-b651-d8e3e8810d90</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4c56b278-107f-4cd0-9ed0-ae0b79b36dc0</guid>
            <versionId>f6b99c88-5ba0-4a11-9651-1f6bbb75b228</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c7fa1ba0-93ba-4041-80a9-76c91954fd74</processVariableId>
            <description isNull="true" />
            <processId>1.417ae6ff-837a-426f-b651-d8e3e8810d90</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ba11c633-87b1-4a1f-9e23-3956f29a6468</guid>
            <versionId>219e99e4-8841-43f1-a4e5-aa6419b6842f</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.08a61fad-fc92-4542-8718-7d2a37dab8ad</processVariableId>
            <description isNull="true" />
            <processId>1.417ae6ff-837a-426f-b651-d8e3e8810d90</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>49bcadb2-b71d-4183-81d8-3d528f8a0fec</guid>
            <versionId>ba92827e-bf72-4773-9b28-9ea5c3406769</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.395ab416-b106-41f6-8649-cb7c36cd8763</processVariableId>
            <description isNull="true" />
            <processId>1.417ae6ff-837a-426f-b651-d8e3e8810d90</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0643cfb6-9854-4ed2-a57e-b48b54de3a15</guid>
            <versionId>ce01d07b-a1c7-4d0c-8eef-506d6dd1474c</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.180d3dbe-1bc1-4671-81a4-f8e420f87e95</processItemId>
            <processId>1.417ae6ff-837a-426f-b651-d8e3e8810d90</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.e67ffc43-b60e-446b-bd59-bb6ca88f39ec</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d53</guid>
            <versionId>2fd8d942-da64-46d9-89f2-c82df5770373</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="254" y="176">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.e67ffc43-b60e-446b-bd59-bb6ca88f39ec</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>3a392a8b-ded7-4dd1-a130-71e5c19b9e81</guid>
                <versionId>5246a9a0-437a-4c4b-a2ab-f2b0168a66b0</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.a98b7a2b-acb0-4996-b502-2d7cc99c3bc7</processItemId>
            <processId>1.417ae6ff-837a-426f-b651-d8e3e8810d90</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.f8060baa-ecb3-405f-830b-5cfc32cd7b85</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-616c</guid>
            <versionId>*************-473e-a79a-01f33ba68775</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.f8060baa-ecb3-405f-830b-5cfc32cd7b85</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>9a79a22f-6f21-49a3-8ff4-075933df51d8</guid>
                <versionId>3b80c5ed-a958-463d-9bf8-78cee9951ec6</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8b78165b-6fee-4c56-bce5-40ba5d5b9e7b</processItemId>
            <processId>1.417ae6ff-837a-426f-b651-d8e3e8810d90</processId>
            <name>Check Existing GL Account</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.3ffdae89-f13b-4225-ac2a-d44a564f6298</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.180d3dbe-1bc1-4671-81a4-f8e420f87e95</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-616d</guid>
            <versionId>c5328e1f-8a32-4f26-85ef-1d126e464344</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.dd95fbdc-d474-4864-831a-5b4e7369018e</processItemPrePostId>
                <processItemId>2025.8b78165b-6fee-4c56-bce5-40ba5d5b9e7b</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>35be9141-354c-4208-8ada-d95342499787</guid>
                <versionId>89f5fe5c-f1aa-4c97-8467-f1b088ae40e0</versionId>
            </processPrePosts>
            <layoutData x="150" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3d53</errorHandlerItem>
                <errorHandlerItemId>2025.180d3dbe-1bc1-4671-81a4-f8e420f87e95</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.3ffdae89-f13b-4225-ac2a-d44a564f6298</subProcessId>
                <attachedProcessRef>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/1.8d5bbf92-4c72-4227-82b8-0d9a3444fcf2</attachedProcessRef>
                <guid>8625dbc8-8865-4934-91d0-3e0362b7a6f8</guid>
                <versionId>6f7083de-b883-44de-84e5-d11dfb5e0910</versionId>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b1938f74-96d7-4aa3-af2b-04ef5127fe2e</parameterMappingId>
                    <processParameterId>2055.ed1918e4-41d8-403a-8849-7849e628fc5f</processParameterId>
                    <parameterMappingParentId>3012.3ffdae89-f13b-4225-ac2a-d44a564f6298</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.currentProcessInstanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>e1793cd3-47e8-46b6-85b2-676282147ac8</guid>
                    <versionId>25acb44a-8872-4578-9eca-34c366a5b7ae</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isFound">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.1f8d6e7b-2a1c-4b9b-8289-d61b390d8cc8</parameterMappingId>
                    <processParameterId>2055.c2601476-984a-4451-8b74-9e89fd877490</processParameterId>
                    <parameterMappingParentId>3012.3ffdae89-f13b-4225-ac2a-d44a564f6298</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>94c5a3fe-d114-4846-860d-07e9e5b7f197</guid>
                    <versionId>39c54a3e-38c9-4cca-927c-ed83c5ff113a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.5a82148e-7e7a-44b2-9869-43d4537e8bb2</parameterMappingId>
                    <processParameterId>2055.90a47e75-99e0-4027-8e62-c28e5c779d17</processParameterId>
                    <parameterMappingParentId>3012.3ffdae89-f13b-4225-ac2a-d44a564f6298</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>c778052b-ac48-4d89-b275-0b36e83d40df</guid>
                    <versionId>5e3ff59c-ad40-4dac-90e5-1fcc1b227cfd</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.29889093-b790-477f-ba23-498adc811fa7</parameterMappingId>
                    <processParameterId>2055.a520c6ba-6e3b-4054-8f5f-1988df4c9f7c</processParameterId>
                    <parameterMappingParentId>3012.3ffdae89-f13b-4225-ac2a-d44a564f6298</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>c8c4dafb-155c-43d6-bd99-c04271b5d621</guid>
                    <versionId>5fe6a9da-e90c-4e53-97cc-d5242dfd38cf</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b0c6718d-8113-45dd-8d64-c1360c550966</parameterMappingId>
                    <processParameterId>2055.4d5b4910-bd3a-47f4-8c11-66d8090a2e50</processParameterId>
                    <parameterMappingParentId>3012.3ffdae89-f13b-4225-ac2a-d44a564f6298</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>f3013aa5-014f-4334-8406-4aecceea011b</guid>
                    <versionId>82db7c0f-4539-40a5-b868-19a97ecf5c28</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.9b552fa9-354e-48aa-b937-fefa0f12c638</parameterMappingId>
                    <processParameterId>2055.0126d85c-445e-4538-8a6e-601eb37de77a</processParameterId>
                    <parameterMappingParentId>3012.3ffdae89-f13b-4225-ac2a-d44a564f6298</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>6e0bd10e-75eb-40aa-b3c2-c8e2ee76bf4a</guid>
                    <versionId>8bf94bbd-f682-416a-92eb-c9eec9354401</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="glAccountNumber">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.cd520db2-543d-4120-8d83-a8c3f24eedb6</parameterMappingId>
                    <processParameterId>2055.c18a6899-6013-4345-8ce9-739a16ed9634</processParameterId>
                    <parameterMappingParentId>3012.3ffdae89-f13b-4225-ac2a-d44a564f6298</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.data</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>5d7c6b16-c72c-43cc-8623-68f73cce6c2c</guid>
                    <versionId>8ffbf938-16a7-4d7e-940e-c69db469afd6</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6ce28b6b-4757-4134-9fcf-4e7309d353b6</parameterMappingId>
                    <processParameterId>2055.dc3d81e0-f34c-4006-8a4a-9b4a01525cf0</processParameterId>
                    <parameterMappingParentId>3012.3ffdae89-f13b-4225-ac2a-d44a564f6298</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>1eabe0b7-f8ed-4754-8b47-ccb6a50deb1b</guid>
                    <versionId>bf0a0dbc-da39-4739-9c21-2018291f5254</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.38bb53aa-204e-4b60-94df-7854bfbe4acf</parameterMappingId>
                    <processParameterId>2055.f2d60f9e-6b2b-4c46-8e1e-92afe56fe880</processParameterId>
                    <parameterMappingParentId>3012.3ffdae89-f13b-4225-ac2a-d44a564f6298</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>6d55bb0d-289c-427c-9bcf-c36adbd033d1</guid>
                    <versionId>c38a768d-728d-49cc-9b2f-8f43d8df9289</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.885185cb-71c1-4904-aabc-2f07a877709e</parameterMappingId>
                    <processParameterId>2055.9f586901-6b8c-4c19-83f1-35cea5db49bb</processParameterId>
                    <parameterMappingParentId>3012.3ffdae89-f13b-4225-ac2a-d44a564f6298</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"INWARD DOCUMENTARY COLLECTION"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>0f1019d8-f1dc-4bac-b415-fc2082c029ee</guid>
                    <versionId>cc08bb71-1112-4a36-8789-436d9ec01a0b</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.db5ebe20-d599-4fd2-ae69-09d85059fde6</parameterMappingId>
                    <processParameterId>2055.9b6a1701-f2e0-4f28-8be3-23c434bd009b</processParameterId>
                    <parameterMappingParentId>3012.3ffdae89-f13b-4225-ac2a-d44a564f6298</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>14407944-7ab2-40da-830e-f020131b8906</guid>
                    <versionId>e92e52f7-e6f0-4e06-80ce-30d1e1822b00</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.8b78165b-6fee-4c56-bce5-40ba5d5b9e7b</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Check Existing GL Account" id="1.417ae6ff-837a-426f-b651-d8e3e8810d90" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.cc67ae2c-c123-435d-bbdf-a42cac4e27e1">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"222BILC05"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.afd80a03-8f97-415a-a4b4-3b943fefd19a" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.4c77dcb6-94d8-45d1-b6d2-55c6e6dcfcdd" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="fee2cf18-b540-4d1e-84cb-3e52f32c83c8">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="d6acd287-3812-425f-a66f-fb6a2d83e17f" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>7ab9e7eb-9d1e-43e5-bfef-b753b544ad7a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a98b7a2b-acb0-4996-b502-2d7cc99c3bc7</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8b78165b-6fee-4c56-bce5-40ba5d5b9e7b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>585a79c4-cb9f-4118-80f2-9846a0098db6</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>180d3dbe-1bc1-4671-81a4-f8e420f87e95</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="7ab9e7eb-9d1e-43e5-bfef-b753b544ad7a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.182a52cf-2a33-4b09-95ea-c08f04a37a19</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="a98b7a2b-acb0-4996-b502-2d7cc99c3bc7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-616c</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>141c186f-d673-40e2-b31e-b3156dc37914</ns16:incoming>
                        
                        
                        <ns16:incoming>f8ab7ee1-c745-4913-89c0-696c86aa7390</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="7ab9e7eb-9d1e-43e5-bfef-b753b544ad7a" targetRef="8b78165b-6fee-4c56-bce5-40ba5d5b9e7b" name="To Check Existing GL Account" id="2027.182a52cf-2a33-4b09-95ea-c08f04a37a19">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.8d5bbf92-4c72-4227-82b8-0d9a3444fcf2" name="Check Existing GL Account" id="8b78165b-6fee-4c56-bce5-40ba5d5b9e7b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="150" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.182a52cf-2a33-4b09-95ea-c08f04a37a19</ns16:incoming>
                        
                        
                        <ns16:outgoing>141c186f-d673-40e2-b31e-b3156dc37914</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.c18a6899-6013-4345-8ce9-739a16ed9634</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.data</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.4d5b4910-bd3a-47f4-8c11-66d8090a2e50</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.0126d85c-445e-4538-8a6e-601eb37de77a</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ed1918e4-41d8-403a-8849-7849e628fc5f</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.currentProcessInstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9f586901-6b8c-4c19-83f1-35cea5db49bb</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"INWARD DOCUMENTARY COLLECTION"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9b6a1701-f2e0-4f28-8be3-23c434bd009b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a520c6ba-6e3b-4054-8f5f-1988df4c9f7c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.c2601476-984a-4451-8b74-9e89fd877490</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.f2d60f9e-6b2b-4c46-8e1e-92afe56fe880</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.90a47e75-99e0-4027-8e62-c28e5c779d17</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.dc3d81e0-f34c-4006-8a4a-9b4a01525cf0</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="8b78165b-6fee-4c56-bce5-40ba5d5b9e7b" targetRef="a98b7a2b-acb0-4996-b502-2d7cc99c3bc7" name="To is Successful" id="141c186f-d673-40e2-b31e-b3156dc37914">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:4532aa1aa99c10af:490ccb33:188befc8bcd:-218</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="instanceID" id="2056.372991e0-39be-4c6d-8f74-2123bc596e35" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="prefix" id="2056.5ea2791f-8a2c-482f-83b3-3b2ee8114e43" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="processName" id="2056.22db3ab3-4d0b-40d5-8430-2197d9051860" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="requestAppID" id="2056.e6b246b8-686c-4c50-8eb0-33009a6e71f9" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="snapshot" id="2056.2c4d52a6-dc4d-4e06-84d1-6489d71eb37f" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="userID" id="2056.c070116b-7771-400d-8fa2-437115acceda" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="8b78165b-6fee-4c56-bce5-40ba5d5b9e7b" parallelMultiple="false" name="Error" id="585a79c4-cb9f-4118-80f2-9846a0098db6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="185" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>6c8d4335-3097-4d70-83d1-c43f9e480bda</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="a043c561-b18f-4763-8e93-d77ea0db597f" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="ebe79d76-603b-480b-8782-4ed8fe380cf3" eventImplId="5ba12a84-33c0-472c-85b6-339b1ff74913">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.c7fa1ba0-93ba-4041-80a9-76c91954fd74" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.08a61fad-fc92-4542-8718-7d2a37dab8ad" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.395ab416-b106-41f6-8649-cb7c36cd8763" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Catch Errors" id="180d3dbe-1bc1-4671-81a4-f8e420f87e95">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="254" y="176" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>6c8d4335-3097-4d70-83d1-c43f9e480bda</ns16:incoming>
                        
                        
                        <ns16:outgoing>f8ab7ee1-c745-4913-89c0-696c86aa7390</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="585a79c4-cb9f-4118-80f2-9846a0098db6" targetRef="180d3dbe-1bc1-4671-81a4-f8e420f87e95" name="To Catch Errors" id="6c8d4335-3097-4d70-83d1-c43f9e480bda">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="180d3dbe-1bc1-4671-81a4-f8e420f87e95" targetRef="a98b7a2b-acb0-4996-b502-2d7cc99c3bc7" name="To End" id="f8ab7ee1-c745-4913-89c0-696c86aa7390">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To is Successful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.141c186f-d673-40e2-b31e-b3156dc37914</processLinkId>
            <processId>1.417ae6ff-837a-426f-b651-d8e3e8810d90</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8b78165b-6fee-4c56-bce5-40ba5d5b9e7b</fromProcessItemId>
            <endStateId>guid:4532aa1aa99c10af:490ccb33:188befc8bcd:-218</endStateId>
            <toProcessItemId>2025.a98b7a2b-acb0-4996-b502-2d7cc99c3bc7</toProcessItemId>
            <guid>0426d138-3c66-46c0-bf51-ee57b328db1c</guid>
            <versionId>6cdf140d-9ab5-48f5-a1d2-632d1bc8b999</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.8b78165b-6fee-4c56-bce5-40ba5d5b9e7b</fromProcessItemId>
            <toProcessItemId>2025.a98b7a2b-acb0-4996-b502-2d7cc99c3bc7</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.f8ab7ee1-c745-4913-89c0-696c86aa7390</processLinkId>
            <processId>1.417ae6ff-837a-426f-b651-d8e3e8810d90</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.180d3dbe-1bc1-4671-81a4-f8e420f87e95</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.a98b7a2b-acb0-4996-b502-2d7cc99c3bc7</toProcessItemId>
            <guid>04b72563-0ddd-4a3a-a1f0-0139a5e83d4c</guid>
            <versionId>7516c776-7024-4ad2-bc4f-10659c8c39e9</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.180d3dbe-1bc1-4671-81a4-f8e420f87e95</fromProcessItemId>
            <toProcessItemId>2025.a98b7a2b-acb0-4996-b502-2d7cc99c3bc7</toProcessItemId>
        </link>
    </process>
</teamworks>

