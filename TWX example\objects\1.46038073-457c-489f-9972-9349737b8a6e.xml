<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.46038073-457c-489f-9972-9349737b8a6e" name="Get Advices Code">
        <lastModified>1692505593488</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.22d0b73c-745c-4fd2-8474-8a25faaf7595</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>92e4f60b-1822-442b-8c61-309d0993cede</guid>
        <versionId>d76e2b87-c084-41a4-aa81-1336fec3d7f3</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3ddd" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.19c3f603-d22d-4e37-9934-309d08de89c6"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"e78c96e0-2f12-4787-9b42-dedf79a5f214"},{"incoming":["fb4f652a-3826-4ab8-a964-4df27d199cad","2205309e-d02a-442d-8566-1dae69ebf739"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":750,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6129"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"3ec6be20-f12b-4f7f-827d-6dae1a6b13e7"},{"targetRef":"22d0b73c-745c-4fd2-8474-8a25faaf7595","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Split Input","declaredType":"sequenceFlow","id":"2027.19c3f603-d22d-4e37-9934-309d08de89c6","sourceRef":"e78c96e0-2f12-4787-9b42-dedf79a5f214"},{"startQuantity":1,"outgoing":["a01182d1-3636-48a3-91a7-aabaf72f56de"],"incoming":["1d4e8747-560f-4763-8e03-dceae38eab17"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":262,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Get Advices Code","dataInputAssociation":[{"targetRef":"2055.0b74c51b-ca9c-490f-8ee6-e8be47d8f027","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.7a07822a-1f5f-44da-8fae-0075fb92bf76","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user_id"]}}]},{"targetRef":"2055.9d2f2d0c-5f60-4a4d-8ca0-fb7d44ed42ce","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.currentProcessInstanceID"]}}]},{"targetRef":"2055.e4184c04-e76c-4ea9-8baf-0fb1ab41692f","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"INWARD DOCUMENTARY COLLECTION\""]}}]},{"targetRef":"2055.992bd140-a6f1-437f-8861-f96fedaeb186","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.41a53d37-78d4-4cb0-8e5e-0f8ea909de5f","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.f2a97b03-5de9-4536-8c7f-8f064b712475","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.productCode"]}}]},{"targetRef":"2055.51d39b31-97ab-42d1-8e27-bcb070ffa0f2","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.eventCode"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"a2f65502-fcae-4a71-bbe7-8631d9e1748c","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.a2b0a889-ce35-486c-880d-83b1cba185d6","declaredType":"TFormalExpression","content":["tw.local.advicesList"]}}],"sourceRef":["2055.ea62e4d3-568a-4e69-891a-318bf659a9b3"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.75a72db9-2ec3-4e27-85d7-595d65e7317d"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.6fa968cb-917f-4651-82a5-be38ec7f213f"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorCode"]}}],"sourceRef":["2055.56a8ebe2-ba94-49eb-8220-f23c978a35be"]}],"calledElement":"1.3c3cf284-fa8f-42d5-a037-16bd9da3d099"},{"targetRef":"fa8604c2-ca48-42bc-8bde-c5d63ef06e5e","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:502f0309b7e9a7eb:-5dac1a55:188bbb8ea24:-5c3e"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To is Successful","declaredType":"sequenceFlow","id":"a01182d1-3636-48a3-91a7-aabaf72f56de","sourceRef":"a2f65502-fcae-4a71-bbe7-8631d9e1748c"},{"startQuantity":1,"outgoing":["fb4f652a-3826-4ab8-a964-4df27d199cad"],"incoming":["955caae0-1911-4565-8d2a-5f82fbb6a35f"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":526,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Map data","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"290d3524-87d6-4a7e-8ee8-9271da3bef2e","scriptFormat":"text\/x-javascript","script":{"content":["try{\r\n\ttw.local.results = new tw.object.listOf.String();\r\n\tif (tw.local.isSuccessful) {\r\n\t\ttw.local.results = new tw.object.listOf.String();\r\n\t\r\n\t\tfor (var i=0; i&lt; tw.local.advicesList.listLength; i++) {\r\n\t\t\r\n\t\t\ttw.local.results[i] = tw.local.advicesList[i].fftCode;\r\n\t\t}\r\n\t}\r\n} catch (err) {\r\n\ttw.local.errorMSG = err.message;\r\n\tthrow new Error(tw.local.errorMSG);\r\n}\r\n"]}},{"targetRef":"3ec6be20-f12b-4f7f-827d-6dae1a6b13e7","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"fb4f652a-3826-4ab8-a964-4df27d199cad","sourceRef":"290d3524-87d6-4a7e-8ee8-9271da3bef2e"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.listOf.toolkit.NBEINTT.MWAdvicesArrayList();\nautoObject[0] = new tw.object.toolkit.NBEINTT.MWAdvicesArrayList();\nautoObject[0].productCode = \"\";\nautoObject[0].eventCode = \"\";\nautoObject[0].messageType = \"\";\nautoObject[0].fftCode = \"\";\nautoObject[0].type = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.a2b0a889-ce35-486c-880d-83b1cba185d6","name":"advicesList","isCollection":true,"declaredType":"dataObject","id":"2056.0f5022ae-a14b-4ecf-b00c-8c0631fcc176"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"eventCode","isCollection":false,"declaredType":"dataObject","id":"2056.97f4b076-e210-4d6b-85e0-cf8101c950c9"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"productCode","isCollection":false,"declaredType":"dataObject","id":"2056.7443c448-e9c8-4714-85da-9a909204475c"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"instanceID","isCollection":false,"declaredType":"dataObject","id":"2056.a8a72793-c3d3-4dae-87e7-d380b453e4a9"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"prefix","isCollection":false,"declaredType":"dataObject","id":"2056.f3f9fa20-4230-4b35-b054-768a2ad08d0c"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"processName","isCollection":false,"declaredType":"dataObject","id":"2056.b60ec26b-c49a-460e-a9bc-213352aff6ff"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestAppID","isCollection":false,"declaredType":"dataObject","id":"2056.169063f9-0c61-4947-ac16-3c4ddeeb5b61"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"snapshot","isCollection":false,"declaredType":"dataObject","id":"2056.24e25f88-38f6-4219-9a50-9688fc2368bc"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"userID","isCollection":false,"declaredType":"dataObject","id":"2056.e1ee8495-e2d2-41bc-95a5-2a07ffd608ac"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.5cddcb6c-13ec-4263-842e-75d69dcf6054"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.9a596747-cebe-4592-8736-560728f83b62"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.dd410d45-333b-458d-851c-bfb646a22e81"},{"startQuantity":1,"outgoing":["1d4e8747-560f-4763-8e03-dceae38eab17"],"incoming":["2027.19c3f603-d22d-4e37-9934-309d08de89c6"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":100,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Split Input","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"22d0b73c-745c-4fd2-8474-8a25faaf7595","scriptFormat":"text\/x-javascript","script":{"content":["try{\r\n\ttw.local.Seperated = new tw.object.listOf.String();\r\n\ttw.local.Seperated = tw.local.data.split(\"-\");\r\n\ttw.local.productCode = tw.local.Seperated[1];\r\n\t\r\n\tif (tw.local.Seperated[0]==\"Initial\") {\r\n\t\ttw.local.eventCode = \"BOOK\";\t\r\n\t}\r\n\telse{\r\n\t\ttw.local.eventCode = \"INIT\";\r\n\t}\r\n} catch (err) {\r\n\ttw.local.errorMSG = err.message;\r\n\tthrow new Error(tw.local.errorMSG);\r\n}\r\n"]}},{"targetRef":"a2f65502-fcae-4a71-bbe7-8631d9e1748c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get Advices Code","declaredType":"sequenceFlow","id":"1d4e8747-560f-4763-8e03-dceae38eab17","sourceRef":"22d0b73c-745c-4fd2-8474-8a25faaf7595"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"Seperated","isCollection":true,"declaredType":"dataObject","id":"2056.b327460f-3d09-46f2-8cf7-804548fd1db2"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.80646637-e9ca-4b35-81b8-7d90121422b4"},{"outgoing":["955caae0-1911-4565-8d2a-5f82fbb6a35f","b1410ae6-e01f-4acc-8d9d-60194af0a5ee"],"incoming":["a01182d1-3636-48a3-91a7-aabaf72f56de"],"default":"955caae0-1911-4565-8d2a-5f82fbb6a35f","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":375,"y":76,"declaredType":"TNodeVisualInfo","height":32}]},"name":"is Successful","declaredType":"exclusiveGateway","id":"fa8604c2-ca48-42bc-8bde-c5d63ef06e5e"},{"targetRef":"290d3524-87d6-4a7e-8ee8-9271da3bef2e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Map data","declaredType":"sequenceFlow","id":"955caae0-1911-4565-8d2a-5f82fbb6a35f","sourceRef":"fa8604c2-ca48-42bc-8bde-c5d63ef06e5e"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error2","isCollection":false,"declaredType":"dataObject","id":"2056.924c2d73-ea7b-4cc7-8067-06f0effc3a0b"},{"targetRef":"db8a3359-6e24-4a8f-8a54-739f0bc2063f","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.isSuccessful\t  ==\t  false"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End Event","declaredType":"sequenceFlow","id":"b1410ae6-e01f-4acc-8d9d-60194af0a5ee","sourceRef":"fa8604c2-ca48-42bc-8bde-c5d63ef06e5e"},{"parallelMultiple":false,"outgoing":["7c8a389e-4dc1-4b42-821e-38c690a61f1e"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"5ba87848-06b6-460b-88f9-c5ebdd6924e6"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"5c2b3ee2-9753-4efc-8558-0cbcf8ae8ff5","otherAttributes":{"eventImplId":"0ca012c2-dbac-425f-80b3-360fca9ea583"}}],"attachedToRef":"22d0b73c-745c-4fd2-8474-8a25faaf7595","extensionElements":{"nodeVisualInfo":[{"width":24,"x":135,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"b1d8b957-f9ba-4305-8717-c2de589db90c","outputSet":{}},{"parallelMultiple":false,"outgoing":["0fc59d9a-33b7-4b59-886d-976818132547"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"c4902546-3c91-4c27-8782-091fac976bcf"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"af75e5b0-2c61-4513-8404-538e86ab6ddd","otherAttributes":{"eventImplId":"dd75c63f-5f51-4609-8bb4-80c82650d40c"}}],"attachedToRef":"290d3524-87d6-4a7e-8ee8-9271da3bef2e","extensionElements":{"nodeVisualInfo":[{"width":24,"x":561,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"0cf6ab82-98a5-4564-8c33-c430eb8393fe","outputSet":{}},{"targetRef":"db8a3359-6e24-4a8f-8a54-739f0bc2063f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Error","declaredType":"sequenceFlow","id":"7c8a389e-4dc1-4b42-821e-38c690a61f1e","sourceRef":"b1d8b957-f9ba-4305-8717-c2de589db90c"},{"targetRef":"db8a3359-6e24-4a8f-8a54-739f0bc2063f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Error","declaredType":"sequenceFlow","id":"0fc59d9a-33b7-4b59-886d-976818132547","sourceRef":"0cf6ab82-98a5-4564-8c33-c430eb8393fe"},{"startQuantity":1,"outgoing":["2205309e-d02a-442d-8566-1dae69ebf739"],"incoming":["b1410ae6-e01f-4acc-8d9d-60194af0a5ee","7c8a389e-4dc1-4b42-821e-38c690a61f1e","0fc59d9a-33b7-4b59-886d-976818132547"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":525,"y":198,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Error","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"db8a3359-6e24-4a8f-8a54-739f0bc2063f","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"&lt;br&gt; Error Code : \"+tw.local.errorCode+\"\";"]}},{"targetRef":"3ec6be20-f12b-4f7f-827d-6dae1a6b13e7","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2205309e-d02a-442d-8566-1dae69ebf739","sourceRef":"db8a3359-6e24-4a8f-8a54-739f0bc2063f"}],"laneSet":[{"id":"2d262839-491c-419b-98a3-9c2a48238881","lane":[{"flowNodeRef":["e78c96e0-2f12-4787-9b42-dedf79a5f214","3ec6be20-f12b-4f7f-827d-6dae1a6b13e7","a2f65502-fcae-4a71-bbe7-8631d9e1748c","290d3524-87d6-4a7e-8ee8-9271da3bef2e","22d0b73c-745c-4fd2-8474-8a25faaf7595","fa8604c2-ca48-42bc-8bde-c5d63ef06e5e","db8a3359-6e24-4a8f-8a54-739f0bc2063f","b1d8b957-f9ba-4305-8717-c2de589db90c","0cf6ab82-98a5-4564-8c33-c430eb8393fe"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"fed9bf7f-a954-4a51-8e91-153a8a2cf0b7","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get Advices Code","declaredType":"process","id":"1.46038073-457c-489f-9972-9349737b8a6e","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.d028f816-1956-469b-93d2-1314194accbd"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.5aa90542-d8c5-48e3-8501-9fbc7b31c01d"}],"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"Final-ICAP\""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.d7808873-58cf-4dce-bd14-0546c254ef34"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d7808873-58cf-4dce-bd14-0546c254ef34</processParameterId>
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"Final-ICAP"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>58d32e15-60dc-4fde-8ff8-c12fcc9f6b53</guid>
            <versionId>44e0a302-0134-4803-b091-da2482bafec4</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d028f816-1956-469b-93d2-1314194accbd</processParameterId>
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>39eec7d0-2ce3-4006-bbc4-8afdfa156e1b</guid>
            <versionId>2bf38db5-4b12-47c9-9669-965566a3f09a</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5aa90542-d8c5-48e3-8501-9fbc7b31c01d</processParameterId>
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7c953c75-1154-4763-88d8-f896822efeb3</guid>
            <versionId>d8647d6a-0190-4f67-8e59-31d3293671f2</versionId>
        </processParameter>
        <processVariable name="advicesList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0f5022ae-a14b-4ecf-b00c-8c0631fcc176</processVariableId>
            <description isNull="true" />
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.a2b0a889-ce35-486c-880d-83b1cba185d6</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.listOf.toolkit.NBEINTT.MWAdvicesArrayList();
autoObject[0] = new tw.object.toolkit.NBEINTT.MWAdvicesArrayList();
autoObject[0].productCode = "";
autoObject[0].eventCode = "";
autoObject[0].messageType = "";
autoObject[0].fftCode = "";
autoObject[0].type = "";
autoObject</defaultValue>
            <guid>40784e66-4c28-4864-b729-e93def787425</guid>
            <versionId>076ca1a8-28d8-4aa7-9c21-d5daec618d6d</versionId>
        </processVariable>
        <processVariable name="eventCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.97f4b076-e210-4d6b-85e0-cf8101c950c9</processVariableId>
            <description isNull="true" />
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>""</defaultValue>
            <guid>bf2a1bc0-ec23-4be9-b4b2-3528811db962</guid>
            <versionId>6ae7548a-8470-47ec-83e3-33a27ce244d0</versionId>
        </processVariable>
        <processVariable name="productCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7443c448-e9c8-4714-85da-9a909204475c</processVariableId>
            <description isNull="true" />
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>68a13507-20f2-4183-af1b-e61ec5297016</guid>
            <versionId>cc5ab498-a903-4e9e-84b9-0174880bc661</versionId>
        </processVariable>
        <processVariable name="instanceID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a8a72793-c3d3-4dae-87e7-d380b453e4a9</processVariableId>
            <description isNull="true" />
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9891bd3a-d8f8-4c43-b75c-6d6c8b2807b9</guid>
            <versionId>863f195b-7556-4cff-ada5-2297a802d19d</versionId>
        </processVariable>
        <processVariable name="prefix">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f3f9fa20-4230-4b35-b054-768a2ad08d0c</processVariableId>
            <description isNull="true" />
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>90974099-179b-4cfe-9aac-1c035c3057f3</guid>
            <versionId>1a5c2170-751f-4d34-9f3b-a69261a51b06</versionId>
        </processVariable>
        <processVariable name="processName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b60ec26b-c49a-460e-a9bc-213352aff6ff</processVariableId>
            <description isNull="true" />
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6075a88f-6d72-44de-b0e1-ea682fd93530</guid>
            <versionId>22b3b453-9daa-4df3-85a4-75df9b555174</versionId>
        </processVariable>
        <processVariable name="requestAppID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.169063f9-0c61-4947-ac16-3c4ddeeb5b61</processVariableId>
            <description isNull="true" />
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>eb9f73a0-482a-4b17-b48c-5f12cb6c332d</guid>
            <versionId>a8d544e6-ee6e-437e-9e29-cb578ed95881</versionId>
        </processVariable>
        <processVariable name="snapshot">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.24e25f88-38f6-4219-9a50-9688fc2368bc</processVariableId>
            <description isNull="true" />
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b5c6aa16-149c-46b6-91c6-0c9f665cb5e1</guid>
            <versionId>5c5843d0-8eaa-44f3-8cc0-35c4418be3f4</versionId>
        </processVariable>
        <processVariable name="userID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e1ee8495-e2d2-41bc-95a5-2a07ffd608ac</processVariableId>
            <description isNull="true" />
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bc07c031-42b4-4e6e-9f7f-366e828f645f</guid>
            <versionId>1b62fcd0-095c-47cd-acf6-7cf052180716</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5cddcb6c-13ec-4263-842e-75d69dcf6054</processVariableId>
            <description isNull="true" />
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>718a1aaa-7f7c-4501-834c-22ba3eede8a4</guid>
            <versionId>97e7a131-00e5-463f-80a4-ee66f0dc8d33</versionId>
        </processVariable>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9a596747-cebe-4592-8736-560728f83b62</processVariableId>
            <description isNull="true" />
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4341b5d5-a95c-46c9-b942-c0c520dcf34c</guid>
            <versionId>90ba10d8-5821-43c5-b80e-da7929dcd04c</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.dd410d45-333b-458d-851c-bfb646a22e81</processVariableId>
            <description isNull="true" />
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <namespace>2</namespace>
            <seq>12</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0fe10bac-eceb-4e59-8a52-85ab3cbcc7f7</guid>
            <versionId>59ff7c64-b376-404a-8647-4c36ad08dc28</versionId>
        </processVariable>
        <processVariable name="Seperated">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b327460f-3d09-46f2-8cf7-804548fd1db2</processVariableId>
            <description isNull="true" />
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <namespace>2</namespace>
            <seq>13</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>66523786-bb17-4144-9067-9ac12b910f84</guid>
            <versionId>771772fa-04d3-4d93-9404-d8f793ad1cc3</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.80646637-e9ca-4b35-81b8-7d90121422b4</processVariableId>
            <description isNull="true" />
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <namespace>2</namespace>
            <seq>14</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>328fd135-5e01-4a7e-92b0-938dd7b224fe</guid>
            <versionId>b13a083b-e165-4847-8238-8adf471bce3c</versionId>
        </processVariable>
        <processVariable name="error2">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.924c2d73-ea7b-4cc7-8067-06f0effc3a0b</processVariableId>
            <description isNull="true" />
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <namespace>2</namespace>
            <seq>15</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a25f7371-3b73-471d-8455-6f0589a9261c</guid>
            <versionId>7b4fe7f4-07b5-4c2a-aef8-0ea76eede071</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.290d3524-87d6-4a7e-8ee8-9271da3bef2e</processItemId>
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <name>Map data</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.6c04123e-1376-4856-b911-418c510878b1</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.db8a3359-6e24-4a8f-8a54-739f0bc2063f</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6128</guid>
            <versionId>012642dc-c2f0-466c-89da-38e5ace89166</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="526" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:e6d52ec61513ed03:232a663a:189f92002e8:-65d7</errorHandlerItem>
                <errorHandlerItemId>2025.db8a3359-6e24-4a8f-8a54-739f0bc2063f</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.6c04123e-1376-4856-b911-418c510878b1</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>try{&#xD;
	tw.local.results = new tw.object.listOf.String();&#xD;
	if (tw.local.isSuccessful) {&#xD;
		tw.local.results = new tw.object.listOf.String();&#xD;
	&#xD;
		for (var i=0; i&lt; tw.local.advicesList.listLength; i++) {&#xD;
		&#xD;
			tw.local.results[i] = tw.local.advicesList[i].fftCode;&#xD;
		}&#xD;
	}&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>d7381903-19d9-4179-819f-708799a2d714</guid>
                <versionId>83c908b2-7422-4adb-af74-9c79a4aa1adb</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.a2f65502-fcae-4a71-bbe7-8631d9e1748c</processItemId>
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <name>Get Advices Code</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.012e0e45-9d20-4471-974b-b73c62240d41</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-612a</guid>
            <versionId>09c463cb-0a33-404c-a19f-d64cd558ed43</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="262" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.012e0e45-9d20-4471-974b-b73c62240d41</subProcessId>
                <attachedProcessRef>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/1.3c3cf284-fa8f-42d5-a037-16bd9da3d099</attachedProcessRef>
                <guid>0c509a8b-1f58-48ed-a1c5-e6b7706009a6</guid>
                <versionId>9e92cd11-de51-4487-8661-7aaa98d781b0</versionId>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7798b7e6-4ac1-48fe-a6ad-1e6b8270f7e5</parameterMappingId>
                    <processParameterId>2055.41a53d37-78d4-4cb0-8e5e-0f8ea909de5f</processParameterId>
                    <parameterMappingParentId>3012.012e0e45-9d20-4471-974b-b73c62240d41</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>6e570e03-5911-43db-9972-cf5bb788af3d</guid>
                    <versionId>0506aa0d-5a31-4adc-be5a-7aabdd02fd7c</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="eventCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.9b4ace13-2174-4131-97b7-df0df7b2718b</parameterMappingId>
                    <processParameterId>2055.51d39b31-97ab-42d1-8e27-bcb070ffa0f2</processParameterId>
                    <parameterMappingParentId>3012.012e0e45-9d20-4471-974b-b73c62240d41</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.eventCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>7172a8c2-8cea-4cc8-a196-258e6c94d706</guid>
                    <versionId>05d8d174-286e-403b-932c-e4238939ee20</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8e9f960d-c227-4c56-a1a4-917206ed4c87</parameterMappingId>
                    <processParameterId>2055.6fa968cb-917f-4651-82a5-be38ec7f213f</processParameterId>
                    <parameterMappingParentId>3012.012e0e45-9d20-4471-974b-b73c62240d41</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>109e3ac3-3c50-477f-b5a0-4275ec35099d</guid>
                    <versionId>1b7ff14d-f983-4123-bb6d-9dc417c1d31b</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="productCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8a05c3dc-7892-4b6b-a120-29f47aa84562</parameterMappingId>
                    <processParameterId>2055.f2a97b03-5de9-4536-8c7f-8f064b712475</processParameterId>
                    <parameterMappingParentId>3012.012e0e45-9d20-4471-974b-b73c62240d41</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.productCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>19a5bf24-08ba-4368-82d1-4465c0ed35ee</guid>
                    <versionId>3f76d6ba-4475-4bfe-9156-6125780a8bff</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a6fa70e4-5b07-4e27-a32f-da4469b01761</parameterMappingId>
                    <processParameterId>2055.7a07822a-1f5f-44da-8fae-0075fb92bf76</processParameterId>
                    <parameterMappingParentId>3012.012e0e45-9d20-4471-974b-b73c62240d41</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>0d654035-5205-4f83-a0a8-c72465c16a92</guid>
                    <versionId>507d55f5-520f-49c8-ac07-621f7ff3fa37</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="advicesList">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.36b4fc5d-8e32-460b-97d0-6a809f48b8ea</parameterMappingId>
                    <processParameterId>2055.ea62e4d3-568a-4e69-891a-318bf659a9b3</processParameterId>
                    <parameterMappingParentId>3012.012e0e45-9d20-4471-974b-b73c62240d41</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.advicesList</value>
                    <classRef>/12.a2b0a889-ce35-486c-880d-83b1cba185d6</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>e8f684a2-c60e-473e-85c7-f7fce3abc00b</guid>
                    <versionId>57640cfb-d880-475f-b185-1f3634ada756</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ac6c562d-9668-4298-8ece-20adf271b183</parameterMappingId>
                    <processParameterId>2055.992bd140-a6f1-437f-8861-f96fedaeb186</processParameterId>
                    <parameterMappingParentId>3012.012e0e45-9d20-4471-974b-b73c62240d41</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>76eecbde-38fe-439d-abe0-c35eb9bb162b</guid>
                    <versionId>6907df9c-8c05-42b3-a7f3-da86ef3bdd14</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e1467f63-164f-4b7a-bb32-121d684bb034</parameterMappingId>
                    <processParameterId>2055.0b74c51b-ca9c-490f-8ee6-e8be47d8f027</processParameterId>
                    <parameterMappingParentId>3012.012e0e45-9d20-4471-974b-b73c62240d41</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>b564740a-f5a0-4899-96ad-e2d870c16ae7</guid>
                    <versionId>69f10468-4c8c-43e5-8caf-5d7c5a8c5a52</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.55118a6c-1332-4a96-84b6-bd0358120903</parameterMappingId>
                    <processParameterId>2055.75a72db9-2ec3-4e27-85d7-595d65e7317d</processParameterId>
                    <parameterMappingParentId>3012.012e0e45-9d20-4471-974b-b73c62240d41</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>5a8a4420-83ff-42de-84d0-dc4b9c4ca251</guid>
                    <versionId>8553388d-9547-4899-b619-01e209c8279d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8dc34146-6c18-46b4-af3d-24df69565ade</parameterMappingId>
                    <processParameterId>2055.e4184c04-e76c-4ea9-8baf-0fb1ab41692f</processParameterId>
                    <parameterMappingParentId>3012.012e0e45-9d20-4471-974b-b73c62240d41</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"INWARD DOCUMENTARY COLLECTION"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>ebf4a4c0-7867-48e4-a2f2-687e8e2b09ee</guid>
                    <versionId>92b10eed-66b3-4811-b6ed-c3dbfba34bc1</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.114b58a8-db8c-4d9e-82e2-0308a78d6adb</parameterMappingId>
                    <processParameterId>2055.56a8ebe2-ba94-49eb-8220-f23c978a35be</processParameterId>
                    <parameterMappingParentId>3012.012e0e45-9d20-4471-974b-b73c62240d41</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>5a247383-a2e8-4866-a98c-1c048463883d</guid>
                    <versionId>c6d07166-85e0-45bf-b9bc-ca8ecdcf9fea</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.0d215de6-8601-4867-a3cb-cefeec8ccf06</parameterMappingId>
                    <processParameterId>2055.9d2f2d0c-5f60-4a4d-8ca0-fb7d44ed42ce</processParameterId>
                    <parameterMappingParentId>3012.012e0e45-9d20-4471-974b-b73c62240d41</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.currentProcessInstanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>cacde3bf-f3d9-4425-8ea5-0d029bcb4922</guid>
                    <versionId>f717acb9-214b-4dbe-a1a6-77e8149365a0</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.22d0b73c-745c-4fd2-8474-8a25faaf7595</processItemId>
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <name>Split Input</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.83f5f372-e67d-420c-9ff9-f3bb9b1e59ac</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.db8a3359-6e24-4a8f-8a54-739f0bc2063f</errorHandlerItemId>
            <guid>guid:e6be8f1c237f7274:1bda9bc2:1894fa205e2:-6f51</guid>
            <versionId>4ce079a9-bfb0-405c-ad2a-04aeef1dcc29</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.f6eb0767-8ccd-464b-a60c-7c86e402e377</processItemPrePostId>
                <processItemId>2025.22d0b73c-745c-4fd2-8474-8a25faaf7595</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>d333ac6e-05f3-427a-a3bf-60099a34e65a</guid>
                <versionId>781c99fe-b062-42fb-92d8-563ac680888f</versionId>
            </processPrePosts>
            <layoutData x="100" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:e6d52ec61513ed03:232a663a:189f92002e8:-65d7</errorHandlerItem>
                <errorHandlerItemId>2025.db8a3359-6e24-4a8f-8a54-739f0bc2063f</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.83f5f372-e67d-420c-9ff9-f3bb9b1e59ac</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>try{&#xD;
	tw.local.Seperated = new tw.object.listOf.String();&#xD;
	tw.local.Seperated = tw.local.data.split("-");&#xD;
	tw.local.productCode = tw.local.Seperated[1];&#xD;
	&#xD;
	if (tw.local.Seperated[0]=="Initial") {&#xD;
		tw.local.eventCode = "BOOK";	&#xD;
	}&#xD;
	else{&#xD;
		tw.local.eventCode = "INIT";&#xD;
	}&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>0c991f4d-7b62-48be-a22b-2a77777f239d</guid>
                <versionId>bb5007e3-b8fb-4100-b4c6-7a98ea137ae6</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.db8a3359-6e24-4a8f-8a54-739f0bc2063f</processItemId>
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <name>Catch Error</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.564fa3b5-3169-480d-bf63-628ccad8f8e8</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:e6d52ec61513ed03:232a663a:189f92002e8:-65d7</guid>
            <versionId>9ea0437f-b1d8-47b8-8c7a-5101d40331d7</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="525" y="198">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.564fa3b5-3169-480d-bf63-628ccad8f8e8</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</script>
                <isRule>false</isRule>
                <guid>d5d92a2e-a1f9-466c-a0bc-2739a7e87eb0</guid>
                <versionId>2a509350-9597-4a4c-b68f-d27759e5a5b8</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3ec6be20-f12b-4f7f-827d-6dae1a6b13e7</processItemId>
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.3cfd44de-7344-447b-9f41-eeef8536ac17</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6129</guid>
            <versionId>dc78c909-e965-4243-9e4c-4c16b73d9a58</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="750" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.3cfd44de-7344-447b-9f41-eeef8536ac17</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>4eae366c-7ac5-42cc-9736-248d343f7ae8</guid>
                <versionId>28a748f5-994f-451f-96d4-cdb05a2aa549</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.fa8604c2-ca48-42bc-8bde-c5d63ef06e5e</processItemId>
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <name>is Successful</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.9fad2e8b-73d5-4e3a-9b4d-da9fd05e73ff</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189e6fc33e5:-f6a</guid>
            <versionId>f13a8d34-55bd-4402-b03e-24073b5221b4</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="375" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.9fad2e8b-73d5-4e3a-9b4d-da9fd05e73ff</switchId>
                <guid>b3386e10-d14e-4fbb-b84c-6d1e0fe81c3d</guid>
                <versionId>c9198ff5-4e7b-4863-ad5d-e2dfd2630a16</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.1d6e3a39-85e4-4599-8622-fcbd3954cded</switchConditionId>
                    <switchId>3013.9fad2e8b-73d5-4e3a-9b4d-da9fd05e73ff</switchId>
                    <seq>1</seq>
                    <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3ddc</endStateId>
                    <condition>tw.local.isSuccessful	  ==	  false</condition>
                    <guid>8a1c5d99-744c-4ff3-b26c-bb8f1f261e9d</guid>
                    <versionId>379fad20-0f30-4ce3-a207-9ae9eb1fde36</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.22d0b73c-745c-4fd2-8474-8a25faaf7595</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get Advices Code" id="1.46038073-457c-489f-9972-9349737b8a6e" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.d7808873-58cf-4dce-bd14-0546c254ef34">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"Final-ICAP"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.d028f816-1956-469b-93d2-1314194accbd" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.5aa90542-d8c5-48e3-8501-9fbc7b31c01d" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="2d262839-491c-419b-98a3-9c2a48238881">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="fed9bf7f-a954-4a51-8e91-153a8a2cf0b7" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>e78c96e0-2f12-4787-9b42-dedf79a5f214</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3ec6be20-f12b-4f7f-827d-6dae1a6b13e7</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a2f65502-fcae-4a71-bbe7-8631d9e1748c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>290d3524-87d6-4a7e-8ee8-9271da3bef2e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>22d0b73c-745c-4fd2-8474-8a25faaf7595</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>fa8604c2-ca48-42bc-8bde-c5d63ef06e5e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>db8a3359-6e24-4a8f-8a54-739f0bc2063f</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b1d8b957-f9ba-4305-8717-c2de589db90c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>0cf6ab82-98a5-4564-8c33-c430eb8393fe</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="e78c96e0-2f12-4787-9b42-dedf79a5f214">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.19c3f603-d22d-4e37-9934-309d08de89c6</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="3ec6be20-f12b-4f7f-827d-6dae1a6b13e7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="750" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6129</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>fb4f652a-3826-4ab8-a964-4df27d199cad</ns16:incoming>
                        
                        
                        <ns16:incoming>2205309e-d02a-442d-8566-1dae69ebf739</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="e78c96e0-2f12-4787-9b42-dedf79a5f214" targetRef="22d0b73c-745c-4fd2-8474-8a25faaf7595" name="To Split Input" id="2027.19c3f603-d22d-4e37-9934-309d08de89c6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.3c3cf284-fa8f-42d5-a037-16bd9da3d099" name="Get Advices Code" id="a2f65502-fcae-4a71-bbe7-8631d9e1748c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="262" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>1d4e8747-560f-4763-8e03-dceae38eab17</ns16:incoming>
                        
                        
                        <ns16:outgoing>a01182d1-3636-48a3-91a7-aabaf72f56de</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.0b74c51b-ca9c-490f-8ee6-e8be47d8f027</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.7a07822a-1f5f-44da-8fae-0075fb92bf76</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9d2f2d0c-5f60-4a4d-8ca0-fb7d44ed42ce</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.currentProcessInstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.e4184c04-e76c-4ea9-8baf-0fb1ab41692f</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"INWARD DOCUMENTARY COLLECTION"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.992bd140-a6f1-437f-8861-f96fedaeb186</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.41a53d37-78d4-4cb0-8e5e-0f8ea909de5f</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.f2a97b03-5de9-4536-8c7f-8f064b712475</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.productCode</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.51d39b31-97ab-42d1-8e27-bcb070ffa0f2</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.eventCode</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.ea62e4d3-568a-4e69-891a-318bf659a9b3</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.a2b0a889-ce35-486c-880d-83b1cba185d6">tw.local.advicesList</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.75a72db9-2ec3-4e27-85d7-595d65e7317d</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.6fa968cb-917f-4651-82a5-be38ec7f213f</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.56a8ebe2-ba94-49eb-8220-f23c978a35be</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="a2f65502-fcae-4a71-bbe7-8631d9e1748c" targetRef="fa8604c2-ca48-42bc-8bde-c5d63ef06e5e" name="To is Successful" id="a01182d1-3636-48a3-91a7-aabaf72f56de">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:502f0309b7e9a7eb:-5dac1a55:188bbb8ea24:-5c3e</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Map data" id="290d3524-87d6-4a7e-8ee8-9271da3bef2e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="526" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>955caae0-1911-4565-8d2a-5f82fbb6a35f</ns16:incoming>
                        
                        
                        <ns16:outgoing>fb4f652a-3826-4ab8-a964-4df27d199cad</ns16:outgoing>
                        
                        
                        <ns16:script>try{&#xD;
	tw.local.results = new tw.object.listOf.String();&#xD;
	if (tw.local.isSuccessful) {&#xD;
		tw.local.results = new tw.object.listOf.String();&#xD;
	&#xD;
		for (var i=0; i&lt; tw.local.advicesList.listLength; i++) {&#xD;
		&#xD;
			tw.local.results[i] = tw.local.advicesList[i].fftCode;&#xD;
		}&#xD;
	}&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="290d3524-87d6-4a7e-8ee8-9271da3bef2e" targetRef="3ec6be20-f12b-4f7f-827d-6dae1a6b13e7" name="To End" id="fb4f652a-3826-4ab8-a964-4df27d199cad">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.a2b0a889-ce35-486c-880d-83b1cba185d6" isCollection="true" name="advicesList" id="2056.0f5022ae-a14b-4ecf-b00c-8c0631fcc176">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">var autoObject = new tw.object.listOf.toolkit.NBEINTT.MWAdvicesArrayList();
autoObject[0] = new tw.object.toolkit.NBEINTT.MWAdvicesArrayList();
autoObject[0].productCode = "";
autoObject[0].eventCode = "";
autoObject[0].messageType = "";
autoObject[0].fftCode = "";
autoObject[0].type = "";
autoObject</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="eventCode" id="2056.97f4b076-e210-4d6b-85e0-cf8101c950c9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="productCode" id="2056.7443c448-e9c8-4714-85da-9a909204475c" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="instanceID" id="2056.a8a72793-c3d3-4dae-87e7-d380b453e4a9" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="prefix" id="2056.f3f9fa20-4230-4b35-b054-768a2ad08d0c" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="processName" id="2056.b60ec26b-c49a-460e-a9bc-213352aff6ff" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="requestAppID" id="2056.169063f9-0c61-4947-ac16-3c4ddeeb5b61" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="snapshot" id="2056.24e25f88-38f6-4219-9a50-9688fc2368bc" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="userID" id="2056.e1ee8495-e2d2-41bc-95a5-2a07ffd608ac" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.5cddcb6c-13ec-4263-842e-75d69dcf6054" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.9a596747-cebe-4592-8736-560728f83b62" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.dd410d45-333b-458d-851c-bfb646a22e81" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Split Input" id="22d0b73c-745c-4fd2-8474-8a25faaf7595">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="100" y="57" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.19c3f603-d22d-4e37-9934-309d08de89c6</ns16:incoming>
                        
                        
                        <ns16:outgoing>1d4e8747-560f-4763-8e03-dceae38eab17</ns16:outgoing>
                        
                        
                        <ns16:script>try{&#xD;
	tw.local.Seperated = new tw.object.listOf.String();&#xD;
	tw.local.Seperated = tw.local.data.split("-");&#xD;
	tw.local.productCode = tw.local.Seperated[1];&#xD;
	&#xD;
	if (tw.local.Seperated[0]=="Initial") {&#xD;
		tw.local.eventCode = "BOOK";	&#xD;
	}&#xD;
	else{&#xD;
		tw.local.eventCode = "INIT";&#xD;
	}&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="22d0b73c-745c-4fd2-8474-8a25faaf7595" targetRef="a2f65502-fcae-4a71-bbe7-8631d9e1748c" name="To Get Advices Code" id="1d4e8747-560f-4763-8e03-dceae38eab17">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="Seperated" id="2056.b327460f-3d09-46f2-8cf7-804548fd1db2" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.80646637-e9ca-4b35-81b8-7d90121422b4" />
                    
                    
                    <ns16:exclusiveGateway default="955caae0-1911-4565-8d2a-5f82fbb6a35f" name="is Successful" id="fa8604c2-ca48-42bc-8bde-c5d63ef06e5e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="375" y="76" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>a01182d1-3636-48a3-91a7-aabaf72f56de</ns16:incoming>
                        
                        
                        <ns16:outgoing>955caae0-1911-4565-8d2a-5f82fbb6a35f</ns16:outgoing>
                        
                        
                        <ns16:outgoing>b1410ae6-e01f-4acc-8d9d-60194af0a5ee</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="fa8604c2-ca48-42bc-8bde-c5d63ef06e5e" targetRef="290d3524-87d6-4a7e-8ee8-9271da3bef2e" name="To Map data" id="955caae0-1911-4565-8d2a-5f82fbb6a35f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" name="error2" id="2056.924c2d73-ea7b-4cc7-8067-06f0effc3a0b" />
                    
                    
                    <ns16:sequenceFlow sourceRef="fa8604c2-ca48-42bc-8bde-c5d63ef06e5e" targetRef="db8a3359-6e24-4a8f-8a54-739f0bc2063f" name="To End Event" id="b1410ae6-e01f-4acc-8d9d-60194af0a5ee">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isSuccessful	  ==	  false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="22d0b73c-745c-4fd2-8474-8a25faaf7595" parallelMultiple="false" name="Error" id="b1d8b957-f9ba-4305-8717-c2de589db90c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="135" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>7c8a389e-4dc1-4b42-821e-38c690a61f1e</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="5ba87848-06b6-460b-88f9-c5ebdd6924e6" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="5c2b3ee2-9753-4efc-8558-0cbcf8ae8ff5" eventImplId="0ca012c2-dbac-425f-80b3-360fca9ea583">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="290d3524-87d6-4a7e-8ee8-9271da3bef2e" parallelMultiple="false" name="Error1" id="0cf6ab82-98a5-4564-8c33-c430eb8393fe">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="561" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>0fc59d9a-33b7-4b59-886d-976818132547</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="c4902546-3c91-4c27-8782-091fac976bcf" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="af75e5b0-2c61-4513-8404-538e86ab6ddd" eventImplId="dd75c63f-5f51-4609-8bb4-80c82650d40c">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="b1d8b957-f9ba-4305-8717-c2de589db90c" targetRef="db8a3359-6e24-4a8f-8a54-739f0bc2063f" name="To Catch Error" id="7c8a389e-4dc1-4b42-821e-38c690a61f1e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="0cf6ab82-98a5-4564-8c33-c430eb8393fe" targetRef="db8a3359-6e24-4a8f-8a54-739f0bc2063f" name="To Catch Error" id="0fc59d9a-33b7-4b59-886d-976818132547">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Catch Error" id="db8a3359-6e24-4a8f-8a54-739f0bc2063f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="525" y="198" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>b1410ae6-e01f-4acc-8d9d-60194af0a5ee</ns16:incoming>
                        
                        
                        <ns16:incoming>7c8a389e-4dc1-4b42-821e-38c690a61f1e</ns16:incoming>
                        
                        
                        <ns16:incoming>0fc59d9a-33b7-4b59-886d-976818132547</ns16:incoming>
                        
                        
                        <ns16:outgoing>2205309e-d02a-442d-8566-1dae69ebf739</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"&lt;br&gt; Error Code : "+tw.local.errorCode+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="db8a3359-6e24-4a8f-8a54-739f0bc2063f" targetRef="3ec6be20-f12b-4f7f-827d-6dae1a6b13e7" name="To End" id="2205309e-d02a-442d-8566-1dae69ebf739">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To is Successful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.a01182d1-3636-48a3-91a7-aabaf72f56de</processLinkId>
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.a2f65502-fcae-4a71-bbe7-8631d9e1748c</fromProcessItemId>
            <endStateId>guid:502f0309b7e9a7eb:-5dac1a55:188bbb8ea24:-5c3e</endStateId>
            <toProcessItemId>2025.fa8604c2-ca48-42bc-8bde-c5d63ef06e5e</toProcessItemId>
            <guid>1817c25c-4148-4c54-93a6-33faeb7f5402</guid>
            <versionId>3f4078a4-8514-4c26-9161-c1f3ee21759d</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.a2f65502-fcae-4a71-bbe7-8631d9e1748c</fromProcessItemId>
            <toProcessItemId>2025.fa8604c2-ca48-42bc-8bde-c5d63ef06e5e</toProcessItemId>
        </link>
        <link name="To Get Advices Code">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.1d4e8747-560f-4763-8e03-dceae38eab17</processLinkId>
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.22d0b73c-745c-4fd2-8474-8a25faaf7595</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.a2f65502-fcae-4a71-bbe7-8631d9e1748c</toProcessItemId>
            <guid>b55c54c2-9d34-41e1-8458-84e029ed1d29</guid>
            <versionId>5f7dc0d4-9698-4202-b43f-5fc1201ea825</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.22d0b73c-745c-4fd2-8474-8a25faaf7595</fromProcessItemId>
            <toProcessItemId>2025.a2f65502-fcae-4a71-bbe7-8631d9e1748c</toProcessItemId>
        </link>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.b1410ae6-e01f-4acc-8d9d-60194af0a5ee</processLinkId>
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.fa8604c2-ca48-42bc-8bde-c5d63ef06e5e</fromProcessItemId>
            <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3ddc</endStateId>
            <toProcessItemId>2025.db8a3359-6e24-4a8f-8a54-739f0bc2063f</toProcessItemId>
            <guid>25826ad4-a792-43b9-b994-de330caa0a42</guid>
            <versionId>653c65a1-e374-4b78-b0a2-1773ad4deb48</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.fa8604c2-ca48-42bc-8bde-c5d63ef06e5e</fromProcessItemId>
            <toProcessItemId>2025.db8a3359-6e24-4a8f-8a54-739f0bc2063f</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.2205309e-d02a-442d-8566-1dae69ebf739</processLinkId>
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.db8a3359-6e24-4a8f-8a54-739f0bc2063f</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.3ec6be20-f12b-4f7f-827d-6dae1a6b13e7</toProcessItemId>
            <guid>fa446553-6feb-4013-8139-abf2d6c86274</guid>
            <versionId>818d3e09-1e84-4b55-907d-3f64e7706d7f</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.db8a3359-6e24-4a8f-8a54-739f0bc2063f</fromProcessItemId>
            <toProcessItemId>2025.3ec6be20-f12b-4f7f-827d-6dae1a6b13e7</toProcessItemId>
        </link>
        <link name="To Map data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.955caae0-1911-4565-8d2a-5f82fbb6a35f</processLinkId>
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.fa8604c2-ca48-42bc-8bde-c5d63ef06e5e</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.290d3524-87d6-4a7e-8ee8-9271da3bef2e</toProcessItemId>
            <guid>eec1626d-cf55-4004-ab78-807c860f3221</guid>
            <versionId>b687e8f8-2085-49a7-a913-36a806fc108c</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.fa8604c2-ca48-42bc-8bde-c5d63ef06e5e</fromProcessItemId>
            <toProcessItemId>2025.290d3524-87d6-4a7e-8ee8-9271da3bef2e</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.fb4f652a-3826-4ab8-a964-4df27d199cad</processLinkId>
            <processId>1.46038073-457c-489f-9972-9349737b8a6e</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.290d3524-87d6-4a7e-8ee8-9271da3bef2e</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.3ec6be20-f12b-4f7f-827d-6dae1a6b13e7</toProcessItemId>
            <guid>cb3bbd8a-f691-475c-a46a-efcd7bb78728</guid>
            <versionId>d6acce49-3992-4fd8-864f-8954cf096566</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.290d3524-87d6-4a7e-8ee8-9271da3bef2e</fromProcessItemId>
            <toProcessItemId>2025.3ec6be20-f12b-4f7f-827d-6dae1a6b13e7</toProcessItemId>
        </link>
    </process>
</teamworks>

