<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.f2c8f018-67e0-416a-acb3-8af30664cba5" name="Waiting CBE Approval">
        <lastModified>1692698515645</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.017f41e3-4a25-4dfa-b598-af7ed48d49b3</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>e5f25840-57fa-4400-9919-5612577a1170</guid>
        <versionId>634e32ae-4dfc-4123-bd61-53bd0ef048b8</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.32359e17-5e44-40d3-b3ec-8aa9f7537828"],"isInterrupting":true,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":50,"y":188,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"3c61f056-712b-4290-b0ce-2c1b69f65785"},{"incoming":["2027.b6e13286-1293-4b41-9b33-9dc4bdcaf36d"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":848,"y":188,"declaredType":"TNodeVisualInfo","height":44}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"End","declaredType":"endEvent","id":"9404282f-3ec6-490c-b763-1d5fcf5223bb"},{"targetRef":"2025.674f24d4-c1fe-42d6-9959-cdf0d266ad30","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Waiting CBE Approval","declaredType":"sequenceFlow","id":"2027.32359e17-5e44-40d3-b3ec-8aa9f7537828","sourceRef":"3c61f056-712b-4290-b0ce-2c1b69f65785"},{"startQuantity":1,"outgoing":["2027.0d099e1d-0221-4791-adf5-b3b5603ef3b7"],"incoming":["2027.1645c156-332c-4f50-91bd-58feff9d3d96"],"default":"2027.0d099e1d-0221-4791-adf5-b3b5603ef3b7","extensionElements":{"nodeVisualInfo":[{"width":95,"x":543,"y":165,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Status","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.5b35a7b3-6de8-4817-ac2f-9a61a6ef84b3","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inApproval;\r\ntw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingTreasuryMakerReview;\r\ntw.local.idcRequest.stepLog.action = tw.local.selectedAction;"]}},{"targetRef":"2025.e16159fa-302a-4b8a-b108-2919f0ef72b2","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"OK To End","declaredType":"sequenceFlow","id":"2027.0d099e1d-0221-4791-adf5-b3b5603ef3b7","sourceRef":"2025.5b35a7b3-6de8-4817-ac2f-9a61a6ef84b3"},{"outgoing":["2027.c231611b-3570-446c-885b-89f387631a12","2027.88b8952f-e31b-4936-842e-19b00dc85c73"],"incoming":["2027.d00333e9-33a0-4889-bb8d-c077e065ead7","2027.8dd78d4e-4141-4264-930c-dc82bc6922a2","2027.3988f0d8-63c3-4322-b28e-93b7e3987906","2027.1ead6deb-ca24-43e4-8e58-67d5629fdb2d"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":290,"y":165,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"layoutItemId":"Error_Message1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a12c544f-fe1f-4f23-8a19-d52a266c4e24","optionName":"@label","value":"Error Message"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d316447e-0949-4bf8-8e6c-f768c0e6d94f","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"056c7557-59ec-4d38-8d5f-de239c7f3d11","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0f4da1b9-d0f3-4fdf-8343-9399823a4c83","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d7359ac0-e371-4eb3-80c0-83699ba63ff0","optionName":"@visibility","value":"tw.local.errorVIS"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"36570bf1-abf6-4d62-8ea0-5226bba4fb3d","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.46690ca1-5521-4e5c-bc9e-ecb22d75ca97","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"a4e2a666-7990-44ad-8802-492acecb77fe","version":"8550"},{"contentBoxContrib":[{"contributions":[{"contentBoxContrib":[{"contributions":[{"layoutItemId":"Customer_Information1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"101e9834-c041-4582-814e-8d22d4ebbcab","optionName":"@label","value":"Customer Information"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2e42db77-2de0-45ae-82a0-3242b2e9057b","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4a058bb4-ad15-48ab-88e0-b63f40efda2a","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e53cf7d7-a310-458e-8ef3-3891809f1a5a","optionName":"instanceview","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"425857a2-618a-43db-8731-b4698d9867f6","optionName":"alertMessage","value":"tw.local.alertMessage"}],"viewUUID":"64.656cc232-8247-43c3-9481-3cc7a9aec2e6","binding":"tw.local.idcRequest.customerInformation","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"850918f3-ae1e-4365-8b42-7e601153ed48","version":"8550"},{"layoutItemId":"Basic_Details1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4414cf20-349d-4ed9-8d8d-4f7a3188bd91","optionName":"@label","value":"Basic Details"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c5805473-b50a-4482-86da-d98cbd50537d","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"962d7d3b-762c-4eba-8fb1-e83fc8f5cc82","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8eb175ee-1166-435e-8ef1-e6931361e8a1","optionName":"alertMessage","value":"tw.local.errorMSG"}],"viewUUID":"64.3d13e9b9-bcc9-4fe9-ac80-3c287115f705","binding":"tw.local.idcRequest","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"c1dca3b8-f486-43cd-8833-25fa1c324441","version":"8550"},{"layoutItemId":"Financial_Details__Branch1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"26502982-8e97-4095-802d-6ef1a80c09ed","optionName":"@label","value":"Financial Details  Branch"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1402c8d8-c9e3-40eb-8e45-32876547b265","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f170aa14-8d89-4e33-8cc9-f6a08ac76857","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a9bcdc87-e629-413d-8f31-ffe16c5a7c6a","optionName":"CIF","value":"tw.local.idcRequest.customerInformation.CIFNumber"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1cd914ab-5f19-4a0e-8e44-05dbbf20ec9c","optionName":"accountsList","value":"tw.local.accountsList[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"18dffc5b-7182-45f8-8897-4b83086e9da2","optionName":"requestType","value":"tw.local.idcRequest.IDCRequestType.englishdescription"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1dd4b645-89d6-4eef-8fc0-ffb710b1c9ad","optionName":"haveAmountAdvanced","value":"DEFAULT"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b2029fda-43ad-49c2-8a82-2cb6b186cab1","optionName":"isChecker","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"47bd5566-5e41-4ed1-8159-f39170340bfe","optionName":"tmpUsedAdvancePayment","value":"tw.local.tmpUsedAdvancePayment"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"92193b68-a5f8-4866-869c-032236bda66b","optionName":"currencyVis","value":"tw.local.currencyVis"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d88e3357-7df9-45e0-82d2-ca4f871524f0","optionName":"requestID","value":"tw.local.idcRequest.appInfo.instanceID"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"bf603f9e-f50f-4e42-8fcd-da0ba7777e3b","optionName":"alertMessage","value":"tw.local.errorMSG"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"79de72d9-2ef3-4d13-8d18-4134cadba452","optionName":"errorVis","value":"tw.local.errorVIS"}],"viewUUID":"64.74d3cb97-ad59-4249-847b-a21122e44b22","binding":"tw.local.idcRequest.financialDetails","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"21822f84-952b-4edc-852a-6163e26e0ee4","version":"8550"},{"layoutItemId":"Attachment1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0d54045e-f098-4464-844a-4933dc007e91","optionName":"@label","value":"Attachment"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a83a0620-8b05-47fe-8be7-************","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"640b825b-510c-422e-8ea5-697760366b7f","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"84880d15-8a1c-4912-8853-8ba61f06a11f","optionName":"canDelete","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cabce9f3-22d7-495d-888f-51a12c61fc6c","optionName":"canCreate","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"74b258c0-dd7d-430b-8c7c-010b38c0bd93","optionName":"canUpdate","value":"false"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0b1ee9ed-4b2a-4522-830d-9c666d7b60aa","optionName":"ECMproperties","value":"tw.local.ECMproperties"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1414247d-cef8-499c-816e-17d763b9bd38","optionName":"visiable","value":"false"}],"viewUUID":"64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7","binding":"tw.local.attachment[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"82c50fbb-d5f6-4bb1-8dda-6575cfd71fef","version":"8550"},{"layoutItemId":"App_History_View_21","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"dde162a0-7075-4fde-8ed7-a70616672091","optionName":"@label","value":"History"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5167b6d3-00c4-4c8f-8734-622d568e6a22","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"da87eb96-32e5-477d-8577-3a1ad8cad61e","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"af233f4a-44de-4362-83d7-e790132f32e8","optionName":"historyVisFlag","value":"None"}],"viewUUID":"64.6ac8a533-b05a-45a1-9e46-1c4542e6b8be","binding":"tw.local.treasuryComments[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"28cfe003-1095-40fe-8f0a-59ee42fd0d3a","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"29da4323-1dd0-4ee9-89d1-a45bf9f8a1fb"}],"layoutItemId":"Tab_Section1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d51d0add-4559-4a2c-88bd-6db19b90032c","optionName":"@label","value":"Tab section"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"49ea0b35-1b3d-4392-8c14-539d8e93d613","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2ad1ba70-b1c2-482b-8978-cb84312505b3","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"49c0138a-0f87-43b5-8e2a-d8d0c08d03c2","optionName":"sizeStyle","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"X\"}]}"}],"viewUUID":"64.c05b439f-a4bd-48b0-8644-fa3b59052217","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"bdb77abf-ff28-4cd5-8de5-84fe862e2c89","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"84e4d9d9-c684-46e6-887e-4cf4fb9be60a"}],"layoutItemId":"DC_Templete1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"da058063-6812-40af-8dde-e8ff368bb926","optionName":"@label","value":"DC Templete"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d3ccf651-ac46-4292-8a51-fd6b5e1eca70","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ae3b1e74-4a28-4574-8674-714470daeef9","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"40117d7b-39e1-4d10-8d02-1949964268e5","optionName":"stepLog","value":"tw.local.idcRequest.stepLog"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e6cf5fb3-5f2a-449d-81e3-13f0a5bd9976","optionName":"invalidTabs","value":"tw.local.invalidTabs[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b7aaac28-65bf-41de-896e-1e96683e4d29","optionName":"action","value":"tw.local.action[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2c40fa32-49c9-4e67-8486-6c2298b4874b","optionName":"selectedAction","value":"tw.local.selectedAction"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a2acf58f-c703-4e1e-8aca-b6bed1372659","optionName":"buttonName","value":"Submit"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"449b555e-1143-4309-8504-63a14bcd39bf","optionName":"hasApprovals","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fcd5cc64-4023-4fee-8de2-42c2614e7bf9","optionName":"hasReturnReason","value":"false"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"83b7d27e-**************-039159149824","optionName":"approvals","value":"tw.local.idcRequest.approvals"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"38225f16-05b3-451d-88fa-326a07646e14","optionName":"approvalsReadOnly","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5a34f750-5f0d-4464-8515-a1402fc287ab","optionName":"isCAD","value":"false"}],"viewUUID":"64.f9e6899d-e7d7-4296-ba71-268fcd57e296","binding":"tw.local.idcRequest.appInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"771c12db-0a3f-42f5-8e24-2ce6e5f451c3","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"Waiting CBE Approval","isForCompensation":false,"completionQuantity":1,"id":"2025.2acbb661-0ba9-4950-a40d-5275b121f9a3"},{"targetRef":"2025.9945acca-fa22-4aff-8de7-9627ae3cd563","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"05a11345-3198-4728-8c5f-89c5c3248c34","coachEventPath":"DC_Templete1\/submit"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.c231611b-3570-446c-885b-89f387631a12","sourceRef":"2025.2acbb661-0ba9-4950-a40d-5275b121f9a3"},{"outgoing":["2027.d00333e9-33a0-4889-bb8d-c077e065ead7"],"incoming":["2027.88b8952f-e31b-4936-842e-19b00dc85c73"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition"}],"extensionElements":{"default":["2027.d00333e9-33a0-4889-bb8d-c077e065ead7"],"nodeVisualInfo":[{"width":24,"x":301,"y":44,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page","declaredType":"intermediateThrowEvent","id":"2025.81a3add5-a097-4468-ae4c-5ea8b28906a1"},{"targetRef":"2025.81a3add5-a097-4468-ae4c-5ea8b28906a1","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"456b631e-9631-4f92-bbd6-29e1713c276c","coachEventPath":"DC_Templete1\/saveState"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topLeft","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Stay on page","declaredType":"sequenceFlow","id":"2027.88b8952f-e31b-4936-842e-19b00dc85c73","sourceRef":"2025.2acbb661-0ba9-4950-a40d-5275b121f9a3"},{"targetRef":"2025.2acbb661-0ba9-4950-a40d-5275b121f9a3","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Waiting CBE Approval","declaredType":"sequenceFlow","id":"2027.d00333e9-33a0-4889-bb8d-c077e065ead7","sourceRef":"2025.81a3add5-a097-4468-ae4c-5ea8b28906a1"},{"outgoing":["2027.b6e13286-1293-4b41-9b33-9dc4bdcaf36d"],"incoming":["2027.0d099e1d-0221-4791-adf5-b3b5603ef3b7"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":695,"y":165,"declaredType":"TNodeVisualInfo","height":70}]},"declaredType":"callActivity","startQuantity":1,"default":"2027.b6e13286-1293-4b41-9b33-9dc4bdcaf36d","name":"Update History","dataInputAssociation":[{"targetRef":"2055.648598d0-2039-40d4-b60b-3753a273a378","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.idcRequest.stepLog"]}}]},{"targetRef":"2055.5fca703f-c44e-4efc-b6ac-6b71dd05abf0","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.treasuryComments"]}}]},{"targetRef":"2055.df0b4fa5-42fa-431d-8822-9eeb4e99abc8","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Treasury Maker\""]}}]},{"targetRef":"2055.322bdb97-0698-43d7-8172-71cbc933103d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","declaredType":"TFormalExpression","content":["tw.local.idcRequest"]}}]},{"targetRef":"2055.ebf12d20-c385-4ebf-81b8-8053911f8aa6","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.treasuryComments"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.e16159fa-302a-4b8a-b108-2919f0ef72b2","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.idcRequest.stepLog"]}}],"sourceRef":["2055.65675974-9215-43be-8dce-3b75511a591d"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.treasuryComments"]}}],"sourceRef":["2055.8fcdef92-a110-407f-aff8-5693f497f953"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","declaredType":"TFormalExpression","content":["tw.local.treasuryComments"]}}],"sourceRef":["2055.236df123-3f3f-4bdd-8cba-b6a13fa9de98"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.60a8424b-59f2-4328-8d4f-c388b30e202f"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression"}}],"sourceRef":["2055.fce152d9-1c42-43bc-8bff-44f6b45aba67"]}],"calledElement":"1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59"},{"targetRef":"9404282f-3ec6-490c-b763-1d5fcf5223bb","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.b6e13286-1293-4b41-9b33-9dc4bdcaf36d","sourceRef":"2025.e16159fa-302a-4b8a-b108-2919f0ef72b2"},{"startQuantity":1,"outgoing":["2027.8dd78d4e-4141-4264-930c-dc82bc6922a2"],"incoming":["2027.a1aca0da-291a-4fee-8741-24d94dfca080"],"default":"2027.8dd78d4e-4141-4264-930c-dc82bc6922a2","extensionElements":{"nodeVisualInfo":[{"width":95,"x":138,"y":165,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Step Name","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.c74cc14f-e2d1-4128-adb7-948a7c8e28ad","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.idcRequest.appInfo.stepName = tw.system.processInstance.task.subject;\r\ntw.local.idcRequest.appInfo.appID = tw.system.processInstance.id;\r\n\r\n"]}},{"targetRef":"2025.2acbb661-0ba9-4950-a40d-5275b121f9a3","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Waiting CBE Approval","declaredType":"sequenceFlow","id":"2027.8dd78d4e-4141-4264-930c-dc82bc6922a2","sourceRef":"2025.c74cc14f-e2d1-4128-adb7-948a7c8e28ad"},{"startQuantity":1,"outgoing":["2027.a1aca0da-291a-4fee-8741-24d94dfca080"],"incoming":["2027.32359e17-5e44-40d3-b3ec-8aa9f7537828"],"default":"2027.a1aca0da-291a-4fee-8741-24d94dfca080","extensionElements":{"nodeVisualInfo":[{"width":95,"x":138,"y":62,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Initialization Script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.674f24d4-c1fe-42d6-9959-cdf0d266ad30","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.idcRequest.stepLog = {};\r\ntw.local.idcRequest.stepLog.startTime = new Date();\r\n\r\ntw.local.errorVIS = \"NONE\";\r\ntw.local.action = [];\r\ntw.local.action[0]=tw.epv.Action.sendBackToMainQueue;"]}},{"targetRef":"2025.c74cc14f-e2d1-4128-adb7-948a7c8e28ad","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set Step Name","declaredType":"sequenceFlow","id":"2027.a1aca0da-291a-4fee-8741-24d94dfca080","sourceRef":"2025.674f24d4-c1fe-42d6-9959-cdf0d266ad30"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"action","isCollection":true,"declaredType":"dataObject","id":"2056.c9b305bb-a4e3-489c-9040-0fea6b8979ee"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"selectedAction","isCollection":false,"declaredType":"dataObject","id":"2056.528ea898-4984-4b92-b20b-9bc1cbadd9e8"},{"startQuantity":1,"outgoing":["2027.90acfe32-a1ff-44f2-84e6-03a9b90269b3"],"incoming":["2027.c231611b-3570-446c-885b-89f387631a12"],"default":"2027.90acfe32-a1ff-44f2-84e6-03a9b90269b3","extensionElements":{"nodeVisualInfo":[{"color":"#95D087","width":95,"x":290,"y":303,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Validation","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.9945acca-fa22-4aff-8de7-9627ae3cd563","scriptFormat":"text\/x-javascript","script":{"content":["var message = \"\";\r\nvar mandatoryTriggered = false;\r\nvar tempLength = 0 ;\r\n\/\/var invalidTabs = [];\r\n\/*\r\n* =========================================================================================================\r\n*  \r\n* Add a coach validation error \r\n* \t\t\r\n* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\n*\r\n* =========================================================================================================\r\n*\/\r\n\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\n{\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\n\tfromMandatory &amp;&amp; mandatoryTriggered ? \"\" : tw.local.message += \"&lt;li dir='rtl'&gt;\" + validationMessage + \"&lt;\/li&gt;\";\r\n}\r\n\r\n\/*\r\n* ==============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is in the past and the last variable to exclude today\r\n*\t\r\n* EX:\tnotPastDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)\r\n*\r\n* =============================================================================================================================\r\n*\/\r\n\r\nfunction notPastDate(date , fieldName , controlMessage , validationMessage , exclude)\r\n{\r\n\tif (exclude)\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &lt;= new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\t\r\n\t}\r\n\telse\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &lt; new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\r\n\t}\r\n}\r\n\r\n\/*\r\n* ==============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is between two dates\r\n*\t\r\n* EX:\tdateBetween(tw.local.date , date1 , date2 , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!')\r\n*\r\n* =============================================================================================================================\r\n*\/\r\n\r\nfunction dateBetween(field , date1 , date2 , fieldName , controlMessage , validationMessage)\r\n{\r\n\tif(field &lt; date1 &amp;&amp; field &gt; date2)\r\n\t{\r\n\t \treturn true;\r\n\t}\r\n\taddError(fieldName , controlMessage , validationMessage);\r\n\treturn false;\r\n}\r\n\r\n\/*\r\n* ===============================================================================================================================\r\n*\r\n* Add a coach validation error if the date is in the future and the last varaible to exculde today\r\n*\t\r\n* EX:\tnotFutureDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)\r\n*\r\n* ===============================================================================================================================\r\n*\/\r\n\r\nfunction notFutureDate(date , fieldName ,  controlMessage , validationMessage , exculde)\r\n{\r\n\tif (exculde)\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &gt;= new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\t\r\n\t}\r\n\telse\r\n\t{\r\n\t\tif(date != null &amp;&amp; date &gt; new Date())\r\n\t\t{\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t\treturn true;\r\n\t}\r\n}\r\n\r\n\r\n\/*\r\n* =================================================================================================================================\r\n*\r\n* Add a coach validation error if the string length is less than given length\r\n*\t\r\n* EX:\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\r\n*\r\n* =================================================================================================================================\r\n*\/\r\n\r\nfunction minLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the string length is greater than given length\r\n*\t\r\n* EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field != null &amp;&amp; field != undefined &amp;&amp; field.length &gt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the number is greater than given value\r\n*\t\r\n* EX:\tmaxNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction maxNumber(field , fieldName , max , controlMessage , validationMessage)\r\n{\r\n\tif (field &gt; max)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the number is less than given value\r\n*\t\r\n* EX:\tminNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\n\r\nfunction minNumber(field , fieldName , min , controlMessage , validationMessage)\r\n{\r\n\tif (field &lt; min)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a coach validation error if the field is null 'Mandatory'\r\n*\t\r\n* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\r\nfunction mandatory(field , fieldName)\r\n{\r\n\tif (field == null || field == undefined )\r\n\t{\r\n\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\tmandatoryTriggered = true;\r\n\t\treturn false;\r\n\t}\r\n\telse\r\n\t{\r\n\t\tswitch (typeof field)\r\n\t\t{\r\n\t\t\tcase \"string\":\r\n\t\t\t\tif (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"number\":\r\n\t\t\t\tif (field == 0.0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , \"This Field Is Mandatory\" , \"Mandatory Fields\" , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t}\r\n\t}\r\n\treturn true;\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a validation mark on the tab if there is an error\r\n*\t\r\n* EX:\tvalidateTab(1)\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\r\nfunction validateTab(index)\r\n{\r\n\tif (tw.system.coachValidation.validationErrors.length &gt; tempLength)\r\n\t{\r\n\t\ttempLength = tw.system.coachValidation.validationErrors.length ;\r\n\/\/\t\ttw.local.invalidTabs[tw.local.invalidTabs.length] = index;\r\n\t}\t\r\n}\r\n\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a validation mark on the tab if there is an error\r\n*\t\r\n* EX:\tvalidateTab(1)\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\/\/function validateDecimal(field, fieldName, controlMessage , validationMessage) {\r\n\/\/   regexString = `^\\\\d{1,12}(\\\\.\\\\d{1,12})?$`;\r\n\/\/   regex = new RegExp(regexString);\r\n\/\/\r\n\/\/  if (!regex.test(field))\r\n\/\/\t{\r\n\/\/\t\taddError(fieldName , controlMessage , validationMessage);\r\n\/\/\t\treturn false;\r\n\/\/\t}\r\n\/\/\treturn true;\r\n\/\/}\r\n\/\/-----------------------------------------financial Details---------------------------------------------------------\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.documentAmount,\"tw.local.idcRequest.financialDetails.documentAmount\");\r\n\/\/\/\/validateDecimal(tw.local.idcRequest.financialDetails.documentAmount, \"tw.local.idcRequest.financialDetails.documentAmount\", \"max length is 14\" , \"max length is 14\");\r\n\/\/minNumber(tw.local.idcRequest.financialDetails.documentAmount , \"tw.local.idcRequest.financialDetails.documentAmount\" , 0.01 , \"must be more than 0\" , \"must be more than 0\");\r\n\/\/\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.chargesAccount,\"tw.local.idcRequest.financialDetails.chargesAccount\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.paymentAccount,\"tw.local.idcRequest.financialDetails.paymentAccount\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.documentCurrency.code,\"tw.local.idcRequest.financialDetails.documentCurrency.code\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.code,\"tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.code\");\r\n\/\/mandatory(tw.local.idcRequest.financialDetails.sourceOfFunds.code,\"tw.local.idcRequest.financialDetails.sourceOfFunds.code\");\r\n\/\/validateTab(2);\r\n\/\/\/\/----------------------------------basic details------------------------------------------------------------------------------\r\n\/\/\r\n\/\/mandatory(tw.local.idcRequest.importPurpose.code,\"tw.local.idcRequest.importPurpose.code\");\r\n\/\/mandatory(tw.local.idcRequest.paymentTerms.code,\"tw.local.idcRequest.paymentTerms.code\");\r\n\/\/mandatory(tw.local.idcRequest.documentsSource.code,\"tw.local.idcRequest.documentsSource.code\");\r\n\/\/mandatory(tw.local.idcRequest.productCategory.code,\"tw.local.idcRequest.productCategory.code\");\r\n\/\/mandatory(tw.local.idcRequest.commodityDescription,\"tw.local.idcRequest.commodityDescription\");\r\n\/\/if (tw.local.idcRequest.invoices.length&gt;0){\r\n\/\/\tfor (var i=0; i&lt;tw.local.idcRequest.invoices.length; i++) {\r\n\/\/\t\tmandatory(tw.local.idcRequest.invoices[i].number,\"tw.local.idcRequest.invoices[\"+i+\"].number\");\r\n\/\/\t\tmandatory(tw.local.idcRequest.invoices[i].date,\"tw.local.idcRequest.invoices[\"+i+\"].date\");\r\n\/\/\t}\r\n\/\/}\r\n\/\/if(tw.local.idcRequest.billOfLading.length&gt;0){\r\n\/\/\tfor (var i=0; i&lt;tw.local.idcRequest.billOfLading.length; i++) {\r\n\/\/\t\tmandatory(tw.local.idcRequest.billOfLading[i].number,\"tw.local.idcRequest.billOfLading[\"+i+\"].number\");\r\n\/\/\t\tmandatory(tw.local.idcRequest.billOfLading[i].date,\"tw.local.idcRequest.billOfLading[\"+i+\"].date\");\r\n\/\/\t}\r\n\/\/}\r\n\/\/mandatory(tw.local.idcRequest.countryOfOrigin.code,\"tw.local.idcRequest.countryOfOrigin.code\");\r\n\/\/validateTab(1);\r\n\/\/----------------------------------------app info------------------------------------------------------------------------------------\r\nmandatory(tw.local.selectedAction,\"tw.local.selectedAction\");\r\n\r\n"]}},{"outgoing":["2027.1645c156-332c-4f50-91bd-58feff9d3d96","2027.3988f0d8-63c3-4322-b28e-93b7e3987906"],"incoming":["2027.90acfe32-a1ff-44f2-84e6-03a9b90269b3"],"default":"2027.1645c156-332c-4f50-91bd-58feff9d3d96","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":405,"y":322,"declaredType":"TNodeVisualInfo","height":32}],"preAssignmentScript":["if (tw.local.errorExist || (tw.system.coachValidation.validationErrors.length &gt; 0 )) {\r\n\ttw.local.validation = false;\r\n}\r\nelse{\r\n\ttw.local.validation = true;\r\n}"]},"name":"Have Errors","declaredType":"exclusiveGateway","id":"2025.fa649319-83e5-4d10-a087-6aa2d7d090c3"},{"targetRef":"2025.5b35a7b3-6de8-4817-ac2f-9a61a6ef84b3","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftBottom","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Copy of Copy of no","declaredType":"sequenceFlow","id":"2027.1645c156-332c-4f50-91bd-58feff9d3d96","sourceRef":"2025.fa649319-83e5-4d10-a087-6aa2d7d090c3"},{"targetRef":"2025.2acbb661-0ba9-4950-a40d-5275b121f9a3","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.validation\t  ==\t  false"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightBottom","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Copy of Copy of yes","declaredType":"sequenceFlow","id":"2027.3988f0d8-63c3-4322-b28e-93b7e3987906","sourceRef":"2025.fa649319-83e5-4d10-a087-6aa2d7d090c3"},{"targetRef":"2025.fa649319-83e5-4d10-a087-6aa2d7d090c3","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Have Errors","declaredType":"sequenceFlow","id":"2027.90acfe32-a1ff-44f2-84e6-03a9b90269b3","sourceRef":"2025.9945acca-fa22-4aff-8de7-9627ae3cd563"},{"itemSubjectRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","name":"accountsList","isCollection":true,"declaredType":"dataObject","id":"2056.272ee29e-56a2-4681-877b-05cd47cd0b0c"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"validation","isCollection":false,"declaredType":"dataObject","id":"2056.f6d383ea-98c2-47a4-8b6b-046a07cf4188"},{"itemSubjectRef":"itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67","name":"tmpUsedAdvancePayment","isCollection":false,"declaredType":"dataObject","id":"2056.4d544179-d33b-4f85-835e-72da18ef5071"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"DEFAULT\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"currencyVis","isCollection":false,"declaredType":"dataObject","id":"2056.7e7d8bdc-1d25-40ab-8bf2-08312fc96a92"},{"startQuantity":1,"outgoing":["2027.1ead6deb-ca24-43e4-8e58-67d5629fdb2d"],"incoming":["2027.ef757f52-d8cf-456c-8d98-1ff426b95867"],"default":"2027.1ead6deb-ca24-43e4-8e58-67d5629fdb2d","extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":694,"y":15,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Handling Error","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.5f644ad3-b1d2-4142-886a-596de3dc6a14","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.errorMSG = String(tw.error.data);\r\ntw.local.errorVIS = \"EDITABLE\";"]}},{"parallelMultiple":false,"outgoing":["2027.ef757f52-d8cf-456c-8d98-1ff426b95867"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2025.7e199729-53be-4494-87db-31743b7977fe"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition"}],"attachedToRef":"2025.e16159fa-302a-4b8a-b108-2919f0ef72b2","extensionElements":{"default":["2027.ef757f52-d8cf-456c-8d98-1ff426b95867"],"nodeVisualInfo":[{"width":24,"x":730,"y":153,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"2025.b2425ca1-bb30-4471-8d35-090867b2a0a3","outputSet":{}},{"targetRef":"2025.5f644ad3-b1d2-4142-886a-596de3dc6a14","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Handling Error","declaredType":"sequenceFlow","id":"2027.ef757f52-d8cf-456c-8d98-1ff426b95867","sourceRef":"2025.b2425ca1-bb30-4471-8d35-090867b2a0a3"},{"targetRef":"2025.2acbb661-0ba9-4950-a40d-5275b121f9a3","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Waiting CBE Approval","declaredType":"sequenceFlow","id":"2027.1ead6deb-ca24-43e4-8e58-67d5629fdb2d","sourceRef":"2025.5f644ad3-b1d2-4142-886a-596de3dc6a14"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.c2adc175-f2a9-4c73-8d02-91da551b24f5"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorVIS","isCollection":false,"declaredType":"dataObject","id":"2056.fd22afec-d6fc-443a-8003-f4b726264301"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"alertMessage","isCollection":false,"declaredType":"dataObject","id":"2056.52ec9d02-f232-4dd1-8ef2-1b7cfaa7e22c"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"declaredType":"dataObject","id":"2056.33ff724e-9558-4835-80ee-02cc61b9b468"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"invalidTabs","isCollection":true,"declaredType":"dataObject","id":"2056.3d6340db-0cbc-403d-841c-d30b1612b75d"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"762bed24-ca9f-4470-8f06-077556a5ef21","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"e1af27ee-7cb9-42dd-bfb1-a533af8bbf74","processType":"None"}],"exposedAs":["NotExposed"]},"implementation":"##unspecified","documentation":[{"textFormat":"text\/plain"}],"name":"Waiting CBE Approval","declaredType":"globalUserTask","id":"1.f2c8f018-67e0-416a-acb3-8af30664cba5","ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.19153306-7e7e-4ce0-8d73-b24ade667180"},{"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"attachment","isCollection":true,"id":"2055.690326c2-3e2a-48f2-8091-935656717e2b"},{"itemSubjectRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","name":"treasuryComments","isCollection":true,"id":"2055.dbd6de09-c2b9-41d6-99dd-5de23cc5a7c1"}],"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.8ce8b34e-54bb-4623-a4c9-ab892efacac6","epvProcessLinkId":"d3064db2-6092-4f70-8dbd-1c9c8241823e","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e","epvProcessLinkId":"2d79f5bd-d5d5-493d-8448-f159c5442689","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.02818ba4-c183-4dfb-8924-18e2d9a515dd","epvProcessLinkId":"27088ef6-296e-4256-82f5-11d9cff3e408","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{}],"outputSet":[{}],"dataInput":[{"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"idcRequest","isCollection":false,"id":"2055.03a5f1e9-21d6-4ac6-9894-82edc980e087"},{"itemSubjectRef":"itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f","name":"attachment","isCollection":true,"id":"2055.7a6533b6-bbb9-4f62-aa08-d41a34aa3927"},{"itemSubjectRef":"itm.12.da5cf277-3788-466d-9108-e44438cbc7b1","name":"treasuryComments","isCollection":true,"id":"2055.3b198fb8-2cbb-43a3-8d9a-6f2233fce87a"},{"itemSubjectRef":"itm.12.ae22157e-8e19-4a60-a294-712ff5dc96df","name":"ECMproperties","isCollection":false,"id":"2055.d9261eb9-6067-4b77-b05b-249696e3ac60"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"1.3aefd489-e4fb-47b7-bc43-0ffc83ae5a90"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.03a5f1e9-21d6-4ac6-9894-82edc980e087</processParameterId>
            <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7e5b64be-a694-41d9-b43b-fe3b8f4c4d27</guid>
            <versionId>1232ab57-adb1-4232-85f7-fe6b7ab78baa</versionId>
        </processParameter>
        <processParameter name="attachment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.7a6533b6-bbb9-4f62-aa08-d41a34aa3927</processParameterId>
            <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>40eb605d-faff-4590-aaec-56a1088a8a80</guid>
            <versionId>4ecf3fea-2cf7-491f-a53a-c7bebba6b354</versionId>
        </processParameter>
        <processParameter name="treasuryComments">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.3b198fb8-2cbb-43a3-8d9a-6f2233fce87a</processParameterId>
            <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>75aa40dd-88b8-4eb1-994e-8e8aa80394d6</guid>
            <versionId>2c01c775-24ab-4cca-8464-7babcb69ba48</versionId>
        </processParameter>
        <processParameter name="ECMproperties">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d9261eb9-6067-4b77-b05b-249696e3ac60</processParameterId>
            <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.ae22157e-8e19-4a60-a294-712ff5dc96df</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>e7e8afba-a703-4fcf-a523-22ebef939c23</guid>
            <versionId>a8053e19-c26d-4980-8bbf-7c64ce0f03d2</versionId>
        </processParameter>
        <processParameter name="idcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.19153306-7e7e-4ce0-8d73-b24ade667180</processParameterId>
            <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>0b9e78b1-9215-4477-a721-684d7be5bfe2</guid>
            <versionId>59dbf3f7-8c31-4022-a65b-abd579c61b45</versionId>
        </processParameter>
        <processParameter name="attachment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.690326c2-3e2a-48f2-8091-935656717e2b</processParameterId>
            <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>3e7a965d-631a-414b-bce6-b4793c5e66cc</guid>
            <versionId>dc443dd0-3ec8-4306-b41b-61f6d5a01fea</versionId>
        </processParameter>
        <processParameter name="treasuryComments">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.dbd6de09-c2b9-41d6-99dd-5de23cc5a7c1</processParameterId>
            <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.da5cf277-3788-466d-9108-e44438cbc7b1</classId>
            <seq>7</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>48daaa00-f5e9-48bc-92c5-cb0b167a549c</guid>
            <versionId>606b048b-b3e0-4eaa-bd73-77164dfb8dbf</versionId>
        </processParameter>
        <processVariable name="action">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c9b305bb-a4e3-489c-9040-0fea6b8979ee</processVariableId>
            <description isNull="true" />
            <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>11f3b4e8-173a-4457-84b5-7ae0f869338b</guid>
            <versionId>7dad1932-d167-457d-93f1-2959e67f2a26</versionId>
        </processVariable>
        <processVariable name="selectedAction">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.528ea898-4984-4b92-b20b-9bc1cbadd9e8</processVariableId>
            <description isNull="true" />
            <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>798e11a9-6ad8-4d60-b03e-9ce5b5a7bdf7</guid>
            <versionId>bfcc5207-f0a2-4564-be5a-08e958a1e5cc</versionId>
        </processVariable>
        <processVariable name="accountsList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.272ee29e-56a2-4681-877b-05cd47cd0b0c</processVariableId>
            <description isNull="true" />
            <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>6c0b7c6b-abf3-4fb8-b1ce-89621b140390/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>02e36c87-c7be-48c6-a1c6-d69e74cbe542</guid>
            <versionId>cbff8ea9-652b-4780-8536-4ae20f4b71cb</versionId>
        </processVariable>
        <processVariable name="validation">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f6d383ea-98c2-47a4-8b6b-046a07cf4188</processVariableId>
            <description isNull="true" />
            <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0d2a98d9-7439-4996-bdb2-823b62eda218</guid>
            <versionId>6029307e-9c4a-44e2-8b4f-54767f01aa12</versionId>
        </processVariable>
        <processVariable name="tmpUsedAdvancePayment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4d544179-d33b-4f85-835e-72da18ef5071</processVariableId>
            <description isNull="true" />
            <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.fc23c5c1-8fd3-4584-89be-8352a606da67</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f34f50c9-bc70-4629-8c69-566a7aaf2c43</guid>
            <versionId>56f51ae5-37d6-41ae-93d4-874cfd7b58bb</versionId>
        </processVariable>
        <processVariable name="currencyVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7e7d8bdc-1d25-40ab-8bf2-08312fc96a92</processVariableId>
            <description isNull="true" />
            <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>eae14eeb-c6ce-4ff3-8d82-132e2d43ea6c</guid>
            <versionId>6164b6d3-089e-47d1-a040-4da189c149ea</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c2adc175-f2a9-4c73-8d02-91da551b24f5</processVariableId>
            <description isNull="true" />
            <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>87c13896-30c8-4f60-8ca5-c8b0aa77cf3a</guid>
            <versionId>85265abe-6def-474f-8a48-aabe8d08be0d</versionId>
        </processVariable>
        <processVariable name="errorVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.fd22afec-d6fc-443a-8003-f4b726264301</processVariableId>
            <description isNull="true" />
            <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>24bec66f-5074-40b5-b059-7c4972e1e28d</guid>
            <versionId>e17f0000-e426-458a-831b-17545843b89e</versionId>
        </processVariable>
        <processVariable name="alertMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.52ec9d02-f232-4dd1-8ef2-1b7cfaa7e22c</processVariableId>
            <description isNull="true" />
            <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3a774aaa-26bf-4e41-92f1-4bd694c63dad</guid>
            <versionId>3a523f02-2183-44f0-9144-26bc71fc21ed</versionId>
        </processVariable>
        <processVariable name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.33ff724e-9558-4835-80ee-02cc61b9b468</processVariableId>
            <description isNull="true" />
            <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ffd29820-31fc-451b-965a-a4462e59812e</guid>
            <versionId>32950d7c-464a-4adb-a4fc-d25581a956cb</versionId>
        </processVariable>
        <processVariable name="invalidTabs">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3d6340db-0cbc-403d-841c-d30b1612b75d</processVariableId>
            <description isNull="true" />
            <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>aef28ad8-1538-4d26-8287-c70df3b38c44</guid>
            <versionId>eb8b4c29-bc47-41c3-b0c8-33c40dbd0dbc</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.017f41e3-4a25-4dfa-b598-af7ed48d49b3</processItemId>
            <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.ca3af724-62a5-479f-a5fd-8e771901c37e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-60c6</guid>
            <versionId>629d20ee-e850-4a73-8bd8-00e7725fa5a5</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.e16159fa-302a-4b8a-b108-2919f0ef72b2</processItemId>
            <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
            <name>Update History</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.29970d62-62a4-40c1-b38d-91eae300cb48</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-60c7</guid>
            <versionId>9db2f4bb-d9ea-4afd-bcba-eebc5235f318</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.29970d62-62a4-40c1-b38d-91eae300cb48</subProcessId>
                <attachedProcessRef>/1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59</attachedProcessRef>
                <guid>2ac1797b-f9b4-41d5-9b8c-d400246af49e</guid>
                <versionId>7c7b9f59-f065-4668-be9b-eae73dedb2dd</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.7b7f224c-1d85-4be7-bc13-b1913451588d</processItemId>
            <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.0c62c2f2-97b8-4ceb-9a79-373fe6a85b6e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-60c8</guid>
            <versionId>ff9bcbb5-aa08-4d25-9f10-d6930d96de24</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.0c62c2f2-97b8-4ceb-9a79-373fe6a85b6e</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>a1265e08-5c22-447f-8d03-1b2b27196737</guid>
                <versionId>20c3f1de-f68f-4af5-bb57-dfbaf6d47c8c</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.3eb7712a-54e8-467a-867d-598132a08a67</epvProcessLinkId>
            <epvId>/21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e</epvId>
            <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
            <guid>a7d9abfe-f92f-49c0-a942-e457030d6294</guid>
            <versionId>4ba4b520-2223-4fa4-98a4-863e9e489fcf</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.ef50bce1-a822-496f-9ecc-35eeaed64a50</epvProcessLinkId>
            <epvId>/21.02818ba4-c183-4dfb-8924-18e2d9a515dd</epvId>
            <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
            <guid>7a88c199-7a6a-4d3a-a59b-3db04f748371</guid>
            <versionId>4ec13cc2-3613-439a-91fc-a8b0e4117a6f</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.1ec89cd8-bc44-4027-b254-573137eb6b4d</epvProcessLinkId>
            <epvId>/21.8ce8b34e-54bb-4623-a4c9-ab892efacac6</epvId>
            <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
            <guid>f7766488-791a-4bad-8613-12d29512865c</guid>
            <versionId>cdb6d687-8f94-4034-ae0c-f0aa77f3bc4f</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.017f41e3-4a25-4dfa-b598-af7ed48d49b3</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="1.3aefd489-e4fb-47b7-bc43-0ffc83ae5a90" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:globalUserTask implementation="##unspecified" name="Waiting CBE Approval" id="1.f2c8f018-67e0-416a-acb3-8af30664cba5">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:userTaskImplementation processType="None" isClosed="false" id="e1af27ee-7cb9-42dd-bfb1-a533af8bbf74">
                            
                            
                            <ns16:startEvent name="Start" id="3c61f056-712b-4290-b0ce-2c1b69f65785">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="50" y="188" width="24" height="24" color="#F8F8F8" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.32359e17-5e44-40d3-b3ec-8aa9f7537828</ns16:outgoing>
                                
                            
                            </ns16:startEvent>
                            
                            
                            <ns16:endEvent name="End" id="9404282f-3ec6-490c-b763-1d5fcf5223bb">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="848" y="188" width="24" height="44" color="#F8F8F8" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.b6e13286-1293-4b41-9b33-9dc4bdcaf36d</ns16:incoming>
                                
                            
                            </ns16:endEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="3c61f056-712b-4290-b0ce-2c1b69f65785" targetRef="2025.674f24d4-c1fe-42d6-9959-cdf0d266ad30" name="To Waiting CBE Approval" id="2027.32359e17-5e44-40d3-b3ec-8aa9f7537828">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.0d099e1d-0221-4791-adf5-b3b5603ef3b7" name="Set Status" id="2025.5b35a7b3-6de8-4817-ac2f-9a61a6ef84b3">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="543" y="165" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.1645c156-332c-4f50-91bd-58feff9d3d96</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.0d099e1d-0221-4791-adf5-b3b5603ef3b7</ns16:outgoing>
                                
                                
                                <ns16:script>tw.local.idcRequest.appInfo.status = tw.epv.IDCStatus.inApproval;&#xD;
tw.local.idcRequest.appInfo.subStatus = tw.epv.IDCsubStatus.pendingTreasuryMakerReview;&#xD;
tw.local.idcRequest.stepLog.action = tw.local.selectedAction;</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.5b35a7b3-6de8-4817-ac2f-9a61a6ef84b3" targetRef="2025.e16159fa-302a-4b8a-b108-2919f0ef72b2" name="OK To End" id="2027.0d099e1d-0221-4791-adf5-b3b5603ef3b7">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns3:formTask name="Waiting CBE Approval" id="2025.2acbb661-0ba9-4950-a40d-5275b121f9a3">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="290" y="165" width="95" height="70" />
                                    
                                    
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.d00333e9-33a0-4889-bb8d-c077e065ead7</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.8dd78d4e-4141-4264-930c-dc82bc6922a2</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.3988f0d8-63c3-4322-b28e-93b7e3987906</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.1ead6deb-ca24-43e4-8e58-67d5629fdb2d</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.c231611b-3570-446c-885b-89f387631a12</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.88b8952f-e31b-4936-842e-19b00dc85c73</ns16:outgoing>
                                
                                
                                <ns3:formDefinition>
                                    
                                    
                                    <ns19:coachDefinition>
                                        
                                        
                                        <ns19:layout>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>a4e2a666-7990-44ad-8802-492acecb77fe</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>Error_Message1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>a12c544f-fe1f-4f23-8a19-d52a266c4e24</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Error Message</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>d316447e-0949-4bf8-8e6c-f768c0e6d94f</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>056c7557-59ec-4d38-8d5f-de239c7f3d11</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>SHOW</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>0f4da1b9-d0f3-4fdf-8343-9399823a4c83</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>d7359ac0-e371-4eb3-80c0-83699ba63ff0</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>36570bf1-abf6-4d62-8ea0-5226bba4fb3d</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.46690ca1-5521-4e5c-bc9e-ecb22d75ca97</ns19:viewUUID>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>771c12db-0a3f-42f5-8e24-2ce6e5f451c3</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>DC_Templete1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>da058063-6812-40af-8dde-e8ff368bb926</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>DC Templete</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>d3ccf651-ac46-4292-8a51-fd6b5e1eca70</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>ae3b1e74-4a28-4574-8674-714470daeef9</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>SHOW</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>40117d7b-39e1-4d10-8d02-1949964268e5</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>stepLog</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.idcRequest.stepLog</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>e6cf5fb3-5f2a-449d-81e3-13f0a5bd9976</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>invalidTabs</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.invalidTabs[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>b7aaac28-65bf-41de-896e-1e96683e4d29</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>action</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.action[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>2c40fa32-49c9-4e67-8486-6c2298b4874b</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>selectedAction</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.selectedAction</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>a2acf58f-c703-4e1e-8aca-b6bed1372659</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>buttonName</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Submit</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>449b555e-1143-4309-8504-63a14bcd39bf</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>hasApprovals</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>false</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>fcd5cc64-4023-4fee-8de2-42c2614e7bf9</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>hasReturnReason</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>false</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>83b7d27e-**************-039159149824</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>approvals</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.idcRequest.approvals</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>38225f16-05b3-451d-88fa-326a07646e14</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>approvalsReadOnly</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>false</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>5a34f750-5f0d-4464-8515-a1402fc287ab</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>isCAD</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>false</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.f9e6899d-e7d7-4296-ba71-268fcd57e296</ns19:viewUUID>
                                                
                                                
                                                <ns19:binding>tw.local.idcRequest.appInfo</ns19:binding>
                                                
                                                
                                                <ns19:contentBoxContrib>
                                                    
                                                    
                                                    <ns19:id>84e4d9d9-c684-46e6-887e-4cf4fb9be60a</ns19:id>
                                                    
                                                    
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    
                                                    
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        
                                                        
                                                        <ns19:id>bdb77abf-ff28-4cd5-8de5-84fe862e2c89</ns19:id>
                                                        
                                                        
                                                        <ns19:layoutItemId>Tab_Section1</ns19:layoutItemId>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>d51d0add-4559-4a2c-88bd-6db19b90032c</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>Tab section</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>49ea0b35-1b3d-4392-8c14-539d8e93d613</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value />
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>2ad1ba70-b1c2-482b-8978-cb84312505b3</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>SHOW</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>49c0138a-0f87-43b5-8e2a-d8d0c08d03c2</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>sizeStyle</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"X"}]}</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:viewUUID>64.c05b439f-a4bd-48b0-8644-fa3b59052217</ns19:viewUUID>
                                                        
                                                        
                                                        <ns19:contentBoxContrib>
                                                            
                                                            
                                                            <ns19:id>29da4323-1dd0-4ee9-89d1-a45bf9f8a1fb</ns19:id>
                                                            
                                                            
                                                            <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>850918f3-ae1e-4365-8b42-7e601153ed48</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Customer_Information1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>101e9834-c041-4582-814e-8d22d4ebbcab</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Customer Information</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>2e42db77-2de0-45ae-82a0-3242b2e9057b</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>4a058bb4-ad15-48ab-88e0-b63f40efda2a</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>e53cf7d7-a310-458e-8ef3-3891809f1a5a</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>instanceview</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>425857a2-618a-43db-8731-b4698d9867f6</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.alertMessage</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.656cc232-8247-43c3-9481-3cc7a9aec2e6</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.idcRequest.customerInformation</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>c1dca3b8-f486-43cd-8833-25fa1c324441</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Basic_Details1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>4414cf20-349d-4ed9-8d8d-4f7a3188bd91</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Basic Details</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>c5805473-b50a-4482-86da-d98cbd50537d</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>962d7d3b-762c-4eba-8fb1-e83fc8f5cc82</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>8eb175ee-1166-435e-8ef1-e6931361e8a1</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.3d13e9b9-bcc9-4fe9-ac80-3c287115f705</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.idcRequest</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>21822f84-952b-4edc-852a-6163e26e0ee4</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Financial_Details__Branch1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>26502982-8e97-4095-802d-6ef1a80c09ed</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Financial Details  Branch</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>1402c8d8-c9e3-40eb-8e45-32876547b265</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>f170aa14-8d89-4e33-8cc9-f6a08ac76857</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>a9bcdc87-e629-413d-8f31-ffe16c5a7c6a</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>CIF</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.idcRequest.customerInformation.CIFNumber</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>1cd914ab-5f19-4a0e-8e44-05dbbf20ec9c</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>accountsList</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.accountsList[]</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>18dffc5b-7182-45f8-8897-4b83086e9da2</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>requestType</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.idcRequest.IDCRequestType.englishdescription</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>1dd4b645-89d6-4eef-8fc0-ffb710b1c9ad</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>haveAmountAdvanced</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>DEFAULT</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>b2029fda-43ad-49c2-8a82-2cb6b186cab1</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>isChecker</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>47bd5566-5e41-4ed1-8159-f39170340bfe</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>tmpUsedAdvancePayment</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.tmpUsedAdvancePayment</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>92193b68-a5f8-4866-869c-032236bda66b</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>currencyVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.currencyVis</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>d88e3357-7df9-45e0-82d2-ca4f871524f0</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>requestID</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.idcRequest.appInfo.instanceID</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>bf603f9e-f50f-4e42-8fcd-da0ba7777e3b</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>alertMessage</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.errorMSG</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>79de72d9-2ef3-4d13-8d18-4134cadba452</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>errorVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.errorVIS</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.74d3cb97-ad59-4249-847b-a21122e44b22</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.idcRequest.financialDetails</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>82c50fbb-d5f6-4bb1-8dda-6575cfd71fef</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Attachment1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>0d54045e-f098-4464-844a-4933dc007e91</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Attachment</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>a83a0620-8b05-47fe-8be7-************</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>640b825b-510c-422e-8ea5-697760366b7f</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>84880d15-8a1c-4912-8853-8ba61f06a11f</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canDelete</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>cabce9f3-22d7-495d-888f-51a12c61fc6c</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canCreate</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>74b258c0-dd7d-430b-8c7c-010b38c0bd93</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canUpdate</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>0b1ee9ed-4b2a-4522-830d-9c666d7b60aa</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>ECMproperties</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.ECMproperties</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>1414247d-cef8-499c-816e-17d763b9bd38</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>visiable</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.f1067f18-e2fe-4f8d-8cdb-a948cb0eb0f7</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.attachment[]</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>28cfe003-1095-40fe-8f0a-59ee42fd0d3a</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>App_History_View_21</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>dde162a0-7075-4fde-8ed7-a70616672091</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>History</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>5167b6d3-00c4-4c8f-8734-622d568e6a22</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>da87eb96-32e5-477d-8577-3a1ad8cad61e</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>af233f4a-44de-4362-83d7-e790132f32e8</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>historyVisFlag</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>None</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.6ac8a533-b05a-45a1-9e46-1c4542e6b8be</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.treasuryComments[]</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                        
                                                        </ns19:contentBoxContrib>
                                                        
                                                    
                                                    </ns19:contributions>
                                                    
                                                
                                                </ns19:contentBoxContrib>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                        
                                        </ns19:layout>
                                        
                                    
                                    </ns19:coachDefinition>
                                    
                                
                                </ns3:formDefinition>
                                
                            
                            </ns3:formTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.2acbb661-0ba9-4950-a40d-5275b121f9a3" targetRef="2025.9945acca-fa22-4aff-8de7-9627ae3cd563" name="To End" id="2027.c231611b-3570-446c-885b-89f387631a12">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="05a11345-3198-4728-8c5f-89c5c3248c34">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/submit</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page" id="2025.81a3add5-a097-4468-ae4c-5ea8b28906a1">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="301" y="44" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                    
                                    <ns3:default>2027.d00333e9-33a0-4889-bb8d-c077e065ead7</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.88b8952f-e31b-4936-842e-19b00dc85c73</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.d00333e9-33a0-4889-bb8d-c077e065ead7</ns16:outgoing>
                                
                                
                                <ns3:postponeTaskEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.2acbb661-0ba9-4950-a40d-5275b121f9a3" targetRef="2025.81a3add5-a097-4468-ae4c-5ea8b28906a1" name="To Stay on page" id="2027.88b8952f-e31b-4936-842e-19b00dc85c73">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topLeft</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="456b631e-9631-4f92-bbd6-29e1713c276c">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/saveState</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.81a3add5-a097-4468-ae4c-5ea8b28906a1" targetRef="2025.2acbb661-0ba9-4950-a40d-5275b121f9a3" name="To Waiting CBE Approval" id="2027.d00333e9-33a0-4889-bb8d-c077e065ead7">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:callActivity calledElement="1.a9a7070b-cdbb-4f64-af6c-39dafcf87f59" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.b6e13286-1293-4b41-9b33-9dc4bdcaf36d" name="Update History" id="2025.e16159fa-302a-4b8a-b108-2919f0ef72b2">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="695" y="165" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.0d099e1d-0221-4791-adf5-b3b5603ef3b7</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.b6e13286-1293-4b41-9b33-9dc4bdcaf36d</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.648598d0-2039-40d4-b60b-3753a273a378</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.idcRequest.stepLog</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.5fca703f-c44e-4efc-b6ac-6b71dd05abf0</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.treasuryComments</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.df0b4fa5-42fa-431d-8822-9eeb4e99abc8</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Treasury Maker"</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.322bdb97-0698-43d7-8172-71cbc933103d</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67">tw.local.idcRequest</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.ebf12d20-c385-4ebf-81b8-8053911f8aa6</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.treasuryComments</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.65675974-9215-43be-8dce-3b75511a591d</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.idcRequest.stepLog</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.8fcdef92-a110-407f-aff8-5693f497f953</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.treasuryComments</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.236df123-3f3f-4bdd-8cba-b6a13fa9de98</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1">tw.local.treasuryComments</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.60a8424b-59f2-4328-8d4f-c388b30e202f</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.fce152d9-1c42-43bc-8bff-44f6b45aba67</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" />
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.e16159fa-302a-4b8a-b108-2919f0ef72b2" targetRef="9404282f-3ec6-490c-b763-1d5fcf5223bb" name="To End" id="2027.b6e13286-1293-4b41-9b33-9dc4bdcaf36d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.8dd78d4e-4141-4264-930c-dc82bc6922a2" name="Set Step Name" id="2025.c74cc14f-e2d1-4128-adb7-948a7c8e28ad">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="138" y="165" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.a1aca0da-291a-4fee-8741-24d94dfca080</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.8dd78d4e-4141-4264-930c-dc82bc6922a2</ns16:outgoing>
                                
                                
                                <ns16:script>tw.local.idcRequest.appInfo.stepName = tw.system.processInstance.task.subject;&#xD;
tw.local.idcRequest.appInfo.appID = tw.system.processInstance.id;&#xD;
&#xD;
</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.c74cc14f-e2d1-4128-adb7-948a7c8e28ad" targetRef="2025.2acbb661-0ba9-4950-a40d-5275b121f9a3" name="To Waiting CBE Approval" id="2027.8dd78d4e-4141-4264-930c-dc82bc6922a2">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.a1aca0da-291a-4fee-8741-24d94dfca080" name="Initialization Script" id="2025.674f24d4-c1fe-42d6-9959-cdf0d266ad30">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="138" y="62" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.32359e17-5e44-40d3-b3ec-8aa9f7537828</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.a1aca0da-291a-4fee-8741-24d94dfca080</ns16:outgoing>
                                
                                
                                <ns16:script>tw.local.idcRequest.stepLog = {};&#xD;
tw.local.idcRequest.stepLog.startTime = new Date();&#xD;
&#xD;
tw.local.errorVIS = "NONE";&#xD;
tw.local.action = [];&#xD;
tw.local.action[0]=tw.epv.Action.sendBackToMainQueue;</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.674f24d4-c1fe-42d6-9959-cdf0d266ad30" targetRef="2025.c74cc14f-e2d1-4128-adb7-948a7c8e28ad" name="To Set Step Name" id="2027.a1aca0da-291a-4fee-8741-24d94dfca080">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="action" id="2056.c9b305bb-a4e3-489c-9040-0fea6b8979ee" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="selectedAction" id="2056.528ea898-4984-4b92-b20b-9bc1cbadd9e8" />
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.90acfe32-a1ff-44f2-84e6-03a9b90269b3" name="Validation" id="2025.9945acca-fa22-4aff-8de7-9627ae3cd563">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="290" y="303" width="95" height="70" color="#95D087" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.c231611b-3570-446c-885b-89f387631a12</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.90acfe32-a1ff-44f2-84e6-03a9b90269b3</ns16:outgoing>
                                
                                
                                <ns16:script>var message = "";&#xD;
var mandatoryTriggered = false;&#xD;
var tempLength = 0 ;&#xD;
//var invalidTabs = [];&#xD;
/*&#xD;
* =========================================================================================================&#xD;
*  &#xD;
* Add a coach validation error &#xD;
* 		&#xD;
* EX:	addError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');&#xD;
*&#xD;
* =========================================================================================================&#xD;
*/&#xD;
&#xD;
function addError(fieldName , controlMessage , validationMessage , fromMandatory)&#xD;
{&#xD;
	tw.system.coachValidation.addValidationError(fieldName, controlMessage);&#xD;
	fromMandatory &amp;&amp; mandatoryTriggered ? "" : tw.local.message += "&lt;li dir='rtl'&gt;" + validationMessage + "&lt;/li&gt;";&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is in the past and the last variable to exclude today&#xD;
*	&#xD;
* EX:	notPastDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)&#xD;
*&#xD;
* =============================================================================================================================&#xD;
*/&#xD;
&#xD;
function notPastDate(date , fieldName , controlMessage , validationMessage , exclude)&#xD;
{&#xD;
	if (exclude)&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &lt;= new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;	&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &lt; new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is between two dates&#xD;
*	&#xD;
* EX:	dateBetween(tw.local.date , date1 , date2 , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =============================================================================================================================&#xD;
*/&#xD;
&#xD;
function dateBetween(field , date1 , date2 , fieldName , controlMessage , validationMessage)&#xD;
{&#xD;
	if(field &lt; date1 &amp;&amp; field &gt; date2)&#xD;
	{&#xD;
	 	return true;&#xD;
	}&#xD;
	addError(fieldName , controlMessage , validationMessage);&#xD;
	return false;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ===============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the date is in the future and the last varaible to exculde today&#xD;
*	&#xD;
* EX:	notFutureDate(tw.local.date , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!' , false)&#xD;
*&#xD;
* ===============================================================================================================================&#xD;
*/&#xD;
&#xD;
function notFutureDate(date , fieldName ,  controlMessage , validationMessage , exculde)&#xD;
{&#xD;
	if (exculde)&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &gt;= new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;	&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		if(date != null &amp;&amp; date &gt; new Date())&#xD;
		{&#xD;
			addError(fieldName , controlMessage , validationMessage);&#xD;
			return false;&#xD;
		}&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
&#xD;
/*&#xD;
* =================================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the string length is less than given length&#xD;
*	&#xD;
* EX:	minLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =================================================================================================================================&#xD;
*/&#xD;
&#xD;
function minLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the string length is greater than given length&#xD;
*	&#xD;
* EX:	maxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function maxLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &gt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the number is greater than given value&#xD;
*	&#xD;
* EX:	maxNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function maxNumber(field , fieldName , max , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field &gt; max)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the number is less than given value&#xD;
*	&#xD;
* EX:	minNumber(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
&#xD;
function minNumber(field , fieldName , min , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field &lt; min)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the field is null 'Mandatory'&#xD;
*	&#xD;
* EX:	notNull(tw.local.name , 'tw.local.name')&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
&#xD;
function mandatory(field , fieldName)&#xD;
{&#xD;
	if (field == null || field == undefined )&#xD;
	{&#xD;
		addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
		mandatoryTriggered = true;&#xD;
		return false;&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		switch (typeof field)&#xD;
		{&#xD;
			case "string":&#xD;
				if (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)&#xD;
				{&#xD;
					addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
			case "number":&#xD;
				if (field == 0.0)&#xD;
				{&#xD;
					addError(fieldName , "This Field Is Mandatory" , "Mandatory Fields" , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
		}&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a validation mark on the tab if there is an error&#xD;
*	&#xD;
* EX:	validateTab(1)&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
&#xD;
function validateTab(index)&#xD;
{&#xD;
	if (tw.system.coachValidation.validationErrors.length &gt; tempLength)&#xD;
	{&#xD;
		tempLength = tw.system.coachValidation.validationErrors.length ;&#xD;
//		tw.local.invalidTabs[tw.local.invalidTabs.length] = index;&#xD;
	}	&#xD;
}&#xD;
&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a validation mark on the tab if there is an error&#xD;
*	&#xD;
* EX:	validateTab(1)&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
//function validateDecimal(field, fieldName, controlMessage , validationMessage) {&#xD;
//   regexString = `^\\d{1,12}(\\.\\d{1,12})?$`;&#xD;
//   regex = new RegExp(regexString);&#xD;
//&#xD;
//  if (!regex.test(field))&#xD;
//	{&#xD;
//		addError(fieldName , controlMessage , validationMessage);&#xD;
//		return false;&#xD;
//	}&#xD;
//	return true;&#xD;
//}&#xD;
//-----------------------------------------financial Details---------------------------------------------------------&#xD;
//mandatory(tw.local.idcRequest.financialDetails.documentAmount,"tw.local.idcRequest.financialDetails.documentAmount");&#xD;
////validateDecimal(tw.local.idcRequest.financialDetails.documentAmount, "tw.local.idcRequest.financialDetails.documentAmount", "max length is 14" , "max length is 14");&#xD;
//minNumber(tw.local.idcRequest.financialDetails.documentAmount , "tw.local.idcRequest.financialDetails.documentAmount" , 0.01 , "must be more than 0" , "must be more than 0");&#xD;
//&#xD;
//mandatory(tw.local.idcRequest.financialDetails.chargesAccount,"tw.local.idcRequest.financialDetails.chargesAccount");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.paymentAccount,"tw.local.idcRequest.financialDetails.paymentAccount");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.documentCurrency.code,"tw.local.idcRequest.financialDetails.documentCurrency.code");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.code,"tw.local.idcRequest.financialDetails.sourceOfForeignCurrency.code");&#xD;
//mandatory(tw.local.idcRequest.financialDetails.sourceOfFunds.code,"tw.local.idcRequest.financialDetails.sourceOfFunds.code");&#xD;
//validateTab(2);&#xD;
////----------------------------------basic details------------------------------------------------------------------------------&#xD;
//&#xD;
//mandatory(tw.local.idcRequest.importPurpose.code,"tw.local.idcRequest.importPurpose.code");&#xD;
//mandatory(tw.local.idcRequest.paymentTerms.code,"tw.local.idcRequest.paymentTerms.code");&#xD;
//mandatory(tw.local.idcRequest.documentsSource.code,"tw.local.idcRequest.documentsSource.code");&#xD;
//mandatory(tw.local.idcRequest.productCategory.code,"tw.local.idcRequest.productCategory.code");&#xD;
//mandatory(tw.local.idcRequest.commodityDescription,"tw.local.idcRequest.commodityDescription");&#xD;
//if (tw.local.idcRequest.invoices.length&gt;0){&#xD;
//	for (var i=0; i&lt;tw.local.idcRequest.invoices.length; i++) {&#xD;
//		mandatory(tw.local.idcRequest.invoices[i].number,"tw.local.idcRequest.invoices["+i+"].number");&#xD;
//		mandatory(tw.local.idcRequest.invoices[i].date,"tw.local.idcRequest.invoices["+i+"].date");&#xD;
//	}&#xD;
//}&#xD;
//if(tw.local.idcRequest.billOfLading.length&gt;0){&#xD;
//	for (var i=0; i&lt;tw.local.idcRequest.billOfLading.length; i++) {&#xD;
//		mandatory(tw.local.idcRequest.billOfLading[i].number,"tw.local.idcRequest.billOfLading["+i+"].number");&#xD;
//		mandatory(tw.local.idcRequest.billOfLading[i].date,"tw.local.idcRequest.billOfLading["+i+"].date");&#xD;
//	}&#xD;
//}&#xD;
//mandatory(tw.local.idcRequest.countryOfOrigin.code,"tw.local.idcRequest.countryOfOrigin.code");&#xD;
//validateTab(1);&#xD;
//----------------------------------------app info------------------------------------------------------------------------------------&#xD;
mandatory(tw.local.selectedAction,"tw.local.selectedAction");&#xD;
&#xD;
</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:exclusiveGateway default="2027.1645c156-332c-4f50-91bd-58feff9d3d96" gatewayDirection="Unspecified" name="Have Errors" id="2025.fa649319-83e5-4d10-a087-6aa2d7d090c3">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="405" y="322" width="32" height="32" />
                                    
                                    
                                    <ns3:preAssignmentScript>if (tw.local.errorExist || (tw.system.coachValidation.validationErrors.length &gt; 0 )) {&#xD;
	tw.local.validation = false;&#xD;
}&#xD;
else{&#xD;
	tw.local.validation = true;&#xD;
}</ns3:preAssignmentScript>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.90acfe32-a1ff-44f2-84e6-03a9b90269b3</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.1645c156-332c-4f50-91bd-58feff9d3d96</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.3988f0d8-63c3-4322-b28e-93b7e3987906</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.fa649319-83e5-4d10-a087-6aa2d7d090c3" targetRef="2025.5b35a7b3-6de8-4817-ac2f-9a61a6ef84b3" name="Copy of Copy of no" id="2027.1645c156-332c-4f50-91bd-58feff9d3d96">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftBottom</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.fa649319-83e5-4d10-a087-6aa2d7d090c3" targetRef="2025.2acbb661-0ba9-4950-a40d-5275b121f9a3" name="Copy of Copy of yes" id="2027.3988f0d8-63c3-4322-b28e-93b7e3987906">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>rightBottom</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.validation	  ==	  false</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.9945acca-fa22-4aff-8de7-9627ae3cd563" targetRef="2025.fa649319-83e5-4d10-a087-6aa2d7d090c3" name="To Have Errors" id="2027.90acfe32-a1ff-44f2-84e6-03a9b90269b3">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42" isCollection="true" name="accountsList" id="2056.272ee29e-56a2-4681-877b-05cd47cd0b0c" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="validation" id="2056.f6d383ea-98c2-47a4-8b6b-046a07cf4188" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.fc23c5c1-8fd3-4584-89be-8352a606da67" isCollection="false" name="tmpUsedAdvancePayment" id="2056.4d544179-d33b-4f85-835e-72da18ef5071" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="currencyVis" id="2056.7e7d8bdc-1d25-40ab-8bf2-08312fc96a92">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="true">"DEFAULT"</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.1ead6deb-ca24-43e4-8e58-67d5629fdb2d" name="Handling Error" id="2025.5f644ad3-b1d2-4142-886a-596de3dc6a14">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="694" y="15" width="95" height="70" color="#FF7782" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.ef757f52-d8cf-456c-8d98-1ff426b95867</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.1ead6deb-ca24-43e4-8e58-67d5629fdb2d</ns16:outgoing>
                                
                                
                                <ns16:script>tw.local.errorMSG = String(tw.error.data);&#xD;
tw.local.errorVIS = "EDITABLE";</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:boundaryEvent cancelActivity="true" attachedToRef="2025.e16159fa-302a-4b8a-b108-2919f0ef72b2" parallelMultiple="false" name="Error" id="2025.b2425ca1-bb30-4471-8d35-090867b2a0a3">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="730" y="153" width="24" height="24" />
                                    
                                    
                                    <ns3:default>2027.ef757f52-d8cf-456c-8d98-1ff426b95867</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.ef757f52-d8cf-456c-8d98-1ff426b95867</ns16:outgoing>
                                
                                
                                <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2025.7e199729-53be-4494-87db-31743b7977fe" />
                                
                                
                                <ns16:outputSet />
                                
                                
                                <ns16:errorEventDefinition>
                                    
                                    
                                    <ns16:extensionElements>
                                        
                                        
                                        <ns4:errorEventSettings>
                                            
                                            
                                            <ns4:catchAll>true</ns4:catchAll>
                                            
                                        
                                        </ns4:errorEventSettings>
                                        
                                    
                                    </ns16:extensionElements>
                                    
                                
                                </ns16:errorEventDefinition>
                                
                            
                            </ns16:boundaryEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.b2425ca1-bb30-4471-8d35-090867b2a0a3" targetRef="2025.5f644ad3-b1d2-4142-886a-596de3dc6a14" name="To Handling Error" id="2027.ef757f52-d8cf-456c-8d98-1ff426b95867">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.5f644ad3-b1d2-4142-886a-596de3dc6a14" targetRef="2025.2acbb661-0ba9-4950-a40d-5275b121f9a3" name="To Waiting CBE Approval" id="2027.1ead6deb-ca24-43e4-8e58-67d5629fdb2d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>leftCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.c2adc175-f2a9-4c73-8d02-91da551b24f5" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorVIS" id="2056.fd22afec-d6fc-443a-8003-f4b726264301" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="alertMessage" id="2056.52ec9d02-f232-4dd1-8ef2-1b7cfaa7e22c" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" name="error" id="2056.33ff724e-9558-4835-80ee-02cc61b9b468" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="true" name="invalidTabs" id="2056.3d6340db-0cbc-403d-841c-d30b1612b75d" />
                            
                            
                            <ns3:htmlHeaderTag id="762bed24-ca9f-4470-8f06-077556a5ef21">
                                
                                
                                <ns3:tagName>viewport</ns3:tagName>
                                
                                
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                
                                
                                <ns3:enabled>true</ns3:enabled>
                                
                            
                            </ns3:htmlHeaderTag>
                            
                        
                        </ns3:userTaskImplementation>
                        
                        
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.8ce8b34e-54bb-4623-a4c9-ab892efacac6" epvProcessLinkId="d3064db2-6092-4f70-8dbd-1c9c8241823e" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.a82c0a5a-df05-4f98-8b44-6ff5304c9b3e" epvProcessLinkId="2d79f5bd-d5d5-493d-8448-f159c5442689" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.02818ba4-c183-4dfb-8924-18e2d9a515dd" epvProcessLinkId="27088ef6-296e-4256-82f5-11d9cff3e408" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.03a5f1e9-21d6-4ac6-9894-82edc980e087" />
                        
                        
                        <ns16:dataInput name="attachment" itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" id="2055.7a6533b6-bbb9-4f62-aa08-d41a34aa3927" />
                        
                        
                        <ns16:dataInput name="treasuryComments" itemSubjectRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1" isCollection="true" id="2055.3b198fb8-2cbb-43a3-8d9a-6f2233fce87a" />
                        
                        
                        <ns16:dataInput name="ECMproperties" itemSubjectRef="itm.12.ae22157e-8e19-4a60-a294-712ff5dc96df" isCollection="false" id="2055.d9261eb9-6067-4b77-b05b-249696e3ac60" />
                        
                        
                        <ns16:dataOutput name="idcRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.19153306-7e7e-4ce0-8d73-b24ade667180" />
                        
                        
                        <ns16:dataOutput name="attachment" itemSubjectRef="itm.12.66faea5a-d7f8-4aa5-9764-d2cd1e72285f" isCollection="true" id="2055.690326c2-3e2a-48f2-8091-935656717e2b" />
                        
                        
                        <ns16:dataOutput name="treasuryComments" itemSubjectRef="itm.12.da5cf277-3788-466d-9108-e44438cbc7b1" isCollection="true" id="2055.dbd6de09-c2b9-41d6-99dd-5de23cc5a7c1" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                
                </ns16:globalUserTask>
                
            
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.18c25db0-e35f-4115-b998-569a7ef0326c</processLinkId>
            <processId>1.f2c8f018-67e0-416a-acb3-8af30664cba5</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.017f41e3-4a25-4dfa-b598-af7ed48d49b3</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.7b7f224c-1d85-4be7-bc13-b1913451588d</toProcessItemId>
            <guid>065baac5-bb7b-4991-9763-6b6c9e493390</guid>
            <versionId>f9aa3435-284a-40fa-bdb3-8e28bc54ef06</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.017f41e3-4a25-4dfa-b598-af7ed48d49b3</fromProcessItemId>
            <toProcessItemId>2025.7b7f224c-1d85-4be7-bc13-b1913451588d</toProcessItemId>
        </link>
    </process>
</teamworks>

