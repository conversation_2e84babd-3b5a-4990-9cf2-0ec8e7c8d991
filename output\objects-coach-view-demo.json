{"typeName": "Coach <PERSON>", "count": 3, "objects": [{"id": "64.0fa21995-2169-498c-ba2e-ea66c3dc5616", "versionId": "b7059166-d296-437a-9e30-5607ea8f75c7", "name": "Contract Creation", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"displayName": "Contract Creation", "description": "Coach view for creating new contracts", "loadJsFunction": "if(this.context.binding.get(\"value\").get(\"haveInterest\") == false){\n\tthis.ui.get(\"contract_Interest_Details\").setVisible(false,true);\n}else if (this.context.binding.get(\"value\").get(\"haveInterest\") == true){\n\tthis.ui.get(\"contract_Interest_Details\").setVisible(true,true);\n}\n", "bindingType": "IDCContract", "configOptions": ["contractcashCollateralsOption", "advicesOption", "contractLimitsTrackingOption", "contractInterestDetailsOption", "contractTransactionDetailsOption", "concatString", "accountList", "requestCurrency", "isGLFound", "isSuccessful", "customerCIF", "adviceCodeList", "selectedBIC", "accounteeCIF", "caseCIF", "exRate", "amtPayableByNBE", "contractAmount", "accountIndex", "<PERSON><PERSON><PERSON><PERSON>", "stage", "facilityCIF", "errorMessage", "selectedCIF", "<PERSON><PERSON><PERSON><PERSON>", "interestIsVisible", "alertMessage", "errorVis", "interestVis"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.visabilityController = function  (id) {\n\tvar temp;\n\tswitch (id) {\n\t\tcase \"1\":\n\t\t\ttemp = this.context.binding.get(\"value\").get(\"contractcashCollateralsOption\").get(\"listAllItems\");\n\t\t\tif (temp.length > 0) {\n\t\t\t\tthis.ui.get(\"contract_cash_collaterals\").setVisible(true, true);\n\t\t\t} else {\n\t\t\t\tthis.ui.get(\"contract_cash_collaterals\").setVisible(false, true);\n\t\t\t}\n\t\t\tbreak;\n\t\tcase \"2\":\n\t\t\ttemp = this.context.binding.get(\"value\").get(\"advicesOption\").get(\"listAllItems\");\n\t\t\tif (temp.length > 0) {\n\t\t\t\tthis.ui.get(\"contract_advices\").setVisible(true, true);\n\t\t\t} else {\n\t\t\t\tthis.ui.get(\"contract_advices\").setVisible(false, true);\n\t\t\t}\n\t\t\tbreak;\n\t\tcase \"3\":\n\t\t\ttemp = this.context.binding.get(\"value\").get(\"contractLimitsTrackingOption\").get(\"listAllItems\");\n\t\t\tif (temp.length > 0) {\n\t\t\t\tthis.ui.get(\"contract_limits_tracking\").setVisible(true, true);\n\t\t\t} else {\n\t\t\t\tthis.ui.get(\"contract_limits_tracking\").setVisible(false, true);\n\t\t\t}\n\t\t\tbreak;\n\t\tcase \"4\":\n\t\t\ttemp = this.context.binding.get(\"value\").get(\"contractInterestDetailsOption\").get(\"listAllItems\");\n\t\t\tif (temp.length > 0) {\n\t\t\t\tthis.ui.get(\"contract_Interest_Details\").setVisible(true, true);\n\t\t\t} else {\n\t\t\t\tthis.ui.get(\"contract_Interest_Details\").setVisible(false, true);\n\t\t\t}\n\t\t\tbreak;\n\t\tcase \"5\":\n\t\t\ttemp = this.context.binding.get(\"value\").get(\"contractTransactionDetailsOption\").get(\"listAllItems\");\n\t\t\tif (temp.length > 0) {\n\t\t\t\tthis.ui.get(\"contract_transaction_details\").setVisible(true, true);\n\t\t\t} else {\n\t\t\t\tthis.ui.get(\"contract_transaction_details\").setVisible(false, true);\n\t\t\t}\n\t\t\tbreak;\n\t\tdefault:\n\t\t\tbreak;\n\t}\n}"}]}, "hasDetails": true}, {"id": "64.0d7634e8-859f-4f60-b8ce-a0b32d5a1374", "versionId": "d40f4439-fb45-474b-acf2-8ed3d88c0a8f", "name": "Withdrawal Request Trade FO", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"displayName": "Withdrawal Request Trade FO", "description": "Coach view for withdrawal requests in trade finance", "loadJsFunction": null, "bindingType": "WithdrawalRequest", "configOptions": ["requestAmount", "requestCurrency", "customerDetails", "accountDetails"], "inlineScripts": []}, "hasDetails": true}, {"id": "64.46690ca1-5521-4e5c-bc9e-ecb22d75ca97", "versionId": "436ce08d-104e-4e18-bb0a-7ddeeab82936", "name": "Error Message", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"displayName": "Error Message", "description": "Coach view for displaying error messages", "loadJsFunction": "this.ui.get(\"errorText\").setText(this.context.binding.get(\"value\").get(\"errorMessage\"));\nthis.ui.get(\"errorPanel\").setVisible(true, true);", "bindingType": "ErrorInfo", "configOptions": ["errorMessage", "errorType", "showDetails"], "inlineScripts": [{"name": "<PERSON><PERSON><PERSON>", "scriptType": "JS", "scriptBlock": "this.handleError = function(errorCode, errorMessage) {\n\tconsole.log('Error occurred: ' + errorCode + ' - ' + errorMessage);\n\tthis.context.binding.get('value').get('errorMessage').setValue(errorMessage);\n\tthis.context.binding.get('value').get('errorType').setValue(errorCode);\n\tthis.ui.get('errorPanel').setVisible(true, true);\n};\n\nthis.clearError = function() {\n\tthis.context.binding.get('value').get('errorMessage').setValue('');\n\tthis.ui.get('errorPanel').setVisible(false, true);\n};"}]}, "hasDetails": true}]}