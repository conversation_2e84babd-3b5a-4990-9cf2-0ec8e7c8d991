<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.45e2a149-1d4a-4946-ad59-f81f1424f100" name="Create Folder FileNet">
        <lastModified>1689676308112</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.cab64202-9dfe-4f3f-ab40-f38f2663f588</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>dfc30f28-05b8-4477-a00a-91380beb39a5</guid>
        <versionId>d8df3b6e-051c-495b-8085-957e0914d5e6</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:2e93acfacc1269a3:3c8702fd:18968452cb5:3998" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["8c8e02ac-6a36-4da1-8c41-fe96b818adaa"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":180,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"02166c0a-6ee4-4ada-b175-f442978dcd4e"},{"incoming":["35339af2-0d88-4f14-833c-d488836beb5f","ffffd519-c33a-4013-b23e-dfc6ed46acbd"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":940,"y":180,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-619b"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"df348c6c-74cf-4f31-b35f-76711625e029"},{"outgoing":["f57bd9c2-4682-4528-9c75-992d3f9965f4"],"incoming":["14cfe8b1-1c86-4b33-bebc-83e6616b2c82","ec8efb66-b66c-4eb8-93fa-47e066ffeb2f"],"matchAllSearchCriteria":true,"extensionElements":{"nodeVisualInfo":[{"width":95,"x":504,"y":157,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[],"activityType":["ContentTask"]},"operationRef":"FOLDER_OP_CREATE_FOLDER","implementation":"##WebService","serverName":"FileNet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TContentTask","startQuantity":1,"name":"Create Folder","dataInputAssociation":[{"targetRef":"OBJECT_TYPE_ID","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Folder\""]}}]},{"targetRef":"NAME","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.folderName"]}}]},{"targetRef":"PARENT_FOLDER_ID","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.parentFolderID"]}}]}],"isForCompensation":false,"useMappedVariable":true,"completionQuantity":1,"id":"9e5c1f7e-d13a-40a5-a05b-874732aeec12","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.folderID"]}}],"sourceRef":["FOLDER_ID"]}],"orderOverride":false},{"startQuantity":1,"outgoing":["35339af2-0d88-4f14-833c-d488836beb5f"],"incoming":["f57bd9c2-4682-4528-9c75-992d3f9965f4"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":737,"y":157,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Get ID","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"f683e75f-9457-4a2d-aadd-4d1500d3d561","scriptFormat":"text\/x-javascript","script":{"content":["var fID = tw.local.folderID.toString();\r\nfID = fID.substring(4);\r\ntw.local.folderID = \"{\" + fID + \"}\" ;"]}},{"targetRef":"cab64202-9dfe-4f3f-ab40-f38f2663f588","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Parent Folder Exist","declaredType":"sequenceFlow","id":"8c8e02ac-6a36-4da1-8c41-fe96b818adaa","sourceRef":"02166c0a-6ee4-4ada-b175-f442978dcd4e"},{"targetRef":"f683e75f-9457-4a2d-aadd-4d1500d3d561","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get ID","declaredType":"sequenceFlow","id":"f57bd9c2-4682-4528-9c75-992d3f9965f4","sourceRef":"9e5c1f7e-d13a-40a5-a05b-874732aeec12"},{"targetRef":"df348c6c-74cf-4f31-b35f-76711625e029","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"35339af2-0d88-4f14-833c-d488836beb5f","sourceRef":"f683e75f-9457-4a2d-aadd-4d1500d3d561"},{"outgoing":["920234c4-c533-418e-a18e-994b230cc914"],"incoming":["2e8d986e-bd3a-4179-ac04-8cbe4616eac8"],"matchAllSearchCriteria":true,"extensionElements":{"nodeVisualInfo":[{"width":95,"x":503,"y":279,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[],"activityType":["ContentTask"]},"operationRef":"FOLDER_OP_GET_FOLDER_BY_PATH","implementation":"##WebService","serverName":"FileNet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TContentTask","startQuantity":1,"name":"Get Folder Path","dataInputAssociation":[{"targetRef":"PATH","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.local.parentFolderPath+\"\/\"+tw.local.folderName"]}}]}],"isForCompensation":false,"useMappedVariable":true,"completionQuantity":1,"id":"05af19e1-3e43-46f0-a8f2-e66c6df77696","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183","declaredType":"TFormalExpression","content":["tw.local.folder"]}}],"sourceRef":["FOLDER"]}],"orderOverride":false},{"itemSubjectRef":"itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183","name":"folder","isCollection":false,"declaredType":"dataObject","id":"2056.462e5358-f5c2-49c5-8148-3df2ef46c419"},{"startQuantity":1,"outgoing":["ffffd519-c33a-4013-b23e-dfc6ed46acbd"],"incoming":["920234c4-c533-418e-a18e-994b230cc914"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":729,"y":279,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Get Folder ID","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"182d9888-9fe7-4e78-9c63-fb2eefa21153","scriptFormat":"text\/x-javascript","script":{"content":["var fID = tw.local.folder.objectId.toString();\r\nfID = fID.substring(4);\r\ntw.local.folderID = \"{\" + fID + \"}\" ;"]}},{"targetRef":"182d9888-9fe7-4e78-9c63-fb2eefa21153","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get Folder ID","declaredType":"sequenceFlow","id":"920234c4-c533-418e-a18e-994b230cc914","sourceRef":"05af19e1-3e43-46f0-a8f2-e66c6df77696"},{"targetRef":"df348c6c-74cf-4f31-b35f-76711625e029","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"ffffd519-c33a-4013-b23e-dfc6ed46acbd","sourceRef":"182d9888-9fe7-4e78-9c63-fb2eefa21153"},{"incoming":["077dd0f2-40aa-4ade-8f65-c44d43c4f5e4"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"e1449e6f-e68e-4c30-99ac-d40450f14c1d","otherAttributes":{"eventImplId":"0dadf3e3-cca2-4a36-8b95-15d5ddf44c75"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":661,"y":374,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Error","declaredType":"endEvent","id":"3bf58e15-4c4a-4fb7-8932-175f3b896684"},{"outgoing":["292bb32f-1d08-4f59-be1d-1520ae62463e"],"incoming":["29549467-7feb-453f-81da-41ef558d9391"],"matchAllSearchCriteria":true,"extensionElements":{"nodeVisualInfo":[{"width":95,"x":238,"y":157,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["ContentTask"]},"operationRef":"FOLDER_OP_GET_FOLDER_BY_PATH","implementation":"##WebService","serverName":"FileNet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TContentTask","startQuantity":1,"name":"Get Parent Folder By Path","dataInputAssociation":[{"targetRef":"PATH","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentFolderPath"]}}]}],"isForCompensation":false,"useMappedVariable":true,"completionQuantity":1,"id":"9afad728-e8d2-4c46-9b14-b621a9a05ba5","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183","declaredType":"TFormalExpression","content":["tw.local.parent"]}}],"sourceRef":["FOLDER"]}],"orderOverride":false},{"targetRef":"1a3b1c80-e970-45d7-8190-e6c437866e67","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set Parent Folder ID","declaredType":"sequenceFlow","id":"292bb32f-1d08-4f59-be1d-1520ae62463e","sourceRef":"9afad728-e8d2-4c46-9b14-b621a9a05ba5"},{"itemSubjectRef":"itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183","name":"parent","isCollection":false,"declaredType":"dataObject","id":"2056.b896c83b-9dda-4345-865a-cb76e412afda"},{"parallelMultiple":false,"outgoing":["2e8d986e-bd3a-4179-ac04-8cbe4616eac8"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"ea610645-7055-4298-9b8e-0c0713c28549"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"33475a17-c875-492e-bee9-af52100a17d3","otherAttributes":{"eventImplId":"800f7367-f5b4-4eb6-8171-1be5da45ce6d"}}],"attachedToRef":"9e5c1f7e-d13a-40a5-a05b-874732aeec12","extensionElements":{"nodeVisualInfo":[{"width":24,"x":539,"y":215,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"f185f582-43ce-4acb-ad93-0a5a81a9154d","outputSet":{}},{"targetRef":"05af19e1-3e43-46f0-a8f2-e66c6df77696","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get Folder Path","declaredType":"sequenceFlow","id":"2e8d986e-bd3a-4179-ac04-8cbe4616eac8","sourceRef":"f185f582-43ce-4acb-ad93-0a5a81a9154d"},{"parallelMultiple":false,"outgoing":["077dd0f2-40aa-4ade-8f65-c44d43c4f5e4"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"3924d6e6-bd15-4d6f-9121-eb2f7146a4cb"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"ccb2f17c-0e91-430d-b11c-e51531b419b8","otherAttributes":{"eventImplId":"738a5e9b-9337-43d6-8f6b-583f20b18a99"}}],"attachedToRef":"05af19e1-3e43-46f0-a8f2-e66c6df77696","extensionElements":{"nodeVisualInfo":[{"width":24,"x":538,"y":337,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"5bb9c0ab-88e6-4458-80f3-5cb45145454b","outputSet":{}},{"targetRef":"3bf58e15-4c4a-4fb7-8932-175f3b896684","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Error","declaredType":"sequenceFlow","id":"077dd0f2-40aa-4ade-8f65-c44d43c4f5e4","sourceRef":"5bb9c0ab-88e6-4458-80f3-5cb45145454b"},{"outgoing":["29549467-7feb-453f-81da-41ef558d9391","14cfe8b1-1c86-4b33-bebc-83e6616b2c82"],"incoming":["8c8e02ac-6a36-4da1-8c41-fe96b818adaa"],"default":"29549467-7feb-453f-81da-41ef558d9391","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":123,"y":176,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Parent Folder Exist","declaredType":"exclusiveGateway","id":"cab64202-9dfe-4f3f-ab40-f38f2663f588"},{"targetRef":"9afad728-e8d2-4c46-9b14-b621a9a05ba5","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.parentFolderID\t  ==\t  "]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get Parent Folder By Path","declaredType":"sequenceFlow","id":"29549467-7feb-453f-81da-41ef558d9391","sourceRef":"cab64202-9dfe-4f3f-ab40-f38f2663f588"},{"targetRef":"9e5c1f7e-d13a-40a5-a05b-874732aeec12","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.parentFolderID\t  !=\t  null"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Script Task","declaredType":"sequenceFlow","id":"14cfe8b1-1c86-4b33-bebc-83e6616b2c82","sourceRef":"cab64202-9dfe-4f3f-ab40-f38f2663f588"},{"startQuantity":1,"outgoing":["ec8efb66-b66c-4eb8-93fa-47e066ffeb2f"],"incoming":["292bb32f-1d08-4f59-be1d-1520ae62463e"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":364,"y":157,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Parent Folder ID","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"1a3b1c80-e970-45d7-8190-e6c437866e67","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.parentFolderID= tw.local.parent.objectId;"]}},{"targetRef":"9e5c1f7e-d13a-40a5-a05b-874732aeec12","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Create Folder","declaredType":"sequenceFlow","id":"ec8efb66-b66c-4eb8-93fa-47e066ffeb2f","sourceRef":"1a3b1c80-e970-45d7-8190-e6c437866e67"}],"laneSet":[{"id":"7a6a31ec-c956-48af-9aef-2ff56a116d5f","lane":[{"flowNodeRef":["02166c0a-6ee4-4ada-b175-f442978dcd4e","df348c6c-74cf-4f31-b35f-76711625e029","9e5c1f7e-d13a-40a5-a05b-874732aeec12","f683e75f-9457-4a2d-aadd-4d1500d3d561","3bf58e15-4c4a-4fb7-8932-175f3b896684","05af19e1-3e43-46f0-a8f2-e66c6df77696","182d9888-9fe7-4e78-9c63-fb2eefa21153","9afad728-e8d2-4c46-9b14-b621a9a05ba5","f185f582-43ce-4acb-ad93-0a5a81a9154d","5bb9c0ab-88e6-4458-80f3-5cb45145454b","cab64202-9dfe-4f3f-ab40-f38f2663f588","1a3b1c80-e970-45d7-8190-e6c437866e67"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"02cbf30d-7790-46fa-91e2-d18e6737fe76","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Create Folder FileNet","declaredType":"process","id":"1.45e2a149-1d4a-4946-ad59-f81f1424f100","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"folderID","isCollection":false,"id":"2055.21901c1e-19de-4246-994d-52da5502c23f"}],"inputSet":[{"dataInputRefs":["2055.d04abbef-eb9a-436a-8a7f-49652104b0e3","2055.5b1f380a-9134-4d96-a60a-cdb675b76b45","2055.fc94a5c7-72ab-492e-a419-87a8f509c429"]}],"outputSet":[{"dataOutputRefs":["2055.21901c1e-19de-4246-994d-52da5502c23f"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentFolderPath","isCollection":false,"id":"2055.d04abbef-eb9a-436a-8a7f-49652104b0e3"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"folderName","isCollection":false,"id":"2055.5b1f380a-9134-4d96-a60a-cdb675b76b45"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":""}]},"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"parentFolderID","isCollection":false,"id":"2055.fc94a5c7-72ab-492e-a419-87a8f509c429"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="parentFolderPath">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d04abbef-eb9a-436a-8a7f-49652104b0e3</processParameterId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>fd0bcfdd-e6b6-4002-b4d2-8d62dd16ae92</guid>
            <versionId>ad30fb26-abc9-44b2-9936-3f9ac634873e</versionId>
        </processParameter>
        <processParameter name="folderName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5b1f380a-9134-4d96-a60a-cdb675b76b45</processParameterId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>5ac8cff3-696f-4536-9ac7-d42ba46fee88</guid>
            <versionId>e5d0f02a-aeec-45ab-b32e-cabe9f6e56f2</versionId>
        </processParameter>
        <processParameter name="parentFolderID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.fc94a5c7-72ab-492e-a419-87a8f509c429</processParameterId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>08b50ddc-ff8f-48f6-8fb1-4b168a80fbd5</guid>
            <versionId>27c3afac-8021-4b8f-aa0b-5c4060acd920</versionId>
        </processParameter>
        <processParameter name="folderID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.21901c1e-19de-4246-994d-52da5502c23f</processParameterId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>6bd6d17d-5182-4db4-ba62-b652c76dc087</guid>
            <versionId>874f3a31-8fee-4cef-a3e7-41a38652f318</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d53ec69f-c669-4971-9f48-75a6a4a071b0</processParameterId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6</classId>
            <seq>56</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>9993a075-c59b-495b-8fee-b7a78e411630</guid>
            <versionId>68f3862f-abb9-4b8a-be1e-6b1805ef7613</versionId>
        </processParameter>
        <processParameter name="ECMError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.ab29c98c-3b7b-4b7f-8d8c-67b60537750a</processParameterId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.bfb48b9b-8247-49fb-8f78-82b738b817a6</classId>
            <seq>110</seq>
            <hasDefault>false</hasDefault>
            <defaultValue>ECMError</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>fbc3efce-dc5e-427d-a15b-9f6de634136c</guid>
            <versionId>6f004ea5-da7a-4a83-9957-f2fccd67ab45</versionId>
        </processParameter>
        <processParameter name="ECMError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.764195e7-cf45-4691-980c-f338e93a00c0</processParameterId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.bfb48b9b-8247-49fb-8f78-82b738b817a6</classId>
            <seq>111</seq>
            <hasDefault>false</hasDefault>
            <defaultValue>ECMError</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>066f921f-e95d-473f-af3e-36bcbeb53088</guid>
            <versionId>e97b7088-621e-4868-8b8f-e4e9faa85c6f</versionId>
        </processParameter>
        <processParameter name="ECMError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.39485e33-a88b-4330-a83d-d8cce85a27e8</processParameterId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.bfb48b9b-8247-49fb-8f78-82b738b817a6</classId>
            <seq>112</seq>
            <hasDefault>false</hasDefault>
            <defaultValue>ECMError</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>88bc7b5a-28e7-4653-b701-8957b3aef6bb</guid>
            <versionId>2d0b32df-4def-4855-a953-b90d1f6da3eb</versionId>
        </processParameter>
        <processParameter name="ECMError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c3b2b127-1456-4624-ab90-6e96e812bc84</processParameterId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.bfb48b9b-8247-49fb-8f78-82b738b817a6</classId>
            <seq>113</seq>
            <hasDefault>false</hasDefault>
            <defaultValue>ECMError</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>1b82096a-6637-4787-993c-9fd05b91297e</guid>
            <versionId>a4af9b82-23a1-402e-b737-a3a4d8d0367f</versionId>
        </processParameter>
        <processVariable name="folder">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.462e5358-f5c2-49c5-8148-3df2ef46c419</processVariableId>
            <description isNull="true" />
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.240930df-e9da-40a6-a4e8-4b41b42bb183</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d590376d-b7ad-4531-b4d8-98bfa0bacd6c</guid>
            <versionId>cd9be952-1404-4f9d-955b-74c90564b367</versionId>
        </processVariable>
        <processVariable name="parent">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b896c83b-9dda-4345-865a-cb76e412afda</processVariableId>
            <description isNull="true" />
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>21bb9603-df80-4247-9339-8cf97555cb2a/12.240930df-e9da-40a6-a4e8-4b41b42bb183</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>253da4b2-808a-49d1-adad-5eedd8b5f2c6</guid>
            <versionId>3fd9d922-8beb-4efd-ae8b-951b3f4d136a</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.05af19e1-3e43-46f0-a8f2-e66c6df77696</processItemId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <name>Get Folder Path</name>
            <tWComponentName>ECMConnector</tWComponentName>
            <tWComponentId>3030.622aed56-1581-4c81-9b15-13d17b4825c4</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.3bf58e15-4c4a-4fb7-8932-175f3b896684</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-619e</guid>
            <versionId>3c36ff34-ea0f-465f-97f0-f3b2023f87e9</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.b846045c-2963-4ea7-98b0-5a5ae8a700a6</processItemPrePostId>
                <processItemId>2025.05af19e1-3e43-46f0-a8f2-e66c6df77696</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>4364dfa7-2adc-45d3-ac21-c8d09eb0d585</guid>
                <versionId>0570e0e2-78b5-42ab-b7ff-4255f619f2ff</versionId>
            </processPrePosts>
            <layoutData x="503" y="279">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6196</errorHandlerItem>
                <errorHandlerItemId>2025.3bf58e15-4c4a-4fb7-8932-175f3b896684</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <ecmConnectorId>3030.622aed56-1581-4c81-9b15-13d17b4825c4</ecmConnectorId>
                <definition>&lt;config type="com.lombardisoftware.client.persistence.ECMConnectorConfiguration"&gt;&#xD;
  &lt;inputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;path&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.parentFolderPath+"/"+tw.local.folderName&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;serverName&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/inputParameters&gt;&#xD;
  &lt;outputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;folder&lt;/name&gt;&#xD;
      &lt;type&gt;ECMFolder&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.folder&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/outputParameters&gt;&#xD;
  &lt;matchAllSearchCriteria&gt;true&lt;/matchAllSearchCriteria&gt;&#xD;
  &lt;searchParameters /&gt;&#xD;
  &lt;selectParameters /&gt;&#xD;
  &lt;orderOverride&gt;false&lt;/orderOverride&gt;&#xD;
  &lt;orderOverrideValue&gt;&lt;/orderOverrideValue&gt;&#xD;
  &lt;operationType&gt;FOLDER_OP_GET_FOLDER_BY_PATH&lt;/operationType&gt;&#xD;
  &lt;server&gt;FileNet&lt;/server&gt;&#xD;
  &lt;objectTypeSelection&gt;DOCUMENT&lt;/objectTypeSelection&gt;&#xD;
  &lt;useMappedVariable&gt;true&lt;/useMappedVariable&gt;&#xD;
  &lt;faultParameterId&gt;2055.764195e7-cf45-4691-980c-f338e93a00c0&lt;/faultParameterId&gt;&#xD;
&lt;/config&gt;</definition>
                <guid>e2bb1a13-3067-471a-a553-f2a2f192630c</guid>
                <versionId>48f07747-2f41-4887-bffd-fdc342294a10</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.df348c6c-74cf-4f31-b35f-76711625e029</processItemId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.d75dfcf9-d8be-4f2e-bb00-f51f7dd7cd25</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-619b</guid>
            <versionId>6301ab96-9283-4ecd-b89d-8793d93c0331</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="940" y="180">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.d75dfcf9-d8be-4f2e-bb00-f51f7dd7cd25</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>9a5c1dbf-aefd-450d-978b-46d63d9db389</guid>
                <versionId>4ca6aad8-b40d-4dac-9542-7ebfe1dd1fb0</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.182d9888-9fe7-4e78-9c63-fb2eefa21153</processItemId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <name>Get Folder ID</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.af383c89-84cb-4599-a143-d2fe4386f56a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-619c</guid>
            <versionId>6da1fe96-3324-4152-9fab-1519b7849e0a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="729" y="279">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.af383c89-84cb-4599-a143-d2fe4386f56a</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>var fID = tw.local.folder.objectId.toString();&#xD;
fID = fID.substring(4);&#xD;
tw.local.folderID = "{" + fID + "}" ;</script>
                <isRule>false</isRule>
                <guid>b6926395-ae93-4844-aaf1-298b23972a20</guid>
                <versionId>651ded7e-d041-4a1f-9d54-dfbd476c1d30</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.9afad728-e8d2-4c46-9b14-b621a9a05ba5</processItemId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <name>Get Parent Folder By Path</name>
            <tWComponentName>ECMConnector</tWComponentName>
            <tWComponentId>3030.d274f14d-66ee-4c5c-a21c-bdeb044b3914</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-619a</guid>
            <versionId>6e4bbe5a-e504-4095-aa5b-120c0aa49428</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="238" y="157">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <ecmConnectorId>3030.d274f14d-66ee-4c5c-a21c-bdeb044b3914</ecmConnectorId>
                <definition>&lt;config type="com.lombardisoftware.client.persistence.ECMConnectorConfiguration"&gt;&#xD;
  &lt;inputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;path&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.parentFolderPath&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;serverName&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/inputParameters&gt;&#xD;
  &lt;outputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;folder&lt;/name&gt;&#xD;
      &lt;type&gt;ECMFolder&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.parent&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/outputParameters&gt;&#xD;
  &lt;matchAllSearchCriteria&gt;true&lt;/matchAllSearchCriteria&gt;&#xD;
  &lt;searchParameters /&gt;&#xD;
  &lt;selectParameters /&gt;&#xD;
  &lt;orderOverride&gt;false&lt;/orderOverride&gt;&#xD;
  &lt;orderOverrideValue&gt;&lt;/orderOverrideValue&gt;&#xD;
  &lt;operationType&gt;FOLDER_OP_GET_FOLDER_BY_PATH&lt;/operationType&gt;&#xD;
  &lt;server&gt;FileNet&lt;/server&gt;&#xD;
  &lt;objectTypeSelection&gt;DOCUMENT&lt;/objectTypeSelection&gt;&#xD;
  &lt;useMappedVariable&gt;true&lt;/useMappedVariable&gt;&#xD;
  &lt;faultParameterId&gt;2055.39485e33-a88b-4330-a83d-d8cce85a27e8&lt;/faultParameterId&gt;&#xD;
&lt;/config&gt;</definition>
                <guid>b3d2807f-c9d0-4925-b067-dfc6266b2849</guid>
                <versionId>01c34325-4e6f-4737-896e-b6542503dbe4</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3bf58e15-4c4a-4fb7-8932-175f3b896684</processItemId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <name>Error</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.88b30365-53c5-474a-b48f-5ff303aa234e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6196</guid>
            <versionId>7e3f26e2-cae4-4126-8453-d287c3e513f2</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="661" y="374">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.88b30365-53c5-474a-b48f-5ff303aa234e</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>b88c9630-4a59-408f-8c23-6facf25641ce</guid>
                <versionId>4100b583-15d8-4d6f-b4f4-64f53a0fd165</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.61ff84d1-545a-4337-b352-ef8ff73c217d</parameterMappingId>
                    <processParameterId>2055.d53ec69f-c669-4971-9f48-75a6a4a071b0</processParameterId>
                    <parameterMappingParentId>3007.88b30365-53c5-474a-b48f-5ff303aa234e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>5b5acd56-168c-4bfd-b459-3a299d0f2487</guid>
                    <versionId>a1743304-d7bf-4370-ad59-c44a7dccbc97</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f683e75f-9457-4a2d-aadd-4d1500d3d561</processItemId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <name>Get ID</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.e511da36-d8c4-4601-a974-bcc9535f47be</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6197</guid>
            <versionId>994559ce-f3a6-4ede-8101-97d500cf21ea</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="737" y="157">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.e511da36-d8c4-4601-a974-bcc9535f47be</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>var fID = tw.local.folderID.toString();&#xD;
fID = fID.substring(4);&#xD;
tw.local.folderID = "{" + fID + "}" ;</script>
                <isRule>false</isRule>
                <guid>ead19904-c511-4c40-a452-3e18a0ab342d</guid>
                <versionId>7931f7d2-ee41-4aec-98e0-dd750db442cc</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.9e5c1f7e-d13a-40a5-a05b-874732aeec12</processItemId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <name>Create Folder</name>
            <tWComponentName>ECMConnector</tWComponentName>
            <tWComponentId>3030.16523e13-0843-4603-8687-eaf282255675</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.05af19e1-3e43-46f0-a8f2-e66c6df77696</errorHandlerItemId>
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-619d</guid>
            <versionId>cbe5353a-ad45-4961-a55f-4cc0619d1d11</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.4c325ed8-35d1-4227-8f0e-e1c6f9481568</processItemPrePostId>
                <processItemId>2025.9e5c1f7e-d13a-40a5-a05b-874732aeec12</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>3cea553c-f050-46aa-84b2-1411df56b05c</guid>
                <versionId>ee1f520c-8704-448d-8a9c-818bebeb1d86</versionId>
            </processPrePosts>
            <layoutData x="504" y="157">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-619e</errorHandlerItem>
                <errorHandlerItemId>2025.05af19e1-3e43-46f0-a8f2-e66c6df77696</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <ecmConnectorId>3030.16523e13-0843-4603-8687-eaf282255675</ecmConnectorId>
                <definition>&lt;config type="com.lombardisoftware.client.persistence.ECMConnectorConfiguration"&gt;&#xD;
  &lt;inputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;objectTypeId&lt;/name&gt;&#xD;
      &lt;type&gt;ECMID&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;"Folder"&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;parentFolderId&lt;/name&gt;&#xD;
      &lt;type&gt;ECMID&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.parentFolderID&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;name&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.folderName&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;properties&lt;/name&gt;&#xD;
      &lt;type&gt;ECMProperty&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;true&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;serverName&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/inputParameters&gt;&#xD;
  &lt;outputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;folderId&lt;/name&gt;&#xD;
      &lt;type&gt;ECMID&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.folderID&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/outputParameters&gt;&#xD;
  &lt;matchAllSearchCriteria&gt;true&lt;/matchAllSearchCriteria&gt;&#xD;
  &lt;searchParameters /&gt;&#xD;
  &lt;selectParameters /&gt;&#xD;
  &lt;orderOverride&gt;false&lt;/orderOverride&gt;&#xD;
  &lt;orderOverrideValue&gt;&lt;/orderOverrideValue&gt;&#xD;
  &lt;operationType&gt;FOLDER_OP_CREATE_FOLDER&lt;/operationType&gt;&#xD;
  &lt;server&gt;FileNet&lt;/server&gt;&#xD;
  &lt;objectTypeSelection&gt;DOCUMENT&lt;/objectTypeSelection&gt;&#xD;
  &lt;useMappedVariable&gt;true&lt;/useMappedVariable&gt;&#xD;
  &lt;faultParameterId&gt;2055.c3b2b127-1456-4624-ab90-6e96e812bc84&lt;/faultParameterId&gt;&#xD;
&lt;/config&gt;</definition>
                <guid>e6dfb5ab-47ed-4cd2-980a-e30266865107</guid>
                <versionId>3e1b7476-fb09-41ed-9081-4ae6bf89984e</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1a3b1c80-e970-45d7-8190-e6c437866e67</processItemId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <name>Set Parent Folder ID</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.12ec9397-4b06-4f5e-98d2-93f759f2cf8c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6199</guid>
            <versionId>e1f5ff0f-9f54-4d9b-bacc-eec121eccaf6</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="364" y="157">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.12ec9397-4b06-4f5e-98d2-93f759f2cf8c</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.parentFolderID= tw.local.parent.objectId;</script>
                <isRule>false</isRule>
                <guid>b05a1247-470f-4863-815e-7b9f0a97fb42</guid>
                <versionId>3bcf4e7f-5bb5-4883-a9b7-d7a3f5520345</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.cab64202-9dfe-4f3f-ab40-f38f2663f588</processItemId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <name>Parent Folder Exist</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.f71dbfd6-f8d4-4693-a2a3-26e1a8f5cbc1</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-6198</guid>
            <versionId>f160f411-506e-4a71-a0b4-b3f4fd73b0cd</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="123" y="176">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.f71dbfd6-f8d4-4693-a2a3-26e1a8f5cbc1</switchId>
                <guid>980c3794-3bd3-4df4-af65-d3f6012aaf44</guid>
                <versionId>b8c39d9f-dac9-4e6f-a899-e51f73827512</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.94983dfa-b9e8-45a0-84f5-556278e2c35d</switchConditionId>
                    <switchId>3013.f71dbfd6-f8d4-4693-a2a3-26e1a8f5cbc1</switchId>
                    <seq>1</seq>
                    <endStateId>guid:2e93acfacc1269a3:3c8702fd:18968452cb5:3997</endStateId>
                    <condition>tw.local.parentFolderID	  !=	  null</condition>
                    <guid>1b01e6df-25ae-4960-a948-16eb9a902b18</guid>
                    <versionId>a862c4e9-a029-4275-b381-c4931d79e12c</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.cab64202-9dfe-4f3f-ab40-f38f2663f588</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="180">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Create Folder FileNet" id="1.45e2a149-1d4a-4946-ad59-f81f1424f100" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="parentFolderPath" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.d04abbef-eb9a-436a-8a7f-49652104b0e3">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false" />
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="folderName" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.5b1f380a-9134-4d96-a60a-cdb675b76b45">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false" />
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="parentFolderID" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.fc94a5c7-72ab-492e-a419-87a8f509c429">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false" />
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="folderID" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.21901c1e-19de-4246-994d-52da5502c23f" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.d04abbef-eb9a-436a-8a7f-49652104b0e3</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.5b1f380a-9134-4d96-a60a-cdb675b76b45</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.fc94a5c7-72ab-492e-a419-87a8f509c429</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.21901c1e-19de-4246-994d-52da5502c23f</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="7a6a31ec-c956-48af-9aef-2ff56a116d5f">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="02cbf30d-7790-46fa-91e2-d18e6737fe76" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>02166c0a-6ee4-4ada-b175-f442978dcd4e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>df348c6c-74cf-4f31-b35f-76711625e029</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9e5c1f7e-d13a-40a5-a05b-874732aeec12</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f683e75f-9457-4a2d-aadd-4d1500d3d561</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3bf58e15-4c4a-4fb7-8932-175f3b896684</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>05af19e1-3e43-46f0-a8f2-e66c6df77696</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>182d9888-9fe7-4e78-9c63-fb2eefa21153</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9afad728-e8d2-4c46-9b14-b621a9a05ba5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f185f582-43ce-4acb-ad93-0a5a81a9154d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>5bb9c0ab-88e6-4458-80f3-5cb45145454b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>cab64202-9dfe-4f3f-ab40-f38f2663f588</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1a3b1c80-e970-45d7-8190-e6c437866e67</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="02166c0a-6ee4-4ada-b175-f442978dcd4e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="180" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>8c8e02ac-6a36-4da1-8c41-fe96b818adaa</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="df348c6c-74cf-4f31-b35f-76711625e029">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="940" y="180" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-619b</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>35339af2-0d88-4f14-833c-d488836beb5f</ns16:incoming>
                        
                        
                        <ns16:incoming>ffffd519-c33a-4013-b23e-dfc6ed46acbd</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns4:contentTask serverName="FileNet" operationRef="FOLDER_OP_CREATE_FOLDER" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Create Folder" id="9e5c1f7e-d13a-40a5-a05b-874732aeec12">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="504" y="157" width="95" height="70" />
                            
                            
                            <ns4:activityType>ContentTask</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>14cfe8b1-1c86-4b33-bebc-83e6616b2c82</ns16:incoming>
                        
                        
                        <ns16:incoming>ec8efb66-b66c-4eb8-93fa-47e066ffeb2f</ns16:incoming>
                        
                        
                        <ns16:outgoing>f57bd9c2-4682-4528-9c75-992d3f9965f4</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>OBJECT_TYPE_ID</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Folder"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>NAME</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.folderName</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>PARENT_FOLDER_ID</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.parentFolderID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>FOLDER_ID</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.folderID</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns4:contentTask>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Get ID" id="f683e75f-9457-4a2d-aadd-4d1500d3d561">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="737" y="157" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>f57bd9c2-4682-4528-9c75-992d3f9965f4</ns16:incoming>
                        
                        
                        <ns16:outgoing>35339af2-0d88-4f14-833c-d488836beb5f</ns16:outgoing>
                        
                        
                        <ns16:script>var fID = tw.local.folderID.toString();&#xD;
fID = fID.substring(4);&#xD;
tw.local.folderID = "{" + fID + "}" ;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="02166c0a-6ee4-4ada-b175-f442978dcd4e" targetRef="cab64202-9dfe-4f3f-ab40-f38f2663f588" name="To Parent Folder Exist" id="8c8e02ac-6a36-4da1-8c41-fe96b818adaa">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="9e5c1f7e-d13a-40a5-a05b-874732aeec12" targetRef="f683e75f-9457-4a2d-aadd-4d1500d3d561" name="To Get ID" id="f57bd9c2-4682-4528-9c75-992d3f9965f4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="f683e75f-9457-4a2d-aadd-4d1500d3d561" targetRef="df348c6c-74cf-4f31-b35f-76711625e029" name="To End" id="35339af2-0d88-4f14-833c-d488836beb5f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns4:contentTask serverName="FileNet" operationRef="FOLDER_OP_GET_FOLDER_BY_PATH" name="Get Folder Path" id="05af19e1-3e43-46f0-a8f2-e66c6df77696">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="503" y="279" width="95" height="70" />
                            
                            
                            <ns4:activityType>ContentTask</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2e8d986e-bd3a-4179-ac04-8cbe4616eac8</ns16:incoming>
                        
                        
                        <ns16:outgoing>920234c4-c533-418e-a18e-994b230cc914</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>PATH</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.parentFolderPath+"/"+tw.local.folderName</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>FOLDER</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183">tw.local.folder</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns4:contentTask>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183" isCollection="false" name="folder" id="2056.462e5358-f5c2-49c5-8148-3df2ef46c419" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Get Folder ID" id="182d9888-9fe7-4e78-9c63-fb2eefa21153">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="729" y="279" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>920234c4-c533-418e-a18e-994b230cc914</ns16:incoming>
                        
                        
                        <ns16:outgoing>ffffd519-c33a-4013-b23e-dfc6ed46acbd</ns16:outgoing>
                        
                        
                        <ns16:script>var fID = tw.local.folder.objectId.toString();&#xD;
fID = fID.substring(4);&#xD;
tw.local.folderID = "{" + fID + "}" ;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="05af19e1-3e43-46f0-a8f2-e66c6df77696" targetRef="182d9888-9fe7-4e78-9c63-fb2eefa21153" name="To Get Folder ID" id="920234c4-c533-418e-a18e-994b230cc914">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="182d9888-9fe7-4e78-9c63-fb2eefa21153" targetRef="df348c6c-74cf-4f31-b35f-76711625e029" name="To End" id="ffffd519-c33a-4013-b23e-dfc6ed46acbd">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:endEvent name="Error" id="3bf58e15-4c4a-4fb7-8932-175f3b896684">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="661" y="374" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>077dd0f2-40aa-4ade-8f65-c44d43c4f5e4</ns16:incoming>
                        
                        
                        <ns16:errorEventDefinition id="e1449e6f-e68e-4c30-99ac-d40450f14c1d" eventImplId="0dadf3e3-cca2-4a36-8b95-15d5ddf44c75">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns4:contentTask serverName="FileNet" operationRef="FOLDER_OP_GET_FOLDER_BY_PATH" name="Get Parent Folder By Path" id="9afad728-e8d2-4c46-9b14-b621a9a05ba5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="238" y="157" width="95" height="70" />
                            
                            
                            <ns4:activityType>ContentTask</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>29549467-7feb-453f-81da-41ef558d9391</ns16:incoming>
                        
                        
                        <ns16:outgoing>292bb32f-1d08-4f59-be1d-1520ae62463e</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>PATH</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.parentFolderPath</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>FOLDER</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183">tw.local.parent</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns4:contentTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="9afad728-e8d2-4c46-9b14-b621a9a05ba5" targetRef="1a3b1c80-e970-45d7-8190-e6c437866e67" name="To Set Parent Folder ID" id="292bb32f-1d08-4f59-be1d-1520ae62463e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183" isCollection="false" name="parent" id="2056.b896c83b-9dda-4345-865a-cb76e412afda" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="9e5c1f7e-d13a-40a5-a05b-874732aeec12" parallelMultiple="false" name="Error" id="f185f582-43ce-4acb-ad93-0a5a81a9154d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="539" y="215" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2e8d986e-bd3a-4179-ac04-8cbe4616eac8</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="ea610645-7055-4298-9b8e-0c0713c28549" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="33475a17-c875-492e-bee9-af52100a17d3" eventImplId="800f7367-f5b4-4eb6-8171-1be5da45ce6d">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="f185f582-43ce-4acb-ad93-0a5a81a9154d" targetRef="05af19e1-3e43-46f0-a8f2-e66c6df77696" name="To Get Folder Path" id="2e8d986e-bd3a-4179-ac04-8cbe4616eac8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="05af19e1-3e43-46f0-a8f2-e66c6df77696" parallelMultiple="false" name="Error1" id="5bb9c0ab-88e6-4458-80f3-5cb45145454b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="538" y="337" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>077dd0f2-40aa-4ade-8f65-c44d43c4f5e4</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="3924d6e6-bd15-4d6f-9121-eb2f7146a4cb" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="ccb2f17c-0e91-430d-b11c-e51531b419b8" eventImplId="738a5e9b-9337-43d6-8f6b-583f20b18a99">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="5bb9c0ab-88e6-4458-80f3-5cb45145454b" targetRef="3bf58e15-4c4a-4fb7-8932-175f3b896684" name="To Error" id="077dd0f2-40aa-4ade-8f65-c44d43c4f5e4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="29549467-7feb-453f-81da-41ef558d9391" name="Parent Folder Exist" id="cab64202-9dfe-4f3f-ab40-f38f2663f588">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="123" y="176" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>8c8e02ac-6a36-4da1-8c41-fe96b818adaa</ns16:incoming>
                        
                        
                        <ns16:outgoing>29549467-7feb-453f-81da-41ef558d9391</ns16:outgoing>
                        
                        
                        <ns16:outgoing>14cfe8b1-1c86-4b33-bebc-83e6616b2c82</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="cab64202-9dfe-4f3f-ab40-f38f2663f588" targetRef="9afad728-e8d2-4c46-9b14-b621a9a05ba5" name="To Get Parent Folder By Path" id="29549467-7feb-453f-81da-41ef558d9391">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.parentFolderID	  ==	  </ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="cab64202-9dfe-4f3f-ab40-f38f2663f588" targetRef="9e5c1f7e-d13a-40a5-a05b-874732aeec12" name="To Script Task" id="14cfe8b1-1c86-4b33-bebc-83e6616b2c82">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.parentFolderID	  !=	  null</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Set Parent Folder ID" id="1a3b1c80-e970-45d7-8190-e6c437866e67">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="364" y="157" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>292bb32f-1d08-4f59-be1d-1520ae62463e</ns16:incoming>
                        
                        
                        <ns16:outgoing>ec8efb66-b66c-4eb8-93fa-47e066ffeb2f</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.parentFolderID= tw.local.parent.objectId;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="1a3b1c80-e970-45d7-8190-e6c437866e67" targetRef="9e5c1f7e-d13a-40a5-a05b-874732aeec12" name="To Create Folder" id="ec8efb66-b66c-4eb8-93fa-47e066ffeb2f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Create Folder">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.ec8efb66-b66c-4eb8-93fa-47e066ffeb2f</processLinkId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.1a3b1c80-e970-45d7-8190-e6c437866e67</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.9e5c1f7e-d13a-40a5-a05b-874732aeec12</toProcessItemId>
            <guid>5ca5b1cd-9921-44d1-b667-057c1f427daa</guid>
            <versionId>10491290-77ae-4d7d-b60b-455976cb46f0</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.1a3b1c80-e970-45d7-8190-e6c437866e67</fromProcessItemId>
            <toProcessItemId>2025.9e5c1f7e-d13a-40a5-a05b-874732aeec12</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.ffffd519-c33a-4013-b23e-dfc6ed46acbd</processLinkId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.182d9888-9fe7-4e78-9c63-fb2eefa21153</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.df348c6c-74cf-4f31-b35f-76711625e029</toProcessItemId>
            <guid>d9abafd7-567e-47f6-90c1-922aeb0537e2</guid>
            <versionId>127a3a48-de96-402c-96b1-facd5f705566</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.182d9888-9fe7-4e78-9c63-fb2eefa21153</fromProcessItemId>
            <toProcessItemId>2025.df348c6c-74cf-4f31-b35f-76711625e029</toProcessItemId>
        </link>
        <link name="To Get Parent Folder By Path">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.29549467-7feb-453f-81da-41ef558d9391</processLinkId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.cab64202-9dfe-4f3f-ab40-f38f2663f588</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.9afad728-e8d2-4c46-9b14-b621a9a05ba5</toProcessItemId>
            <guid>9d1d1a5d-f5df-439e-af86-8352b85c201b</guid>
            <versionId>3f395197-9682-4df1-8983-f70607d47de1</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.cab64202-9dfe-4f3f-ab40-f38f2663f588</fromProcessItemId>
            <toProcessItemId>2025.9afad728-e8d2-4c46-9b14-b621a9a05ba5</toProcessItemId>
        </link>
        <link name="To Get ID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.f57bd9c2-4682-4528-9c75-992d3f9965f4</processLinkId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.9e5c1f7e-d13a-40a5-a05b-874732aeec12</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.f683e75f-9457-4a2d-aadd-4d1500d3d561</toProcessItemId>
            <guid>a5fda6a4-e52d-46d7-b7e1-d728fc9883ce</guid>
            <versionId>3f996436-6e8f-4fa0-aba4-4e2224da5934</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.9e5c1f7e-d13a-40a5-a05b-874732aeec12</fromProcessItemId>
            <toProcessItemId>2025.f683e75f-9457-4a2d-aadd-4d1500d3d561</toProcessItemId>
        </link>
        <link name="To Get Folder ID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.920234c4-c533-418e-a18e-994b230cc914</processLinkId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.05af19e1-3e43-46f0-a8f2-e66c6df77696</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.182d9888-9fe7-4e78-9c63-fb2eefa21153</toProcessItemId>
            <guid>40da446f-bea3-41d6-84d4-d22a832fc0c0</guid>
            <versionId>6584cdb8-f9b4-450b-beba-a4562f86a82f</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.05af19e1-3e43-46f0-a8f2-e66c6df77696</fromProcessItemId>
            <toProcessItemId>2025.182d9888-9fe7-4e78-9c63-fb2eefa21153</toProcessItemId>
        </link>
        <link name="To Set Parent Folder ID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.292bb32f-1d08-4f59-be1d-1520ae62463e</processLinkId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.9afad728-e8d2-4c46-9b14-b621a9a05ba5</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.1a3b1c80-e970-45d7-8190-e6c437866e67</toProcessItemId>
            <guid>474ea111-948a-4773-a0b4-42260cf90bdd</guid>
            <versionId>8ab8497f-032c-4a10-8bf7-ea0055a3ef91</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.9afad728-e8d2-4c46-9b14-b621a9a05ba5</fromProcessItemId>
            <toProcessItemId>2025.1a3b1c80-e970-45d7-8190-e6c437866e67</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.35339af2-0d88-4f14-833c-d488836beb5f</processLinkId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f683e75f-9457-4a2d-aadd-4d1500d3d561</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.df348c6c-74cf-4f31-b35f-76711625e029</toProcessItemId>
            <guid>e6a85d60-b719-43fd-a6ae-7e6578cad99c</guid>
            <versionId>d438398a-69d7-443c-980d-9fe4574600f5</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.f683e75f-9457-4a2d-aadd-4d1500d3d561</fromProcessItemId>
            <toProcessItemId>2025.df348c6c-74cf-4f31-b35f-76711625e029</toProcessItemId>
        </link>
        <link name="To Script Task">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.14cfe8b1-1c86-4b33-bebc-83e6616b2c82</processLinkId>
            <processId>1.45e2a149-1d4a-4946-ad59-f81f1424f100</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.cab64202-9dfe-4f3f-ab40-f38f2663f588</fromProcessItemId>
            <endStateId>guid:2e93acfacc1269a3:3c8702fd:18968452cb5:3997</endStateId>
            <toProcessItemId>2025.9e5c1f7e-d13a-40a5-a05b-874732aeec12</toProcessItemId>
            <guid>98bfff8c-44db-435a-b040-29791fec691e</guid>
            <versionId>f422fde6-8db4-4757-920a-4438b3a7ccaa</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="topCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.cab64202-9dfe-4f3f-ab40-f38f2663f588</fromProcessItemId>
            <toProcessItemId>2025.9e5c1f7e-d13a-40a5-a05b-874732aeec12</toProcessItemId>
        </link>
    </process>
</teamworks>

