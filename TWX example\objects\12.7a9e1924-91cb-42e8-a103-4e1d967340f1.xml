<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <twClass id="12.7a9e1924-91cb-42e8-a103-4e1d967340f1" name="IDCCustomsReleaseRequest">
        <lastModified>1688661259527</lastModified>
        <lastModifiedBy>bawadmin</lastModifiedBy>
        <classId>12.7a9e1924-91cb-42e8-a103-4e1d967340f1</classId>
        <type>1</type>
        <isSystem>false</isSystem>
        <shared>false</shared>
        <isShadow>false</isShadow>
        <globalLifetime>false</globalLifetime>
        <internalName isNull="true" />
        <extensionType isNull="true" />
        <saveServiceRef isNull="true" />
        <bpmn2Data isNull="true" />
        <externalId>itm.12.7a9e1924-91cb-42e8-a103-4e1d967340f1</externalId>
        <dependencySummary isNull="true" />
        <jsonData>{"attributeFormDefault":"unqualified","elementFormDefault":"unqualified","targetNamespace":"http:\/\/NBEDCP","complexType":[{"annotation":{"documentation":[{}],"appinfo":[{"shared":[false],"advancedProperties":[{}],"shadow":[false]}]},"sequence":{"element":[{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["appInfo"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{}]}]},"name":"appInfo","type":"{http:\/\/NBEC}AppInfo","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.e7100a5a-462f-4f06-a0ea-5f9c5b209bd6"}}]},"name":"IDCCustomsReleaseRequest"}],"id":"_12.04ad9216-ebb8-4426-9b95-a9c76b6b7eb7"}</jsonData>
        <description isNull="true" />
        <guid>d0be722b-47ba-4a7f-9f58-aedf0a6b524b</guid>
        <versionId>aa249f18-0202-4682-97c7-63f5b9232cbd</versionId>
        <definition>
            <property>
                <name>appInfo</name>
                <description isNull="true" />
                <classRef>a368ab07-810c-4dc3-ac3e-cdf100ce9894/12.e7100a5a-462f-4f06-a0ea-5f9c5b209bd6</classRef>
                <arrayProperty>false</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType isNull="true" />
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName isNull="true" />
                    <typeNamespace isNull="true" />
                    <minOccurs isNull="true" />
                    <maxOccurs isNull="true" />
                    <nillable isNull="true" />
                    <order isNull="true" />
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <validator>
                <className isNull="true" />
                <errorMessage isNull="true" />
                <webWidgetJavaClass isNull="true" />
                <externalType isNull="true" />
                <configData>
                    <schema>
                        <simpleType name="IDCCustomsReleaseRequest">
                            <restriction base="String" />
                        </simpleType>
                    </schema>
                </configData>
            </validator>
            <annotation type="com.lombardisoftware.core.xml.XMLTypeAnnotation" version="2.0">
                <exclude isNull="true" />
                <anonymous isNull="true" />
                <local isNull="true" />
                <name isNull="true" />
                <namespace isNull="true" />
                <elementName isNull="true" />
                <elementNamespace isNull="true" />
                <protoTypeName isNull="true" />
                <baseTypeName isNull="true" />
                <specialType isNull="true" />
                <contentTypeVariety isNull="true" />
                <xscRef isNull="true" />
            </annotation>
        </definition>
    </twClass>
</teamworks>

