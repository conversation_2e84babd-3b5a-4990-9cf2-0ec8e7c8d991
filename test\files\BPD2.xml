<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <bpd id="25.05eef081-e788-4d4c-8465-1fcea678278c" name="Se<PERSON> título">
        <lastModified>1569971472729</lastModified>
        <lastModifiedBy>t99kmg07</lastModifiedBy>
        <bpdId>25.05eef081-e788-4d4c-8465-1fcea678278c</bpdId>
        <isTrackingEnabled>true</isTrackingEnabled>
        <isSpcEnabled>false</isSpcEnabled>
        <restrictedName isNull="true" />
        <isCriticalPathEnabled>false</isCriticalPathEnabled>
        <participantRef isNull="true" />
        <businessDataParticipantRef isNull="true" />
        <perfMetricParticipantRef isNull="true" />
        <ownerTeamParticipantRef isNull="true" />
        <timeScheduleType isNull="true" />
        <timeScheduleName isNull="true" />
        <timeScheduleExpression isNull="true" />
        <holidayScheduleType isNull="true" />
        <holidayScheduleName isNull="true" />
        <holidayScheduleExpression isNull="true" />
        <timezoneType isNull="true" />
        <timezone isNull="true" />
        <timezoneExpression isNull="true" />
        <internalName isNull="true" />
        <description></description>
        <type>1</type>
        <rootBpdId isNull="true" />
        <parentBpdId isNull="true" />
        <parentFlowObjectId isNull="true" />
        <xmlData isNull="true" />
        <bpmn2Data>&lt;ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/bpm/Extensions" xmlns:ns6="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns17="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/uitheme" xmlns:ns20="http://www.ibm.com/bpm/coachview" xmlns:ns21="http://www.ibm.com/xmlns/tagging" id="ca9fbe43-4ba7-417e-afa3-9d18da9d74ba" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript"&gt;&lt;ns16:process name="Sem título" id="25.05eef081-e788-4d4c-8465-1fcea678278c" ns3:executionMode="longRunning"&gt;&lt;ns16:documentation /&gt;&lt;ns16:extensionElements&gt;&lt;ns4:bpdExtension instanceName="&amp;quot;Sem título:&amp;quot; + tw.system.process.instanceId" dueDateEnabled="true" atRiskCalcEnabled="true" enableTracking="true" allowProjectedPathManagement="false" optimizeExecForLatency="false" sBOSyncEnabled="true" allowContentOperations="false" autoTrackingEnabled="false" autoTrackingName="at1569970897959"&gt;&lt;ns4:dueDateSettings type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;8&lt;/ns4:dueDate&gt;&lt;/ns4:dueDateSettings&gt;&lt;ns4:workSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:workSchedule&gt;&lt;/ns4:bpdExtension&gt;&lt;ns2:caseExtension&gt;&lt;ns2:caseFolder id="298d9954-af07-4258-b312-02b5cf8a3743" /&gt;&lt;/ns2:caseExtension&gt;&lt;ns2:isConvergedProcess&gt;true&lt;/ns2:isConvergedProcess&gt;&lt;/ns16:extensionElements&gt;&lt;ns16:ioSpecification&gt;&lt;ns16:inputSet id="_9bb3ef15-0ab1-450c-b6b8-52b4ea618a7b" /&gt;&lt;ns16:outputSet id="_e16330f9-c787-407f-99e5-2e084069fc4e" /&gt;&lt;/ns16:ioSpecification&gt;&lt;ns16:laneSet id="8a42a4b6-5279-4886-bc7d-eb508c64cea3"&gt;&lt;ns16:lane name="Equipa" partitionElementRef="24.da7e4d23-78cb-4483-98ed-b9c238308a03" id="95b63cda-fda9-423b-96de-676694f50a48" ns4:isSystemLane="false"&gt;&lt;ns16:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="0" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns16:extensionElements&gt;&lt;ns16:flowNodeRef&gt;8ef97c1e-0f04-47c5-ae36-0009c0a05590&lt;/ns16:flowNodeRef&gt;&lt;ns16:flowNodeRef&gt;5cd75f8a-ab29-419b-8fbf-8fd2aa015b4f&lt;/ns16:flowNodeRef&gt;&lt;ns16:flowNodeRef&gt;7c8f6977-c612-4e07-82c3-25f04cf97990&lt;/ns16:flowNodeRef&gt;&lt;ns16:flowNodeRef&gt;586c05e8-80be-443f-8f4e-5423df284621&lt;/ns16:flowNodeRef&gt;&lt;ns16:flowNodeRef&gt;1f970dac-a098-478b-865a-abb23a662df9&lt;/ns16:flowNodeRef&gt;&lt;ns16:flowNodeRef&gt;0afc428f-c762-4e2d-8275-781f412e5a88&lt;/ns16:flowNodeRef&gt;&lt;ns16:flowNodeRef&gt;07ef1283-41ee-427a-887f-5f3ce548d0f9&lt;/ns16:flowNodeRef&gt;&lt;/ns16:lane&gt;&lt;ns16:lane name="Sistema" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="7aeab881-f052-44a8-88df-555a9fbb8a1a" ns4:isSystemLane="true"&gt;&lt;ns16:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="201" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns16:extensionElements&gt;&lt;ns16:flowNodeRef&gt;3a475195-368c-4507-8885-83c8cdf88a27&lt;/ns16:flowNodeRef&gt;&lt;/ns16:lane&gt;&lt;/ns16:laneSet&gt;&lt;ns16:startEvent name="Iniciar" id="8ef97c1e-0f04-47c5-ae36-0009c0a05590"&gt;&lt;ns16:extensionElements&gt;&lt;ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" /&gt;&lt;/ns16:extensionElements&gt;&lt;/ns16:startEvent&gt;&lt;ns16:endEvent name="Terminar" id="5cd75f8a-ab29-419b-8fbf-8fd2aa015b4f"&gt;&lt;ns16:extensionElements&gt;&lt;ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" /&gt;&lt;/ns16:extensionElements&gt;&lt;/ns16:endEvent&gt;&lt;ns16:callActivity calledElement="1.58e2b9fc-0846-494a-b2c4-8c0421da19b8" name="Tarefa de utilizador incluída" id="7c8f6977-c612-4e07-82c3-25f04cf97990"&gt;&lt;ns16:extensionElements&gt;&lt;ns3:activityAdHocSettings repeatable="false" hidden="false" triggerType="Automatic" option="Required" /&gt;&lt;ns13:nodeVisualInfo x="117" y="65" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;1&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;(use default)&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;ns3:activityPreconditions triggerType="NoPreconditions" documentTriggerMode="External" /&gt;&lt;/ns16:extensionElements&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns16:performer name="Expert" /&gt;&lt;/ns16:callActivity&gt;&lt;ns16:callActivity calledElement="1.82b84887-27a8-4ed1-a944-29be95a9b7e5" name="Tarefa de utilizador incluída1" id="586c05e8-80be-443f-8f4e-5423df284621"&gt;&lt;ns16:extensionElements&gt;&lt;ns3:activityAdHocSettings repeatable="false" hidden="false" triggerType="Automatic" option="Required" /&gt;&lt;ns13:nodeVisualInfo x="258" y="74" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;1&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;(use default)&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;ns3:activityPreconditions triggerType="NoPreconditions" documentTriggerMode="External" /&gt;&lt;/ns16:extensionElements&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns16:performer name="Expert" /&gt;&lt;/ns16:callActivity&gt;&lt;ns16:callActivity calledElement="1.f24c65ec-bf84-4145-86bf-c3d6aac64167" name="Tarefa de utilizador incluída2" id="1f970dac-a098-478b-865a-abb23a662df9"&gt;&lt;ns16:extensionElements&gt;&lt;ns3:activityAdHocSettings repeatable="false" hidden="false" triggerType="Automatic" option="Required" /&gt;&lt;ns13:nodeVisualInfo x="404" y="76" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;1&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;(use default)&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;InlineUserTask&lt;/ns4:activityType&gt;&lt;ns3:activityPreconditions triggerType="NoPreconditions" documentTriggerMode="External" /&gt;&lt;/ns16:extensionElements&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns16:performer name="Expert" /&gt;&lt;/ns16:callActivity&gt;&lt;ns16:callActivity calledElement="1.61a0bf71-b88b-4fc3-b79b-f023825ed63e" name="Tarefa de utilizador incluída3" id="0afc428f-c762-4e2d-8275-781f412e5a88"&gt;&lt;ns16:extensionElements&gt;&lt;ns13:nodeVisualInfo x="537" y="33" width="95" height="70" /&gt;&lt;ns4:deleteTaskOnCompletion&gt;true&lt;/ns4:deleteTaskOnCompletion&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;1&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;(use default)&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;ServiceTask&lt;/ns4:activityType&gt;&lt;/ns16:extensionElements&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns16:performer name="Expert" /&gt;&lt;/ns16:callActivity&gt;&lt;ns16:callActivity calledElement="1.bdd3fc6b-5583-46c6-9ddf-91408b409b0f" name="Tarefa de utilizador incluída4" id="07ef1283-41ee-427a-887f-5f3ce548d0f9"&gt;&lt;ns16:extensionElements&gt;&lt;ns13:nodeVisualInfo x="769" y="53" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;1&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;(use default)&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;RuleTask&lt;/ns4:activityType&gt;&lt;/ns16:extensionElements&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns16:performer name="Expert" /&gt;&lt;/ns16:callActivity&gt;&lt;ns16:callActivity calledElement="1.7f6199ee-84e0-4fd4-aed2-6df579139f8f" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Tarefa de utilizador incluída5" id="3a475195-368c-4507-8885-83c8cdf88a27"&gt;&lt;ns16:extensionElements&gt;&lt;ns13:nodeVisualInfo x="429" y="69" width="95" height="70" /&gt;&lt;ns4:deleteTaskOnCompletion&gt;true&lt;/ns4:deleteTaskOnCompletion&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;1&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;(use default)&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;ServiceTask&lt;/ns4:activityType&gt;&lt;/ns16:extensionElements&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns16:performer name="Expert" /&gt;&lt;/ns16:callActivity&gt;&lt;ns16:resourceRole name="participantRef" /&gt;&lt;ns16:resourceRole name="businessDataParticipantRef" /&gt;&lt;ns16:resourceRole name="perfMetricParticipantRef" /&gt;&lt;ns16:resourceRole name="ownerTeamParticipantRef" /&gt;&lt;/ns16:process&gt;&lt;ns16:interface name="Sem títuloInterface" id="_a4b785d7-d9be-4599-83d8-b4ec74693ef4" /&gt;&lt;ns16:globalUserTask implementation="64.af46ef40-d360-4e61-a58a-5dcd3b249894" name="1.f24c65ec-bf84-4145-86bf-c3d6aac64167" id="1.f24c65ec-bf84-4145-86bf-c3d6aac64167"&gt;&lt;ns16:ioSpecification&gt;&lt;ns16:inputSet id="_634c9159-4e9e-47ff-830c-4d3d7a9f210e" /&gt;&lt;ns16:outputSet id="_df792ba6-ca01-4f7f-8de1-2e11d219360b" /&gt;&lt;/ns16:ioSpecification&gt;&lt;/ns16:globalUserTask&gt;&lt;/ns16:definitions&gt;&#xD;
</bpmn2Data>
        <dependencySummary>&lt;dependencySummary id="bpdid:7b88404e5edd3729:-4942888d:16d37065276:-144d"&gt;&#xD;
  &lt;artifactReference id="bpdid:7b88404e5edd3729:-4942888d:16d37065276:-144c"&gt;&#xD;
    &lt;refId&gt;/1.f24c65ec-bf84-4145-86bf-c3d6aac64167&lt;/refId&gt;&#xD;
    &lt;refType&gt;1&lt;/refType&gt;&#xD;
    &lt;nameValuePair id="bpdid:7b88404e5edd3729:-4942888d:16d37065276:-144b"&gt;&#xD;
      &lt;name&gt;mimeType&lt;/name&gt;&#xD;
      &lt;value&gt;inlineUserTask&lt;/value&gt;&#xD;
    &lt;/nameValuePair&gt;&#xD;
  &lt;/artifactReference&gt;&#xD;
&lt;/dependencySummary&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"isInterrupting":true,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Iniciar","declaredType":"startEvent","id":"8ef97c1e-0f04-47c5-ae36-0009c0a05590"},{"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Terminar","declaredType":"endEvent","id":"5cd75f8a-ab29-419b-8fbf-8fd2aa015b4f"},{"startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"extensionElements":{"activityAdHocSettings":[{"hidden":false,"repeatable":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TActivityAdHocSettings","triggerType":"Automatic","option":"Required"}],"nodeVisualInfo":[{"width":95,"x":117,"y":65,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"1","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"(use default)"},"type":"TimeCalculation"}}],"activityType":["UserTask"],"activityPreconditions":[{"documentTriggerMode":"External","sourceFolderReferenceType":"FolderId","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TActivityPreconditions","triggerType":"NoPreconditions","matchAll":true}]},"name":"Tarefa de utilizador inclu\u00edda","isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"7c8f6977-c612-4e07-82c3-25f04cf97990","calledElement":"1.58e2b9fc-0846-494a-b2c4-8c0421da19b8"},{"startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"extensionElements":{"activityAdHocSettings":[{"hidden":false,"repeatable":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TActivityAdHocSettings","triggerType":"Automatic","option":"Required"}],"nodeVisualInfo":[{"width":95,"x":258,"y":74,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"1","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"(use default)"},"type":"TimeCalculation"}}],"activityType":["UserTask"],"activityPreconditions":[{"documentTriggerMode":"External","sourceFolderReferenceType":"FolderId","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TActivityPreconditions","triggerType":"NoPreconditions","matchAll":true}]},"name":"Tarefa de utilizador inclu\u00edda1","isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"586c05e8-80be-443f-8f4e-5423df284621","calledElement":"1.82b84887-27a8-4ed1-a944-29be95a9b7e5"},{"startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"extensionElements":{"activityAdHocSettings":[{"hidden":false,"repeatable":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TActivityAdHocSettings","triggerType":"Automatic","option":"Required"}],"nodeVisualInfo":[{"width":95,"x":404,"y":76,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"1","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"(use default)"},"type":"TimeCalculation"}}],"activityType":["InlineUserTask"],"activityPreconditions":[{"documentTriggerMode":"External","sourceFolderReferenceType":"FolderId","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TActivityPreconditions","triggerType":"NoPreconditions","matchAll":true}]},"name":"Tarefa de utilizador inclu\u00edda2","isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"1f970dac-a098-478b-865a-abb23a662df9","calledElement":"1.f24c65ec-bf84-4145-86bf-c3d6aac64167"},{"startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":537,"y":33,"declaredType":"TNodeVisualInfo","height":70}],"deleteTaskOnCompletion":[true],"userTaskSettings":[{"activityAssignmentType":"Lane","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"1","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"(use default)"},"type":"TimeCalculation"}}],"activityType":["ServiceTask"]},"name":"Tarefa de utilizador inclu\u00edda3","isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"0afc428f-c762-4e2d-8275-781f412e5a88","calledElement":"1.61a0bf71-b88b-4fc3-b79b-f023825ed63e"},{"startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":769,"y":53,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"1","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"(use default)"},"type":"TimeCalculation"}}],"activityType":["RuleTask"]},"name":"Tarefa de utilizador inclu\u00edda4","isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"07ef1283-41ee-427a-887f-5f3ce548d0f9","calledElement":"1.bdd3fc6b-5583-46c6-9ddf-91408b409b0f"},{"startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":429,"y":69,"declaredType":"TNodeVisualInfo","height":70}],"deleteTaskOnCompletion":[true],"userTaskSettings":[{"activityAssignmentType":"Lane","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"1","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"(use default)"},"type":"TimeCalculation"}}],"activityType":["ServiceTask"]},"name":"Tarefa de utilizador inclu\u00edda5","isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"3a475195-368c-4507-8885-83c8cdf88a27","calledElement":"1.7f6199ee-84e0-4fd4-aed2-6df579139f8f"}],"laneSet":[{"id":"8a42a4b6-5279-4886-bc7d-eb508c64cea3","lane":[{"flowNodeRef":["8ef97c1e-0f04-47c5-ae36-0009c0a05590","5cd75f8a-ab29-419b-8fbf-8fd2aa015b4f","7c8f6977-c612-4e07-82c3-25f04cf97990","586c05e8-80be-443f-8f4e-5423df284621","1f970dac-a098-478b-865a-abb23a662df9","0afc428f-c762-4e2d-8275-781f412e5a88","07ef1283-41ee-427a-887f-5f3ce548d0f9"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":200}]},"name":"Equipa","partitionElementRef":"24.da7e4d23-78cb-4483-98ed-b9c238308a03","declaredType":"lane","id":"95b63cda-fda9-423b-96de-676694f50a48","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"false"}},{"flowNodeRef":["3a475195-368c-4507-8885-83c8cdf88a27"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":201,"declaredType":"TNodeVisualInfo","height":200}]},"name":"Sistema","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"7aeab881-f052-44a8-88df-555a9fbb8a1a","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"resourceRole":[{"name":"participantRef","declaredType":"resourceRole"},{"name":"businessDataParticipantRef","declaredType":"resourceRole"},{"name":"perfMetricParticipantRef","declaredType":"resourceRole"},{"name":"ownerTeamParticipantRef","declaredType":"resourceRole"}],"isClosed":false,"extensionElements":{"bpdExtension":[{"allowContentOperations":false,"enableTracking":true,"workSchedule":{"timezoneType":0,"timeScheduleType":0,"holidayScheduleType":0},"instanceName":"\"Sem t\u00edtulo:\" + tw.system.process.instanceId","dueDateSettings":{"dueDate":{"unit":"Hours","value":"8","timeOfDay":"00:00"},"type":"TimeCalculation"},"autoTrackingName":"at1569970897959","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TBPDExtension","optimizeExecForLatency":false,"dueDateEnabled":true,"atRiskCalcEnabled":true,"allowProjectedPathManagement":false,"autoTrackingEnabled":false,"sboSyncEnabled":true}],"caseExtension":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmcaseext.TCaseExtension","caseFolder":{"allowSubfoldersCreation":false,"allowLocalDoc":false,"id":"298d9954-af07-4258-b312-02b5cf8a3743","allowExternalFolder":false,"allowExternalDoc":false}}],"isConvergedProcess":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Sem t\u00edtulo","declaredType":"process","id":"25.05eef081-e788-4d4c-8465-1fcea678278c","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"longRunning"},"ioSpecification":{"inputSet":[{"id":"_9bb3ef15-0ab1-450c-b6b8-52b4ea618a7b"}],"outputSet":[{"id":"_e16330f9-c787-407f-99e5-2e084069fc4e"}]}},{"name":"Sem t\u00edtuloInterface","declaredType":"interface","id":"_a4b785d7-d9be-4599-83d8-b4ec74693ef4"},{"implementation":"64.af46ef40-d360-4e61-a58a-5dcd3b249894","name":"1.f24c65ec-bf84-4145-86bf-c3d6aac64167","declaredType":"globalUserTask","id":"1.f24c65ec-bf84-4145-86bf-c3d6aac64167","ioSpecification":{"inputSet":[{"id":"_634c9159-4e9e-47ff-830c-4d3d7a9f210e"}],"outputSet":[{"id":"_df792ba6-ca01-4f7f-8de1-2e11d219360b"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"ca9fbe43-4ba7-417e-afa3-9d18da9d74ba"}</jsonData>
        <migrationData isNull="true" />
        <templateId isNull="true" />
        <externalId isNull="true" />
        <guid>guid:7b88404e5edd3729:-4942888d:16d37065276:-14ba</guid>
        <versionId>f50ca6d3-ee1f-4ada-8ca1-b3e19aadb2fb</versionId>
        <BusinessProcessDiagram id="bpdid:7b88404e5edd3729:-4942888d:16d37065276:-14bb">
            <metadata>
                <entry>
                    <key>SAP_META.SHOULDRECOVER</key>
                    <value>no</value>
                </entry>
            </metadata>
            <name>Sem título</name>
            <documentation></documentation>
            <name>Sem título</name>
            <dimension>
                <size w="600" h="150" />
            </dimension>
            <author>t99kmg07</author>
            <isTrackingEnabled>true</isTrackingEnabled>
            <isCriticalPathEnabled>false</isCriticalPathEnabled>
            <isSpcEnabled>false</isSpcEnabled>
            <isDueDateEnabled>true</isDueDateEnabled>
            <isAtRiskCalcEnabled>true</isAtRiskCalcEnabled>
            <creationDate>1569970897996</creationDate>
            <modificationDate>1569971472729</modificationDate>
            <metricSettings itemType="2" />
            <instanceNameExpression>"Sem título:" + tw.system.process.instanceId</instanceNameExpression>
            <dueDateType>1</dueDateType>
            <dueDateTime>8</dueDateTime>
            <dueDateTimeResolution>1</dueDateTimeResolution>
            <dueDateTimeTOD>00:00</dueDateTimeTOD>
            <officeIntegration>
                <sharePointParentSiteDisabled>true</sharePointParentSiteDisabled>
                <sharePointParentSiteName>&lt;#= tw.system.process.name #&gt;</sharePointParentSiteName>
                <sharePointParentSiteTemplate>ParentSiteTemplate.stp</sharePointParentSiteTemplate>
                <sharePointWorkspaceSiteName>&lt;#= tw.system.process.name #&gt; &lt;#= tw.system.process.instanceId #&gt;</sharePointWorkspaceSiteName>
                <sharePointWorkspaceSiteDescription>This site has been automatically generated for managing collaborations and documents 
for the Lombardi TeamWorks process instance: &lt;#= tw.system.process.name #&gt; &lt;#= tw.system.process.instanceId #&gt;

TeamWorks Link:  http://ACFINMDWBPMD01.ATLANTICO.int:9081/portal/jsp/getProcessDetails.do?bpdInstanceId=&lt;#= tw.system.process.instanceId #&gt;

</sharePointWorkspaceSiteDescription>
                <sharePointWorkspaceSiteTemplate>WorkspaceSiteTemplate.stp</sharePointWorkspaceSiteTemplate>
                <sharePointLCID>1033</sharePointLCID>
            </officeIntegration>
            <timeScheduleType>0</timeScheduleType>
            <holidayScheduleType>0</holidayScheduleType>
            <timezoneType>0</timezoneType>
            <executionProfile>default</executionProfile>
            <isSBOSyncEnabled>true</isSBOSyncEnabled>
            <allowContentOperations>false</allowContentOperations>
            <isLegacyCaseMigrated>false</isLegacyCaseMigrated>
            <defaultPool>
                <BpmnObjectId id="8a42a4b6-5279-4886-bc7d-eb508c64cea3" />
            </defaultPool>
            <defaultInstanceUI id="bpdid:7b88404e5edd3729:-4942888d:16d37065276:-1449" />
            <ownerTeamInstanceUI id="bpdid:7b88404e5edd3729:-4942888d:16d37065276:-144a" />
            <simulationScenario id="bpdid:7b88404e5edd3729:-4942888d:16d37065276:-14b4">
                <name>Predefinição</name>
                <simNumInstances>100</simNumInstances>
                <simMinutesBetween>30</simMinutesBetween>
                <maxInstances>100</maxInstances>
                <useMaxInstances>true</useMaxInstances>
                <continueFromReal>false</continueFromReal>
                <useParticipantCalendars>false</useParticipantCalendars>
                <useDuration>false</useDuration>
                <duration>86400</duration>
                <startTime>1569970898334</startTime>
            </simulationScenario>
            <pool id="8a42a4b6-5279-4886-bc7d-eb508c64cea3">
                <name>Conjunto</name>
                <documentation></documentation>
                <restrictedName>at1569970897959</restrictedName>
                <dimension>
                    <size w="3000" h="400" />
                </dimension>
                <autoTrackingEnabled>false</autoTrackingEnabled>
                <lane id="95b63cda-fda9-423b-96de-676694f50a48">
                    <name>Equipa</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>2c7ae840-cf8c-4998-839b-2cf42b6b7656/24.da7e4d23-78cb-4483-98ed-b9c238308a03</attachedParticipant>
                    <flowObject id="7c8f6977-c612-4e07-82c3-25f04cf97990" componentType="Activity">
                        <name>Tarefa de utilizador incluída</name>
                        <documentation></documentation>
                        <position>
                            <location x="117" y="65" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>AUTOMATIC</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <implementation>
                                <attachedActivityId>/1.58e2b9fc-0846-494a-b2c4-8c0421da19b8</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <forceSend>true</forceSend>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <laneFilter id="bpdid:7b88404e5edd3729:-4942888d:16d37065276:-1446">
                                    <serviceType>1</serviceType>
                                    <teamRef>2c7ae840-cf8c-4998-839b-2cf42b6b7656/24.da7e4d23-78cb-4483-98ed-b9c238308a03</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:7b88404e5edd3729:-4942888d:16d37065276:-1445">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                            <preconditions id="bpdid:7b88404e5edd3729:-4942888d:16d37065276:-1448">
                                <triggerType>0</triggerType>
                                <matchAll>true</matchAll>
                                <documentTriggerMode>1</documentTriggerMode>
                                <sourceFolderReferenceType>0</sourceFolderReferenceType>
                            </preconditions>
                        </component>
                    </flowObject>
                    <flowObject id="586c05e8-80be-443f-8f4e-5423df284621" componentType="Activity">
                        <name>Tarefa de utilizador incluída1</name>
                        <position>
                            <location x="258" y="74" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>AUTOMATIC</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <implementation>
                                <attachedActivityId>/1.82b84887-27a8-4ed1-a944-29be95a9b7e5</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <forceSend>true</forceSend>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <laneFilter id="bpdid:7b88404e5edd3729:-4942888d:16d37065276:-1442">
                                    <serviceType>1</serviceType>
                                    <teamRef>2c7ae840-cf8c-4998-839b-2cf42b6b7656/24.da7e4d23-78cb-4483-98ed-b9c238308a03</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:7b88404e5edd3729:-4942888d:16d37065276:-1441">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                            <preconditions id="bpdid:7b88404e5edd3729:-4942888d:16d37065276:-1444">
                                <triggerType>0</triggerType>
                                <matchAll>true</matchAll>
                                <documentTriggerMode>1</documentTriggerMode>
                                <sourceFolderReferenceType>0</sourceFolderReferenceType>
                            </preconditions>
                        </component>
                    </flowObject>
                    <flowObject id="1f970dac-a098-478b-865a-abb23a662df9" componentType="Activity">
                        <name>Tarefa de utilizador incluída2</name>
                        <documentation></documentation>
                        <position>
                            <location x="404" y="76" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>AUTOMATIC</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <implementation>
                                <attachedActivityId>/1.f24c65ec-bf84-4145-86bf-c3d6aac64167</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <forceSend>true</forceSend>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <laneFilter id="bpdid:7b88404e5edd3729:-4942888d:16d37065276:-143e">
                                    <serviceType>1</serviceType>
                                    <teamRef>2c7ae840-cf8c-4998-839b-2cf42b6b7656/24.da7e4d23-78cb-4483-98ed-b9c238308a03</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:7b88404e5edd3729:-4942888d:16d37065276:-143d">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                            <preconditions id="bpdid:7b88404e5edd3729:-4942888d:16d37065276:-1440">
                                <triggerType>0</triggerType>
                                <matchAll>true</matchAll>
                                <documentTriggerMode>1</documentTriggerMode>
                                <sourceFolderReferenceType>0</sourceFolderReferenceType>
                            </preconditions>
                        </component>
                    </flowObject>
                    <flowObject id="0afc428f-c762-4e2d-8275-781f412e5a88" componentType="Activity">
                        <name>Tarefa de utilizador incluída3</name>
                        <position>
                            <location x="537" y="33" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>4</implementationType>
                            <isConditional>false</isConditional>
                            <bpmnTaskType>3</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <implementation>
                                <attachedActivityId>/1.61a0bf71-b88b-4fc3-b79b-f023825ed63e</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <forceSend>true</forceSend>
                                <noTask>true</noTask>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <laneFilter id="bpdid:7b88404e5edd3729:-4942888d:16d37065276:-143b">
                                    <serviceType>1</serviceType>
                                    <teamRef>2c7ae840-cf8c-4998-839b-2cf42b6b7656/24.da7e4d23-78cb-4483-98ed-b9c238308a03</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:7b88404e5edd3729:-4942888d:16d37065276:-143a">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                    </flowObject>
                    <flowObject id="07ef1283-41ee-427a-887f-5f3ce548d0f9" componentType="Activity">
                        <name>Tarefa de utilizador incluída4</name>
                        <position>
                            <location x="769" y="53" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <bpmnTaskType>2</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <implementation>
                                <attachedActivityId>/1.bdd3fc6b-5583-46c6-9ddf-91408b409b0f</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <forceSend>true</forceSend>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <laneFilter id="bpdid:7b88404e5edd3729:-4942888d:16d37065276:-1438">
                                    <serviceType>1</serviceType>
                                    <teamRef>2c7ae840-cf8c-4998-839b-2cf42b6b7656/24.da7e4d23-78cb-4483-98ed-b9c238308a03</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:7b88404e5edd3729:-4942888d:16d37065276:-1437">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                    </flowObject>
                    <flowObject id="8ef97c1e-0f04-47c5-ae36-0009c0a05590" componentType="Event">
                        <name>Iniciar</name>
                        <documentation></documentation>
                        <position>
                            <location x="25" y="80" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#F8F8F8</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>1</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                    </flowObject>
                    <flowObject id="5cd75f8a-ab29-419b-8fbf-8fd2aa015b4f" componentType="Event">
                        <name>Terminar</name>
                        <documentation></documentation>
                        <position>
                            <location x="650" y="80" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#F8F8F8</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                    </flowObject>
                </lane>
                <lane id="7aeab881-f052-44a8-88df-555a9fbb8a1a">
                    <name>Sistema</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>true</systemLane>
                    <attachedParticipant>2c7ae840-cf8c-4998-839b-2cf42b6b7656/24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b</attachedParticipant>
                    <flowObject id="3a475195-368c-4507-8885-83c8cdf88a27" componentType="Activity">
                        <name>Tarefa de utilizador incluída5</name>
                        <position>
                            <location x="429" y="69" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>4</implementationType>
                            <isConditional>false</isConditional>
                            <bpmnTaskType>3</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <implementation>
                                <attachedActivityId>/1.7f6199ee-84e0-4fd4-aed2-6df579139f8f</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <forceSend>true</forceSend>
                                <noTask>true</noTask>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <laneFilter id="bpdid:7b88404e5edd3729:-4942888d:16d37065276:-1435">
                                    <serviceType>1</serviceType>
                                    <teamRef>2c7ae840-cf8c-4998-839b-2cf42b6b7656/24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:7b88404e5edd3729:-4942888d:16d37065276:-1434">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                    </flowObject>
                </lane>
            </pool>
            <extension id="bpdid:7b88404e5edd3729:-4942888d:16d37065276:-1433" type="CASE">
                <caseFolder id="298d9954-af07-4258-b312-02b5cf8a3743">
                    <allowLocalDoc>false</allowLocalDoc>
                    <allowExternalDoc>false</allowExternalDoc>
                    <allowSubfoldersCreation>false</allowSubfoldersCreation>
                    <allowExternalFolder>false</allowExternalFolder>
                </caseFolder>
            </extension>
        </BusinessProcessDiagram>
    </bpd>
</teamworks>

