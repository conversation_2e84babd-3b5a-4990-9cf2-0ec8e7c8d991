<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.06eaabde-33db-480a-9d65-982fa27c2eac" name="Insert IDC Request">
        <lastModified>1692620829396</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.06eaabde-33db-480a-9d65-982fa27c2eac</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.afab2391-7c99-4985-8f1a-ebb8e6aa072a</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:7ffed11b1cbfb184:129346e9:189b62f5bd4:-3ac3</guid>
        <versionId>45d0fd4f-b287-4f32-a11d-c0369b35bc14</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:f8bb29012246e138:-2ddc0b8b:18a17cdc259:19a0" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.e99b5990-b1b9-474e-80bf-00d16f480ddc"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"9d74c8cd-b368-4325-882b-54cc7732f318"},{"incoming":["c9503c22-2547-446d-8378-1b5e156396e6","a4a86a11-2a26-4804-88f5-56c752e193ca"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":730,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:7ffed11b1cbfb184:129346e9:189b62f5bd4:-3ac1"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"3de76605-8063-4595-8e96-64c6ff6b5943"},{"targetRef":"afab2391-7c99-4985-8f1a-ebb8e6aa072a","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Insert IDC SQL Query","declaredType":"sequenceFlow","id":"2027.e99b5990-b1b9-474e-80bf-00d16f480ddc","sourceRef":"9d74c8cd-b368-4325-882b-54cc7732f318"},{"startQuantity":1,"outgoing":["a1a1b090-a48b-43c7-8e55-d476b767e65b"],"incoming":["2027.e99b5990-b1b9-474e-80bf-00d16f480ddc"],"extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":95,"x":122,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Insert IDC SQL Query","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"afab2391-7c99-4985-8f1a-ebb8e6aa072a","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\ntw.local.sqlStatements[0] = new tw.object.SQLStatement();\r\ntw.local.sqlStatements[0].sql = \"INSERT INTO BPM.IDC_REQUEST_DETAILS (REQUEST_NUMBER, PARENT_REQUEST_NUMBER, BPM_INSTANCE_NUMBER, REQUEST_INITIATOR, REQUEST_BRANCH_HUB, REQUEST_NATURE_ID, REQUEST_TYPE_ID, REQUEST_DATE, REQUEST_STATE, REQUEST_STAGE, REQUEST_STATUS, REQUEST_SUB_STATUS, FLEX_CUBE_CONTRACT_NUMBER, IS_WITHDRAWEN, IMPORT_PURPOSE_ID, PAYMENT_TERMS_ID, DOCUMENTS_SOURCE_ID, PRODUCT_CATEGORY_ID, COMMODITY_DESCRIPTION, ADVANCE_PAYMENT_OUTSTANDING_AMOUNT, DOCUMENT_AMOUNT, CURRENCY, CHARGES_ACCOUNT, SOURCE_OF_FOREIGN_CURRENCY_ID, SOURCE_OF_FUNDS_ID, PAYMENT_ACCOUNT, CASH_AMOUNT_DOCUMENT_CURRENCY, CASH_AMOUNT_NO_CURRENCY, FACILITY_AMOUNT_DOCUMENT_CURRENCY, FACILITY_AMOUNT_NO_CURRENCY, DISCOUNT, AMOUNT_ADVANCED, AMOUNT_PAID_BY_OTHER_BANKS, AMOUNT_SIGHT, AMOUNT_DEFERRED_NO_AVALIZATION, AMOUNT_DEFERRED_AVALIZATION, AMOUNT_PAYABLE_BY_NBE, FIRST_INSTALLEMENT_MATURITY_DATE, NO_OF_DAYS_TILL_MATURITY, BENEFICIARY_NAME, BENEFICIARY_BANK, BENEFICIARY_IBAN, BENEFICIARY_COUNTRY_CODE, CORRESPONDENT_REF_NUM, TRADE_FO_REFERENCE_NUMBER, TRADE_FU_APPROVAL_NO, EXECUTION_HUB_CODE, EXECUTION_HUB_NAME, SHIPPING_DATE, INCOTERMS, SHIPMENT_METHOD_ID, DESTINATION_PORT, CBE_COMMODITY_CLASSIFICATION_ID, HS_CODE, HS_DESCRIPTION, ACID, COUNTRY_OF_ORIGIN_CODE) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)\";\r\n\r\ntw.local.sqlStatements[0].parameters = new tw.object.listOf.SQLParameter();\r\ntw.local.i = 0;\r\nfunction paramInit (type,value) {\r\n\ttw.local.sqlStatements[0].parameters[tw.local.i] = new tw.object.SQLParameter();\r\n\ttw.local.sqlStatements[0].parameters[tw.local.i].type = type;\r\n\ttw.local.sqlStatements[0].parameters[tw.local.i].value = value;\r\n\/\/\ttw.local.sqlStatements[0].parameters[tw.local.i].mode = \"IN\";\r\n\ttw.local.i= tw.local.i+1;\r\n\/\/\treturn tw.local.parameter;\r\n}\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.appInfo.instanceID);\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.ParentIDCRequestNumber);\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.appInfo.appID);\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.appInfo.initiator);\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.appInfo.branch.name);\r\nparamInit (\"INTEGER\",tw.local.IDCRequest.IDCRequestNature.id);\r\nparamInit (\"INTEGER\",tw.local.IDCRequest.IDCRequestType.id);\r\nparamInit (\"DATE\",tw.local.IDCRequest.requestDate);\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.IDCRequestState);\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.IDCRequestStage);\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.appInfo.status);\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.appInfo.subStatus);\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.FCContractNumber);\r\nif (tw.local.IDCRequest.isIDCWithdrawn == true) {\r\n\tparamInit (\"INTEGER\",1);\r\n\t\r\n}else{\r\n\tparamInit (\"INTEGER\",0);\r\n\t\r\n}\r\nparamInit (\"INTEGER\",tw.local.IDCRequest.importPurpose.id);\r\nparamInit (\"INTEGER\",tw.local.IDCRequest.paymentTerms.id);\r\nparamInit (\"INTEGER\",tw.local.IDCRequest.documentsSource.id);\r\nparamInit (\"INTEGER\",tw.local.IDCRequest.productCategory.id);\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.commodityDescription);\r\nif (tw.local.IDCRequest.IDCRequestType.englishdescription == \"Advance Payment\") {\r\n\tparamInit (\"DECIMAL\",tw.local.IDCRequest.financialDetails.amtPayableByNBE);\r\n\t\r\n}else{\r\n\tparamInit (\"DECIMAL\",0.0);\r\n\t\r\n}\r\nparamInit (\"DECIMAL\",tw.local.IDCRequest.financialDetails.documentAmount);\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.financialDetails.documentCurrency.code);\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.financialDetails.chargesAccount);\r\nparamInit (\"INTEGER\",tw.local.IDCRequest.financialDetails.sourceOfForeignCurrency.id);\r\nparamInit (\"INTEGER\",tw.local.IDCRequest.financialDetails.sourceOfFunds.id);\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.financialDetails.paymentAccount);\r\nparamInit (\"DECIMAL\",tw.local.IDCRequest.financialDetails.cashAmtInDocCurrency);\r\nparamInit (\"DECIMAL\",tw.local.IDCRequest.financialDetails.CashAmtWithNoCurrency);\r\nparamInit (\"DECIMAL\",tw.local.IDCRequest.financialDetails.facilityAmtInDocCurrency);\r\nparamInit (\"DECIMAL\",tw.local.IDCRequest.financialDetails.facilityAmtWithNoCurrency);\r\nparamInit (\"DECIMAL\",tw.local.IDCRequest.financialDetails.discountAmt);\r\nparamInit (\"DECIMAL\",tw.local.IDCRequest.financialDetails.amountAdvanced);\r\nparamInit (\"DECIMAL\",tw.local.IDCRequest.financialDetails.amtPaidbyOtherBanks);\r\nparamInit (\"DECIMAL\",tw.local.IDCRequest.financialDetails.amtSight);\r\nparamInit (\"DECIMAL\",tw.local.IDCRequest.financialDetails.amtDeferredNoAvalized);\r\nparamInit (\"DECIMAL\",tw.local.IDCRequest.financialDetails.amtDeferredAvalized);\r\nparamInit (\"DECIMAL\",tw.local.IDCRequest.financialDetails.amtPayableByNBE);\r\nparamInit (\"DATE\",tw.local.IDCRequest.financialDetails.firstInstallementMaturityDate);\r\nparamInit (\"INTEGER\",tw.local.IDCRequest.financialDetails.daysTillMaturity);\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.financialDetails.beneficiaryDetails.name);\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.financialDetails.beneficiaryDetails.bank);\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.financialDetails.beneficiaryDetails.account);\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.financialDetails.beneficiaryDetails.country.code);\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.financialDetails.beneficiaryDetails.correspondentRefNum);\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.financialDetails.tradeFOReferenceNumber);\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.financialDetails.tradeFinanceApprovalNumber);\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.financialDetails.executionHub.code);\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.financialDetails.executionHub.arabicdescription);\r\nparamInit (\"DATE\",tw.local.IDCRequest.productsDetails.shippingDate);\r\nif (tw.local.IDCRequest.productsDetails.incoterms.id == 0) {\r\n\tparamInit (\"INTEGER\",null);\r\n}else{\r\n\tparamInit (\"INTEGER\",tw.local.IDCRequest.productsDetails.incoterms.id);\r\n}\r\n\r\nif (tw.local.IDCRequest.productsDetails.shipmentMethod.id == 0) {\r\n\tparamInit (\"INTEGER\",null);\r\n}else{\r\n\tparamInit (\"INTEGER\",tw.local.IDCRequest.productsDetails.shipmentMethod.id);\r\n}\r\n\r\n\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.productsDetails.destinationPort);\r\nif (tw.local.IDCRequest.productsDetails.CBECommodityClassification.id == 0) {\r\n\tparamInit (\"INTEGER\",null);\r\n}else{\r\n\tparamInit (\"INTEGER\",tw.local.IDCRequest.productsDetails.CBECommodityClassification.id);\r\n\t}\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.productsDetails.HSProduct.code);\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.productsDetails.HSProduct.englishdescription);\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.productsDetails.ACID);\r\nparamInit (\"VARCHAR\",tw.local.IDCRequest.countryOfOrigin.code);\r\n\/\/-------------------------------------------------------------------------------------------\r\ntw.local.sqlStatements[1] = new tw.object.SQLStatement();\r\ntw.local.sqlStatements[1].sql = \"SELECT ID DBID FROM BPM.IDC_REQUEST_DETAILS WHERE REQUEST_NUMBER = ?\";\r\ntw.local.sqlStatements[1].parameters = new tw.object.listOf.SQLParameter();\r\n\r\ntw.local.sqlStatements[1].parameters[0] = new tw.object.SQLParameter();\r\ntw.local.sqlStatements[1].parameters[0].type = \"VARCHAR\";\r\ntw.local.sqlStatements[1].parameters[0].value = tw.local.IDCRequest.appInfo.instanceID;\r\n\r\n"]}},{"targetRef":"e8b6e7e2-5ab2-43bc-82c7-0fc1742374f8","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Service","declaredType":"sequenceFlow","id":"a1a1b090-a48b-43c7-8e55-d476b767e65b","sourceRef":"afab2391-7c99-4985-8f1a-ebb8e6aa072a"},{"startQuantity":1,"outgoing":["90fadd6c-9ce4-4ef0-8fbe-c7671842b276"],"incoming":["a1a1b090-a48b-43c7-8e55-d476b767e65b"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":266,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"SQL Execute Multiple Statements","dataInputAssociation":[{"targetRef":"2055.c023a0b3-e6e5-47a6-a920-04d6b2f8dc26","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","declaredType":"TFormalExpression","content":["tw.local.sqlStatements"]}}]},{"targetRef":"2055.c007e1f5-b9cb-4b02-bf74-bc982112aade","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE"]}}]},{"targetRef":"2055.628ceac6-aa42-426b-97c7-540674f12f38","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Record\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"e8b6e7e2-5ab2-43bc-82c7-0fc1742374f8","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.24961b69-3b9b-4311-8707-6b6dfffaf207"]}],"calledElement":"1.89ee1e72-5c6b-44ed-9e55-158a6cb613af"},{"targetRef":"8407a918-9889-424a-888f-1868e8f548b1","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:a93dd992dce2d4cc:-381cc70d:115a5e00b05:-7fdb"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Insert Subtypes SQL Queries","declaredType":"sequenceFlow","id":"90fadd6c-9ce4-4ef0-8fbe-c7671842b276","sourceRef":"e8b6e7e2-5ab2-43bc-82c7-0fc1742374f8"},{"itemSubjectRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","name":"sqlStatements","isCollection":true,"declaredType":"dataObject","id":"2056.2ed2ec9d-3a30-4d5e-88fc-a478f50a01cf"},{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":true,"declaredType":"dataObject","id":"2056.58801473-2b62-4ec7-8fc2-c418250c60e3"},{"itemSubjectRef":"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c","name":"parameter","isCollection":true,"declaredType":"dataObject","id":"2056.1128deb1-0960-4884-812e-9d92dfcbb40f"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"i","isCollection":false,"declaredType":"dataObject","id":"2056.77d9af18-6d7e-4aec-8ea8-d2c8ef142df9"},{"startQuantity":1,"outgoing":["1cc559b9-e906-463a-8c03-e1f39297e2ef"],"incoming":["90fadd6c-9ce4-4ef0-8fbe-c7671842b276"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":418,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Insert Subtypes SQL Queries","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"8407a918-9889-424a-888f-1868e8f548b1","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.IDCRequest.DBID = tw.local.results[0].DBID;\r\n\r\ntw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\nvar j = 0\r\nfunction paramInit (type,value) {\r\n\ttw.local.sqlStatements[j].parameters[tw.local.i] = new tw.object.SQLParameter();\r\n\ttw.local.sqlStatements[j].parameters[tw.local.i].type = type;\r\n\ttw.local.sqlStatements[j].parameters[tw.local.i].value = value;\r\n\ttw.local.i= tw.local.i+1;\r\n}\r\n\/\/-----------------------------invoice--------------------------------------------------\r\n\r\nfor (j=0; j&lt;tw.local.IDCRequest.invoices.listLength; j++) {\r\n\ttw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\n\ttw.local.sqlStatements[j].sql = \"INSERT INTO BPM.IDC_REQUEST_INVOICES (IDC_REQUEST_ID, INVOICE_NUMBER,INVOICE_DATE) VALUES (?,?,?) ;\";\r\n\t\r\n\ttw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\n\ttw.local.i = 0;\r\n\t\r\n\tparamInit(\"INTEGER\",tw.local.IDCRequest.DBID);\r\n\tparamInit(\"VARCHAR\",tw.local.IDCRequest.invoices[j].number);\r\n\tparamInit(\"DATE\",tw.local.IDCRequest.invoices[j].date);\r\n}\r\n\r\n\/\/\/\/-----------------------------customer---------------------------------------------------\r\n\/\/j+=1;\r\n\ttw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\n\ttw.local.sqlStatements[j].sql = \"INSERT INTO BPM.IDC_CUSTOMER_INFORMATION (IDC_REQUEST_ID, CIF, CUSTOMER_NAME, CUSTOMER_SECTOR, CUSTOMER_TYPE, CUSTOMER_NUMBER_AT_CBE, FACILITY_TYPE_ID, COMMERCIAL_REGISTRATION_NUMBER, COMMERCIAL_REGISTRATION_OFFICE, TAX_CARD_NUMBER, IMPORT_CARD_NUMBER) VALUES (?,?,?,?,?,?,?,?,?,?,?) ;\";\r\n\t\r\n\ttw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\n\ttw.local.i = 0;\r\n\t\r\n\tparamInit(\"INTEGER\",tw.local.IDCRequest.DBID);\r\n\tparamInit(\"VARCHAR\",tw.local.IDCRequest.customerInformation.CIFNumber);\r\n\tparamInit(\"VARCHAR\",tw.local.IDCRequest.customerInformation.customerName);\r\n\tparamInit(\"VARCHAR\",tw.local.IDCRequest.customerInformation.customerSector);\/\/can be update later &lt;----------------------------------------------------\r\n\tparamInit(\"VARCHAR\",tw.local.IDCRequest.customerInformation.customerType);\r\n\tparamInit(\"VARCHAR\",tw.local.IDCRequest.customerInformation.CBENumber);\r\n\tparamInit(\"INTEGER\",tw.local.IDCRequest.customerInformation.facilityType.id);\r\n\tparamInit(\"VARCHAR\",tw.local.IDCRequest.customerInformation.commercialRegistrationNumber);\r\n\tparamInit(\"VARCHAR\",tw.local.IDCRequest.customerInformation.commercialRegistrationOffice);\r\n\tparamInit(\"VARCHAR\",tw.local.IDCRequest.customerInformation.taxCardNumber);\r\n\tparamInit(\"VARCHAR\",tw.local.IDCRequest.customerInformation.importCardNumber);\r\n\/\/-----------------------------bill-----------------------------------------------------\r\nvar n = 0;\r\nj++;\r\nfor (; j&lt;(tw.local.IDCRequest.billOfLading.listLength + tw.local.IDCRequest.invoices.listLength+1); j++) {\r\n\ttw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\n\ttw.local.sqlStatements[j].sql = \"INSERT INTO BPM.IDC_REQUEST_BILLS (IDC_REQUEST_ID, BILL_OF_LADING_REF, BILL_OF_LADING_DATE) VALUES (?,?,?) ;\";\r\n\t\r\n\ttw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\n\ttw.local.i = 0;\r\n\t\r\n\tparamInit(\"INTEGER\",tw.local.IDCRequest.DBID);\r\n\tparamInit(\"VARCHAR\",tw.local.IDCRequest.billOfLading[n].number);\r\n\tparamInit(\"DATE\",tw.local.IDCRequest.billOfLading[n].date);\r\n\tn+=1;\r\n}\r\n\/\/-----------------------------------------facility-----------------------------------------------------------\r\n\r\nif (tw.local.idcContract.facilities.listLength &gt; 0) {\r\n\tfor (var m_f_len=0; m_f_len &lt; tw.local.idcContract.facilities.listLength; m_f_len++) {\r\n\t\tfor (var s_f_len=0; s_f_len &lt; tw.local.idcContract.facilities[m_f_len].facilityLines.listLength; s_f_len++) {\r\n\t\t\ttw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\n\t\t\ttw.local.sqlStatements[j].sql = \"INSERT INTO BPM.IDC_FACILITY_DETAILS (IDC_REQUEST_ID, FACILITY_ID, CURRENCY, LINE_SERIAL, LINE_CODE, FACILITY_PERCENTAGE_TO_BOOK) VALUES (?,?,?,?,?,?) ;\";\r\n\t\t\t\r\n\t\t\ttw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\n\t\t\ttw.local.i = 0;\r\n\t\t\tparamInit(\"INTEGER\",tw.local.IDCRequest.DBID);\r\n\t\t\tparamInit(\"VARCHAR\",tw.local.idcContract.facilities[m_f_len].facilityID);\r\n\t\t\tparamInit(\"VARCHAR\",tw.local.idcContract.facilities[m_f_len].facilityCurrency.value);\r\n\t\t\tparamInit(\"VARCHAR\",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].lineSerialNumber);\r\n\t\t\tparamInit(\"VARCHAR\",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].lineCode);\r\n\t\t\tparamInit(\"DECIMAL\",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].facilityPercentageToBook);\r\n\t\t\tj++;\r\n\t\t}\r\n\t}\r\n}\r\n"]}},{"targetRef":"7e5a39e8-986f-4c70-88eb-bfdd7e4bf7dd","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To SQL Execute Statement","declaredType":"sequenceFlow","id":"1cc559b9-e906-463a-8c03-e1f39297e2ef","sourceRef":"8407a918-9889-424a-888f-1868e8f548b1"},{"startQuantity":1,"outgoing":["c9503c22-2547-446d-8378-1b5e156396e6"],"incoming":["1cc559b9-e906-463a-8c03-e1f39297e2ef"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":554,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"SQL Execute Multiple SubStatements","dataInputAssociation":[{"targetRef":"2055.c023a0b3-e6e5-47a6-a920-04d6b2f8dc26","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","declaredType":"TFormalExpression","content":["tw.local.sqlStatements"]}}]},{"targetRef":"2055.c007e1f5-b9cb-4b02-bf74-bc982112aade","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE"]}}]},{"targetRef":"2055.628ceac6-aa42-426b-97c7-540674f12f38","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Record\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"7e5a39e8-986f-4c70-88eb-bfdd7e4bf7dd","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.24961b69-3b9b-4311-8707-6b6dfffaf207"]}],"calledElement":"1.89ee1e72-5c6b-44ed-9e55-158a6cb613af"},{"targetRef":"3de76605-8063-4595-8e96-64c6ff6b5943","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:a93dd992dce2d4cc:-381cc70d:115a5e00b05:-7fdb"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"c9503c22-2547-446d-8378-1b5e156396e6","sourceRef":"7e5a39e8-986f-4c70-88eb-bfdd7e4bf7dd"},{"parallelMultiple":false,"outgoing":["abc25256-be94-4f29-867f-81c649c93002"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2d532afd-0077-4918-8158-94387ae83097"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"965516d6-3b07-4b8c-87af-189546741935","otherAttributes":{"eventImplId":"86b0f24e-c15a-4c03-8b50-c13c441b81b3"}}],"attachedToRef":"8407a918-9889-424a-888f-1868e8f548b1","extensionElements":{"nodeVisualInfo":[{"width":24,"x":453,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"f64a8cd2-a394-4a23-82ee-b14ea1191de0","outputSet":{}},{"parallelMultiple":false,"outgoing":["3978a550-8f28-420b-8ae8-f999909d5aca"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"b8e62b2d-5aa6-4dba-8207-2fc099671783"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"2e5f4524-2562-4dff-8123-3234062dd264","otherAttributes":{"eventImplId":"60822cef-938d-49ea-8826-0916d59bd87c"}}],"attachedToRef":"7e5a39e8-986f-4c70-88eb-bfdd7e4bf7dd","extensionElements":{"nodeVisualInfo":[{"width":24,"x":589,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error3","declaredType":"boundaryEvent","id":"a5aba410-d234-4944-8162-08cd3f682bf8","outputSet":{}},{"targetRef":"1e446a8b-6a77-4c2a-8f3e-c196734f0b4c","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightTop","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"3978a550-8f28-420b-8ae8-f999909d5aca","sourceRef":"a5aba410-d234-4944-8162-08cd3f682bf8"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.c7ff887e-6208-4bf0-8a5e-31bf5cda1e89"},{"parallelMultiple":false,"outgoing":["a4abec46-1e7c-46b7-8157-377781e0bd3d"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"e2aab190-2ec4-48e4-802f-2d1872265439"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"b7822844-da2c-4b93-84e5-9772d0aacdbc","otherAttributes":{"eventImplId":"2f98a257-9500-46d4-8b8f-a7a28f5e68e3"}}],"attachedToRef":"afab2391-7c99-4985-8f1a-ebb8e6aa072a","extensionElements":{"nodeVisualInfo":[{"width":24,"x":157,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"ff2e1a37-3640-4c8c-84be-96bbbe494039","outputSet":{}},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.4ca37604-a434-4b56-8604-141d293722d6"},{"targetRef":"3de76605-8063-4595-8e96-64c6ff6b5943","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"a4a86a11-2a26-4804-88f5-56c752e193ca","sourceRef":"1e446a8b-6a77-4c2a-8f3e-c196734f0b4c"},{"parallelMultiple":false,"outgoing":["20fd333c-d6e6-44ad-8b39-ff3a11bc2558"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"ddbe9f69-2a1c-489d-8a00-56db4e079ffc"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"50e7391e-a996-46bb-832a-2df4a6eb2218","otherAttributes":{"eventImplId":"d24982ef-beef-4d94-8e91-97a4b84a6970"}}],"attachedToRef":"e8b6e7e2-5ab2-43bc-82c7-0fc1742374f8","extensionElements":{"nodeVisualInfo":[{"width":24,"x":301,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error4","declaredType":"boundaryEvent","id":"5eeec8b0-ae9f-4c90-8054-3040b952678f","outputSet":{}},{"targetRef":"1e446a8b-6a77-4c2a-8f3e-c196734f0b4c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Script Task","declaredType":"sequenceFlow","id":"20fd333c-d6e6-44ad-8b39-ff3a11bc2558","sourceRef":"5eeec8b0-ae9f-4c90-8054-3040b952678f"},{"targetRef":"1e446a8b-6a77-4c2a-8f3e-c196734f0b4c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Script Task","declaredType":"sequenceFlow","id":"a4abec46-1e7c-46b7-8157-377781e0bd3d","sourceRef":"ff2e1a37-3640-4c8c-84be-96bbbe494039"},{"targetRef":"1e446a8b-6a77-4c2a-8f3e-c196734f0b4c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Script Task","declaredType":"sequenceFlow","id":"abc25256-be94-4f29-867f-81c649c93002","sourceRef":"f64a8cd2-a394-4a23-82ee-b14ea1191de0"},{"startQuantity":1,"outgoing":["a4a86a11-2a26-4804-88f5-56c752e193ca"],"incoming":["3978a550-8f28-420b-8ae8-f999909d5aca","abc25256-be94-4f29-867f-81c649c93002","20fd333c-d6e6-44ad-8b39-ff3a11bc2558","a4abec46-1e7c-46b7-8157-377781e0bd3d"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":446,"y":210,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"1e446a8b-6a77-4c2a-8f3e-c196734f0b4c","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error = new tw.object.AjaxError();\r\nif (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n}\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG+\"\";"]}}],"laneSet":[{"id":"b9f68ef0-f3c6-48f7-81d8-add748f37635","lane":[{"flowNodeRef":["9d74c8cd-b368-4325-882b-54cc7732f318","3de76605-8063-4595-8e96-64c6ff6b5943","afab2391-7c99-4985-8f1a-ebb8e6aa072a","e8b6e7e2-5ab2-43bc-82c7-0fc1742374f8","8407a918-9889-424a-888f-1868e8f548b1","7e5a39e8-986f-4c70-88eb-bfdd7e4bf7dd","f64a8cd2-a394-4a23-82ee-b14ea1191de0","a5aba410-d234-4944-8162-08cd3f682bf8","ff2e1a37-3640-4c8c-84be-96bbbe494039","5eeec8b0-ae9f-4c90-8054-3040b952678f","1e446a8b-6a77-4c2a-8f3e-c196734f0b4c"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"e7ed2c1d-d1ca-46fd-8747-9ae2bcee0654","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Insert IDC Request","declaredType":"process","id":"1.06eaabde-33db-480a-9d65-982fa27c2eac","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"IDCRequest","isCollection":false,"id":"2055.2fadd4c3-4741-4fce-8d56-2d8b8fbc8ccb"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.5d7794f2-995a-47eb-8a0c-755c73dc5d01"}],"inputSet":[{"dataInputRefs":["2055.6287bfa2-c4da-4ec3-842c-a164e045a461","2055.e4ea1176-3273-4117-87c0-b882c7df46c4"]}],"outputSet":[{"dataOutputRefs":["2055.2fadd4c3-4741-4fce-8d56-2d8b8fbc8ccb","2055.5d7794f2-995a-47eb-8a0c-755c73dc5d01"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = new tw.object.IDCRequest();\nautoObject.IDCRequestState = \"Final\";\nautoObject.commodityDescription = \"commo\";\nautoObject.countryOfOrigin = new tw.object.DBLookup();\n\/\/autoObject.countryOfOrigin.id = 1;\n\/\/autoObject.countryOfOrigin.code = \"EG\";\n\/\/autoObject.countryOfOrigin.arabicdescription = \"\u0645\u0635\u0631\";\n\/\/autoObject.countryOfOrigin.englishdescription = \"\u064fEgypt\";\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\nautoObject.appInfo.requestDate = \"12-08-2023\";\nautoObject.appInfo.status = \"status\";\nautoObject.appInfo.subStatus = \"subStatus\";\nautoObject.appInfo.initiator = \"initiator\";\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.appInfo.branch.name = \"001\";\nautoObject.appInfo.branch.value = \"001\";\nautoObject.appInfo.requestName = \"ICAP\";\nautoObject.appInfo.requestType = \"ICAP\";\nautoObject.appInfo.stepName = \"stepName\";\nautoObject.appInfo.appRef = \"appRef\";\nautoObject.appInfo.appID = \"85634\";\nautoObject.appInfo.instanceID = \"00102230000220\";\nautoObject.productsDetails = new tw.object.ProductsDetails();\nautoObject.productsDetails.destinationPort = \"destinationPort\";\nautoObject.productsDetails.shippingDate = new TWDate();\nautoObject.productsDetails.HSProduct = new tw.object.DBLookup();\nautoObject.productsDetails.HSProduct.id = 1;\nautoObject.productsDetails.HSProduct.code = \"0101 \u2013 0106\";\nautoObject.productsDetails.HSProduct.arabicdescription = \"arabic\";\nautoObject.productsDetails.HSProduct.englishdescription = \"Live animals\";\nautoObject.productsDetails.incoterms = new tw.object.DBLookup();\nautoObject.productsDetails.incoterms.id = 2;\nautoObject.productsDetails.incoterms.code = \"FCA\";\nautoObject.productsDetails.incoterms.arabicdescription = \"arabicdon\";\nautoObject.productsDetails.incoterms.englishdescription = \"Free to Carrier\";\nautoObject.productsDetails.ACID = \"ACID\";\nautoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();\nautoObject.productsDetails.CBECommodityClassification.id = 3;\nautoObject.productsDetails.CBECommodityClassification.code = \"003\";\nautoObject.productsDetails.CBECommodityClassification.arabicdescription = \"\u0627\u0644\u0627\u0644\u0627\u062a \u0648 \u0627\u0644\u0645\u0639\u062f\u0627\u062a\";\nautoObject.productsDetails.CBECommodityClassification.englishdescription = \"english\";\nautoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();\nautoObject.productsDetails.shipmentMethod.id = 4;\nautoObject.productsDetails.shipmentMethod.code = \"004\";\nautoObject.productsDetails.shipmentMethod.arabicdescription = \"\u0628\u0631\u064a\u062f \u0633\u0631\u064a\u0639\";\nautoObject.productsDetails.shipmentMethod.englishdescription = \"english\";\nautoObject.financialDetails = new tw.object.FinancialDetails();\nautoObject.financialDetails.isAdvancePaymentsUsed = false;\nautoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();\nautoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();\nautoObject.financialDetails.paymentTerms[0].installmentAmount = 1.1;\nautoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();\nautoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();\nautoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();\nautoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency =1.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id =1;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = \"EGP\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = \"\u062c\u0646\u064a\u0647 \u0645\u0635\u0631\u0649\";\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = \"Egyptian Pound\";\nautoObject.financialDetails.usedAdvancePayment[0].AmountAllocated =1.0;\nautoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = \"advance\";\nautoObject.financialDetails.usedAdvancePayment[0].outstandingAmount =1.0;\nautoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = \"invoicer\";\nautoObject.financialDetails.usedAdvancePayment[0].paidAmount =1.0;\nautoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = \"benefic\";\nautoObject.financialDetails.usedAdvancePayment[0].documentAmount =1.0;\nautoObject.financialDetails.discountAmt =77.0;\nautoObject.financialDetails.firstInstallementMaturityDate = new TWDate();\nautoObject.financialDetails.facilityAmtInDocCurrency =1.0;\nautoObject.financialDetails.amtSight =1.0;\nautoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();\nautoObject.financialDetails.beneficiaryDetails.account = \"account\";\nautoObject.financialDetails.beneficiaryDetails.bank = \"bank\";\nautoObject.financialDetails.beneficiaryDetails.correspondentRefNum = \"corresp\";\nautoObject.financialDetails.beneficiaryDetails.name = \"beneficiary\";\nautoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();\nautoObject.financialDetails.beneficiaryDetails.country.id =1;\nautoObject.financialDetails.beneficiaryDetails.country.code = \"EG\";\nautoObject.financialDetails.beneficiaryDetails.country.arabicdescription = \"\u0645\u0635\u0631\";\nautoObject.financialDetails.beneficiaryDetails.country.englishdescription = \"Egypt\";\nautoObject.financialDetails.amtDeferredNoAvalized =1.0;\nautoObject.financialDetails.amtPayableByNBE =1.0;\nautoObject.financialDetails.executionHub = new tw.object.DBLookup();\nautoObject.financialDetails.executionHub.id =1;\nautoObject.financialDetails.executionHub.code = \"077\";\nautoObject.financialDetails.executionHub.arabicdescription = \"\u0648\u062d\u062f\u0629 \u0627\u0639\u062a\u0645\u0627\u062f\u0627\u062a \u0627\u0644\u0628\u0631\u062c\";\nautoObject.financialDetails.executionHub.englishdescription = \"english\";\nautoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();\nautoObject.financialDetails.sourceOfForeignCurrency.id =1;\nautoObject.financialDetails.sourceOfForeignCurrency.code = \"001\";\nautoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = \"\u0644\u0627 \u064a\u0648\u062c\u062f \u0639\u0645\u0644\u0629 \u0627\u062c\u0646\u0628\u064a\u0629\";\nautoObject.financialDetails.sourceOfForeignCurrency.englishdescription = \"No Foreign Currency\";\nautoObject.financialDetails.facilityAmtWithNoCurrency =1.0;\nautoObject.financialDetails.documentCurrency = new tw.object.DBLookup();\nautoObject.financialDetails.documentCurrency.id =1;\nautoObject.financialDetails.documentCurrency.code = \"EGP\";\nautoObject.financialDetails.documentCurrency.arabicdescription = \"\u062c\u0646\u064a\u0647 \u0645\u0635\u0631\u0649\";\nautoObject.financialDetails.documentCurrency.englishdescription = \"Egyptian Pound\";\nautoObject.financialDetails.daysTillMaturity =1;\nautoObject.financialDetails.tradeFinanceApprovalNumber = \"12345\";\nautoObject.financialDetails.amountAdvanced =1.0;\nautoObject.financialDetails.paymentAccount = \"paymentAccount\";\nautoObject.financialDetails.tradeFOReferenceNumber = \"123\";\nautoObject.financialDetails.documentAmount =1.0;\nautoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();\nautoObject.financialDetails.sourceOfFunds.id =1;\nautoObject.financialDetails.sourceOfFunds.code = \"001\";\nautoObject.financialDetails.sourceOfFunds.arabicdescription = \"\u0645\u0648\u0627\u0631\u062f \u0627\u0644\u0639\u0645\u064a\u0644 \u0627\u0644\u0630\u0627\u062a\u064a\u0629\";\nautoObject.financialDetails.sourceOfFunds.englishdescription = \"english\";\nautoObject.financialDetails.chargesAccount = \"charges\";\nautoObject.financialDetails.CashAmtWithNoCurrency =1.0;\nautoObject.financialDetails.amtDeferredAvalized =1.0;\nautoObject.financialDetails.amtPaidbyOtherBanks =1.0;\nautoObject.financialDetails.cashAmtInDocCurrency =1.0;\nautoObject.IDCRequestType = new tw.object.DBLookup();\nautoObject.IDCRequestType.id =1;\nautoObject.IDCRequestType.code = \"code\";\nautoObject.IDCRequestType.arabicdescription = \"arabic\";\nautoObject.IDCRequestType.englishdescription = \"english\";\nautoObject.isIDCWithdrawn = false;\nautoObject.IDCRequestStage = \"Stage\";\nautoObject.FCContractNumber = \"FC\";\nautoObject.billOfLading = new tw.object.listOf.Invoice();\nautoObject.billOfLading[0] = new tw.object.Invoice();\nautoObject.billOfLading[0].date = new TWDate();\nautoObject.billOfLading[0].number = \"123\";\r\nautoObject.billOfLading[1] = new tw.object.Invoice();\r\nautoObject.billOfLading[1].date = new TWDate();\r\nautoObject.billOfLading[1].number = \"123\";\nautoObject.importPurpose = new tw.object.DBLookup();\nautoObject.importPurpose.id =1;\nautoObject.importPurpose.code = \"001\";\nautoObject.importPurpose.arabicdescription = \"\u0627\u0633\u062a\u064a\u0631\u0627\u062f \u0633\u0644\u0639\u0649\";\nautoObject.importPurpose.englishdescription = \"Commodity Import\";\nautoObject.IDCRequestNature = new tw.object.DBLookup();\nautoObject.IDCRequestNature.id =1;\nautoObject.IDCRequestNature.code = \"1\";\nautoObject.IDCRequestNature.arabicdescription = \"\u0627\u0635\u062f\u0631 \u062c\u062f\u064a\u062f\";\nautoObject.IDCRequestNature.englishdescription = \"New Request\";\nautoObject.customerInformation = new tw.object.CustomerInformation();\nautoObject.customerInformation.CIFNumber = \"02366014\";\nautoObject.customerInformation.importCardNumber = \"import\";\nautoObject.customerInformation.commercialRegistrationNumber = \"commercial\";\nautoObject.customerInformation.customerName = \"customerName\";\nautoObject.customerInformation.isCustomeSanctionedbyCBE = \"\";\nautoObject.customerInformation.CBENumber = \"cbe\";\nautoObject.customerInformation.customerSector = \"rSector\";\nautoObject.customerInformation.facilityType = new tw.object.DBLookup();\r\nautoObject.customerInformation.facilityType.id = 1;\r\nautoObject.customerInformation.facilityType.arabicdescription= \"\u0644\u0627 \u064a\u062a\u0645\u062a\u0639 \u0628\u062a\u0633\u0647\u064a\u0644\u0627\u062a\";\nautoObject.customerInformation.commercialRegistrationOffice = \"Office\";\nautoObject.customerInformation.taxCardNumber = \"taxCardNumber\";\nautoObject.customerInformation.customerType = \"cType\";\nautoObject.customerInformation.addressLine1 = \"1\";\nautoObject.customerInformation.addressLine2 = \"2\";\nautoObject.invoices = new tw.object.listOf.Invoice();\nautoObject.invoices[0] = new tw.object.Invoice();\nautoObject.invoices[0].date = new TWDate();\nautoObject.invoices[0].number = \"number\";\r\nautoObject.invoices[1] = new tw.object.Invoice();\r\nautoObject.invoices[1].date = new TWDate();\r\nautoObject.invoices[1].number = \"number2\";\nautoObject.productCategory = new tw.object.DBLookup();\nautoObject.productCategory.id =1;\nautoObject.productCategory.code = \"001\";\nautoObject.productCategory.arabicdescription = \"arabic\";\nautoObject.productCategory.englishdescription = \"Automotive industry\";\nautoObject.documentsSource = new tw.object.DBLookup();\nautoObject.documentsSource.id =1;\nautoObject.documentsSource.code = \"001\";\nautoObject.documentsSource.arabicdescription = \"arabic\";\nautoObject.documentsSource.englishdescription = \"Automotive industry\";\nautoObject.ParentIDCRequestNumber = \"Paren\";\nautoObject.paymentTerms = new tw.object.DBLookup();\nautoObject.paymentTerms.id =1;\nautoObject.paymentTerms.code = \"001\";\nautoObject.paymentTerms.arabicdescription = \"\u0627\u0637\u0644\u0627\u0639\";\nautoObject.paymentTerms.englishdescription = \"Sight\";\nautoObject.approvals = new tw.object.Approvals();\nautoObject.approvals.CAD = false;\nautoObject.approvals.treasury = false;\nautoObject.approvals.compliance = false;\nautoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();\nautoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();\nautoObject.appLog[0].startTime = new TWDate();\nautoObject.appLog[0].endTime = new TWDate();\nautoObject.appLog[0].userName = \"userName\";\nautoObject.appLog[0].role = \"role\";\nautoObject.appLog[0].step = \"step\";\nautoObject.appLog[0].action = \"action\";\nautoObject.appLog[0].comment = \"comment\";\nautoObject.appLog[0].terminateReason = \"terminateReason\";\nautoObject.appLog[0].returnReason = \"returnReason\";\nautoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();\nautoObject.stepLog.startTime = new TWDate();\nautoObject.stepLog.endTime = new TWDate();\nautoObject.stepLog.userName = \"userName\";\nautoObject.stepLog.role = \"role\";\nautoObject.stepLog.step = \"step\";\nautoObject.stepLog.action = \"action\";\nautoObject.stepLog.comment = \"comment\";\nautoObject.stepLog.terminateReason = \"terminateReason\";\nautoObject.stepLog.returnReason = \"returnReason\";\nautoObject.DBID =0;\nautoObject.requestDate = new TWDate();\nautoObject"}]},"itemSubjectRef":"itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67","name":"IDCRequest","isCollection":false,"id":"2055.6287bfa2-c4da-4ec3-842c-a164e045a461"},{"itemSubjectRef":"itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1","name":"idcContract","isCollection":false,"id":"2055.e4ea1176-3273-4117-87c0-b882c7df46c4"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="IDCRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.6287bfa2-c4da-4ec3-842c-a164e045a461</processParameterId>
            <processId>1.06eaabde-33db-480a-9d65-982fa27c2eac</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>975400c4-dd9f-4b34-88b4-6718a40ca0c1</guid>
            <versionId>e0e8df24-37df-41fb-8633-fe90528e8b1d</versionId>
        </processParameter>
        <processParameter name="idcContract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.e4ea1176-3273-4117-87c0-b882c7df46c4</processParameterId>
            <processId>1.06eaabde-33db-480a-9d65-982fa27c2eac</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>09157e28-2476-4ede-85c5-dc2df3765cc5</guid>
            <versionId>2cba46a3-7edf-4f92-9c9d-06585e609504</versionId>
        </processParameter>
        <processParameter name="IDCRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.2fadd4c3-4741-4fce-8d56-2d8b8fbc8ccb</processParameterId>
            <processId>1.06eaabde-33db-480a-9d65-982fa27c2eac</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>169c0ef8-3d11-49cb-96b4-777882ad6907</guid>
            <versionId>95814560-8b69-4ac2-80cc-51cec9d9bf0a</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5d7794f2-995a-47eb-8a0c-755c73dc5d01</processParameterId>
            <processId>1.06eaabde-33db-480a-9d65-982fa27c2eac</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>3d64e90c-ec9b-41da-a6e4-af3363911d6c/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>9bc8ce50-3ac1-4c97-8318-93b17c7a7f6d</guid>
            <versionId>2be67862-fc32-4415-b68d-55bb08a151c0</versionId>
        </processParameter>
        <processVariable name="sqlStatements">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2ed2ec9d-3a30-4d5e-88fc-a478f50a01cf</processVariableId>
            <description isNull="true" />
            <processId>1.06eaabde-33db-480a-9d65-982fa27c2eac</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3990d8b4-983d-4250-bd06-3600f527fed0</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9d04dbc0-e5a6-4f2c-88f2-320b55703bd9</guid>
            <versionId>6f1ca42f-a68b-461c-a8a4-ccc36573414c</versionId>
        </processVariable>
        <processVariable name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.58801473-2b62-4ec7-8fc2-c418250c60e3</processVariableId>
            <description isNull="true" />
            <processId>1.06eaabde-33db-480a-9d65-982fa27c2eac</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>abfadbc3-491e-4657-978b-76b44c3d523e</guid>
            <versionId>7471b9f5-57cb-40d7-bf49-59494ffbe48d</versionId>
        </processVariable>
        <processVariable name="parameter">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.1128deb1-0960-4884-812e-9d92dfcbb40f</processVariableId>
            <description isNull="true" />
            <processId>1.06eaabde-33db-480a-9d65-982fa27c2eac</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0eef93d2-4598-4a56-a457-f548725e3dda</guid>
            <versionId>849d5400-741c-4341-82c6-c6b36a32ceb6</versionId>
        </processVariable>
        <processVariable name="i">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.77d9af18-6d7e-4aec-8ea8-d2c8ef142df9</processVariableId>
            <description isNull="true" />
            <processId>1.06eaabde-33db-480a-9d65-982fa27c2eac</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a42b77a2-632e-40f1-903b-5c5e7ef40f3c</guid>
            <versionId>09a0eeeb-0086-49c8-a8ed-3efe64b226a0</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c7ff887e-6208-4bf0-8a5e-31bf5cda1e89</processVariableId>
            <description isNull="true" />
            <processId>1.06eaabde-33db-480a-9d65-982fa27c2eac</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>""</defaultValue>
            <guid>2ed0c747-7c21-4834-a8b3-88430e0e51f2</guid>
            <versionId>*************-450e-a3aa-ca4bb8c0eec2</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4ca37604-a434-4b56-8604-141d293722d6</processVariableId>
            <description isNull="true" />
            <processId>1.06eaabde-33db-480a-9d65-982fa27c2eac</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>4eb39989-c5fb-47b4-9127-ad1aa4599590/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a2ee2a4f-f983-4ccf-bd61-71263a3d4250</guid>
            <versionId>37545e78-2229-47e2-904b-79dabd3f6365</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1e446a8b-6a77-4c2a-8f3e-c196734f0b4c</processItemId>
            <processId>1.06eaabde-33db-480a-9d65-982fa27c2eac</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.7b7c62b0-4537-417a-83ef-269786ea40de</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3edf</guid>
            <versionId>30c7ddde-5e13-4045-8278-f9b391db9dcd</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="446" y="210">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.7b7c62b0-4537-417a-83ef-269786ea40de</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"";</script>
                <isRule>false</isRule>
                <guid>dbc6ffdf-ffcb-433e-8a35-85d91ad679fc</guid>
                <versionId>ac8e060b-c8f1-412a-8cf1-87af769b0814</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8407a918-9889-424a-888f-1868e8f548b1</processItemId>
            <processId>1.06eaabde-33db-480a-9d65-982fa27c2eac</processId>
            <name>Insert Subtypes SQL Queries</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.0030f588-5eb9-4c96-ae3b-a64ee343fdd7</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.1e446a8b-6a77-4c2a-8f3e-c196734f0b4c</errorHandlerItemId>
            <guid>guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:1364</guid>
            <versionId>4a2f9bfd-1741-4347-9a9a-69d30715ecaa</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="418" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3edf</errorHandlerItem>
                <errorHandlerItemId>2025.1e446a8b-6a77-4c2a-8f3e-c196734f0b4c</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.0030f588-5eb9-4c96-ae3b-a64ee343fdd7</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.IDCRequest.DBID = tw.local.results[0].DBID;&#xD;
&#xD;
tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var j = 0&#xD;
function paramInit (type,value) {&#xD;
	tw.local.sqlStatements[j].parameters[tw.local.i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[tw.local.i].type = type;&#xD;
	tw.local.sqlStatements[j].parameters[tw.local.i].value = value;&#xD;
	tw.local.i= tw.local.i+1;&#xD;
}&#xD;
//-----------------------------invoice--------------------------------------------------&#xD;
&#xD;
for (j=0; j&lt;tw.local.IDCRequest.invoices.listLength; j++) {&#xD;
	tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
	tw.local.sqlStatements[j].sql = "INSERT INTO BPM.IDC_REQUEST_INVOICES (IDC_REQUEST_ID, INVOICE_NUMBER,INVOICE_DATE) VALUES (?,?,?) ;";&#xD;
	&#xD;
	tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
	tw.local.i = 0;&#xD;
	&#xD;
	paramInit("INTEGER",tw.local.IDCRequest.DBID);&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.invoices[j].number);&#xD;
	paramInit("DATE",tw.local.IDCRequest.invoices[j].date);&#xD;
}&#xD;
&#xD;
////-----------------------------customer---------------------------------------------------&#xD;
//j+=1;&#xD;
	tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
	tw.local.sqlStatements[j].sql = "INSERT INTO BPM.IDC_CUSTOMER_INFORMATION (IDC_REQUEST_ID, CIF, CUSTOMER_NAME, CUSTOMER_SECTOR, CUSTOMER_TYPE, CUSTOMER_NUMBER_AT_CBE, FACILITY_TYPE_ID, COMMERCIAL_REGISTRATION_NUMBER, COMMERCIAL_REGISTRATION_OFFICE, TAX_CARD_NUMBER, IMPORT_CARD_NUMBER) VALUES (?,?,?,?,?,?,?,?,?,?,?) ;";&#xD;
	&#xD;
	tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
	tw.local.i = 0;&#xD;
	&#xD;
	paramInit("INTEGER",tw.local.IDCRequest.DBID);&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.CIFNumber);&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.customerName);&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.customerSector);//can be update later &lt;----------------------------------------------------&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.customerType);&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.CBENumber);&#xD;
	paramInit("INTEGER",tw.local.IDCRequest.customerInformation.facilityType.id);&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.commercialRegistrationNumber);&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.commercialRegistrationOffice);&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.taxCardNumber);&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.importCardNumber);&#xD;
//-----------------------------bill-----------------------------------------------------&#xD;
var n = 0;&#xD;
j++;&#xD;
for (; j&lt;(tw.local.IDCRequest.billOfLading.listLength + tw.local.IDCRequest.invoices.listLength+1); j++) {&#xD;
	tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
	tw.local.sqlStatements[j].sql = "INSERT INTO BPM.IDC_REQUEST_BILLS (IDC_REQUEST_ID, BILL_OF_LADING_REF, BILL_OF_LADING_DATE) VALUES (?,?,?) ;";&#xD;
	&#xD;
	tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
	tw.local.i = 0;&#xD;
	&#xD;
	paramInit("INTEGER",tw.local.IDCRequest.DBID);&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.billOfLading[n].number);&#xD;
	paramInit("DATE",tw.local.IDCRequest.billOfLading[n].date);&#xD;
	n+=1;&#xD;
}&#xD;
//-----------------------------------------facility-----------------------------------------------------------&#xD;
&#xD;
if (tw.local.idcContract.facilities.listLength &gt; 0) {&#xD;
	for (var m_f_len=0; m_f_len &lt; tw.local.idcContract.facilities.listLength; m_f_len++) {&#xD;
		for (var s_f_len=0; s_f_len &lt; tw.local.idcContract.facilities[m_f_len].facilityLines.listLength; s_f_len++) {&#xD;
			tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
			tw.local.sqlStatements[j].sql = "INSERT INTO BPM.IDC_FACILITY_DETAILS (IDC_REQUEST_ID, FACILITY_ID, CURRENCY, LINE_SERIAL, LINE_CODE, FACILITY_PERCENTAGE_TO_BOOK) VALUES (?,?,?,?,?,?) ;";&#xD;
			&#xD;
			tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
			tw.local.i = 0;&#xD;
			paramInit("INTEGER",tw.local.IDCRequest.DBID);&#xD;
			paramInit("VARCHAR",tw.local.idcContract.facilities[m_f_len].facilityID);&#xD;
			paramInit("VARCHAR",tw.local.idcContract.facilities[m_f_len].facilityCurrency.value);&#xD;
			paramInit("VARCHAR",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].lineSerialNumber);&#xD;
			paramInit("VARCHAR",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].lineCode);&#xD;
			paramInit("DECIMAL",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].facilityPercentageToBook);&#xD;
			j++;&#xD;
		}&#xD;
	}&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>b489b65c-ef10-4dd6-9397-bda044f1f989</guid>
                <versionId>08f470b8-0b74-40ea-9be8-5180ee1e5a97</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.afab2391-7c99-4985-8f1a-ebb8e6aa072a</processItemId>
            <processId>1.06eaabde-33db-480a-9d65-982fa27c2eac</processId>
            <name>Insert IDC SQL Query</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.e638cea2-d9b3-4b0e-8f51-9cf50b931659</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.1e446a8b-6a77-4c2a-8f3e-c196734f0b4c</errorHandlerItemId>
            <guid>guid:7ffed11b1cbfb184:129346e9:189b62f5bd4:-3a5e</guid>
            <versionId>75df9908-2e2c-4dd3-8ed9-356b708ae1fc</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.5596b7d7-1661-4b84-a569-c8bc95ee2368</processItemPrePostId>
                <processItemId>2025.afab2391-7c99-4985-8f1a-ebb8e6aa072a</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>7c7cd267-cba2-4417-9591-a92770ff54f7</guid>
                <versionId>1f967d6b-be91-4d32-9b5e-028470d48364</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.4ce641d6-7bac-4f91-bf3b-cdd482f59da3</processItemPrePostId>
                <processItemId>2025.afab2391-7c99-4985-8f1a-ebb8e6aa072a</processItemId>
                <location>2</location>
                <script isNull="true" />
                <guid>3661f06e-931e-4624-983c-6931382d128f</guid>
                <versionId>2e0b7f4f-d658-43ac-8cf3-f4c8d813ea2e</versionId>
            </processPrePosts>
            <layoutData x="122" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3edf</errorHandlerItem>
                <errorHandlerItemId>2025.1e446a8b-6a77-4c2a-8f3e-c196734f0b4c</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftTop" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.e638cea2-d9b3-4b0e-8f51-9cf50b931659</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
tw.local.sqlStatements[0] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[0].sql = "INSERT INTO BPM.IDC_REQUEST_DETAILS (REQUEST_NUMBER, PARENT_REQUEST_NUMBER, BPM_INSTANCE_NUMBER, REQUEST_INITIATOR, REQUEST_BRANCH_HUB, REQUEST_NATURE_ID, REQUEST_TYPE_ID, REQUEST_DATE, REQUEST_STATE, REQUEST_STAGE, REQUEST_STATUS, REQUEST_SUB_STATUS, FLEX_CUBE_CONTRACT_NUMBER, IS_WITHDRAWEN, IMPORT_PURPOSE_ID, PAYMENT_TERMS_ID, DOCUMENTS_SOURCE_ID, PRODUCT_CATEGORY_ID, COMMODITY_DESCRIPTION, ADVANCE_PAYMENT_OUTSTANDING_AMOUNT, DOCUMENT_AMOUNT, CURRENCY, CHARGES_ACCOUNT, SOURCE_OF_FOREIGN_CURRENCY_ID, SOURCE_OF_FUNDS_ID, PAYMENT_ACCOUNT, CASH_AMOUNT_DOCUMENT_CURRENCY, CASH_AMOUNT_NO_CURRENCY, FACILITY_AMOUNT_DOCUMENT_CURRENCY, FACILITY_AMOUNT_NO_CURRENCY, DISCOUNT, AMOUNT_ADVANCED, AMOUNT_PAID_BY_OTHER_BANKS, AMOUNT_SIGHT, AMOUNT_DEFERRED_NO_AVALIZATION, AMOUNT_DEFERRED_AVALIZATION, AMOUNT_PAYABLE_BY_NBE, FIRST_INSTALLEMENT_MATURITY_DATE, NO_OF_DAYS_TILL_MATURITY, BENEFICIARY_NAME, BENEFICIARY_BANK, BENEFICIARY_IBAN, BENEFICIARY_COUNTRY_CODE, CORRESPONDENT_REF_NUM, TRADE_FO_REFERENCE_NUMBER, TRADE_FU_APPROVAL_NO, EXECUTION_HUB_CODE, EXECUTION_HUB_NAME, SHIPPING_DATE, INCOTERMS, SHIPMENT_METHOD_ID, DESTINATION_PORT, CBE_COMMODITY_CLASSIFICATION_ID, HS_CODE, HS_DESCRIPTION, ACID, COUNTRY_OF_ORIGIN_CODE) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";&#xD;
&#xD;
tw.local.sqlStatements[0].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.i = 0;&#xD;
function paramInit (type,value) {&#xD;
	tw.local.sqlStatements[0].parameters[tw.local.i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[0].parameters[tw.local.i].type = type;&#xD;
	tw.local.sqlStatements[0].parameters[tw.local.i].value = value;&#xD;
//	tw.local.sqlStatements[0].parameters[tw.local.i].mode = "IN";&#xD;
	tw.local.i= tw.local.i+1;&#xD;
//	return tw.local.parameter;&#xD;
}&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.appInfo.instanceID);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.ParentIDCRequestNumber);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.appInfo.appID);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.appInfo.initiator);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.appInfo.branch.name);&#xD;
paramInit ("INTEGER",tw.local.IDCRequest.IDCRequestNature.id);&#xD;
paramInit ("INTEGER",tw.local.IDCRequest.IDCRequestType.id);&#xD;
paramInit ("DATE",tw.local.IDCRequest.requestDate);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.IDCRequestState);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.IDCRequestStage);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.appInfo.status);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.appInfo.subStatus);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.FCContractNumber);&#xD;
if (tw.local.IDCRequest.isIDCWithdrawn == true) {&#xD;
	paramInit ("INTEGER",1);&#xD;
	&#xD;
}else{&#xD;
	paramInit ("INTEGER",0);&#xD;
	&#xD;
}&#xD;
paramInit ("INTEGER",tw.local.IDCRequest.importPurpose.id);&#xD;
paramInit ("INTEGER",tw.local.IDCRequest.paymentTerms.id);&#xD;
paramInit ("INTEGER",tw.local.IDCRequest.documentsSource.id);&#xD;
paramInit ("INTEGER",tw.local.IDCRequest.productCategory.id);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.commodityDescription);&#xD;
if (tw.local.IDCRequest.IDCRequestType.englishdescription == "Advance Payment") {&#xD;
	paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.amtPayableByNBE);&#xD;
	&#xD;
}else{&#xD;
	paramInit ("DECIMAL",0.0);&#xD;
	&#xD;
}&#xD;
paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.documentAmount);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.documentCurrency.code);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.chargesAccount);&#xD;
paramInit ("INTEGER",tw.local.IDCRequest.financialDetails.sourceOfForeignCurrency.id);&#xD;
paramInit ("INTEGER",tw.local.IDCRequest.financialDetails.sourceOfFunds.id);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.paymentAccount);&#xD;
paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.cashAmtInDocCurrency);&#xD;
paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.CashAmtWithNoCurrency);&#xD;
paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.facilityAmtInDocCurrency);&#xD;
paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.facilityAmtWithNoCurrency);&#xD;
paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.discountAmt);&#xD;
paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.amountAdvanced);&#xD;
paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.amtPaidbyOtherBanks);&#xD;
paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.amtSight);&#xD;
paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.amtDeferredNoAvalized);&#xD;
paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.amtDeferredAvalized);&#xD;
paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.amtPayableByNBE);&#xD;
paramInit ("DATE",tw.local.IDCRequest.financialDetails.firstInstallementMaturityDate);&#xD;
paramInit ("INTEGER",tw.local.IDCRequest.financialDetails.daysTillMaturity);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.beneficiaryDetails.name);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.beneficiaryDetails.bank);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.beneficiaryDetails.account);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.beneficiaryDetails.country.code);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.beneficiaryDetails.correspondentRefNum);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.tradeFOReferenceNumber);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.tradeFinanceApprovalNumber);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.executionHub.code);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.executionHub.arabicdescription);&#xD;
paramInit ("DATE",tw.local.IDCRequest.productsDetails.shippingDate);&#xD;
if (tw.local.IDCRequest.productsDetails.incoterms.id == 0) {&#xD;
	paramInit ("INTEGER",null);&#xD;
}else{&#xD;
	paramInit ("INTEGER",tw.local.IDCRequest.productsDetails.incoterms.id);&#xD;
}&#xD;
&#xD;
if (tw.local.IDCRequest.productsDetails.shipmentMethod.id == 0) {&#xD;
	paramInit ("INTEGER",null);&#xD;
}else{&#xD;
	paramInit ("INTEGER",tw.local.IDCRequest.productsDetails.shipmentMethod.id);&#xD;
}&#xD;
&#xD;
&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.productsDetails.destinationPort);&#xD;
if (tw.local.IDCRequest.productsDetails.CBECommodityClassification.id == 0) {&#xD;
	paramInit ("INTEGER",null);&#xD;
}else{&#xD;
	paramInit ("INTEGER",tw.local.IDCRequest.productsDetails.CBECommodityClassification.id);&#xD;
	}&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.productsDetails.HSProduct.code);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.productsDetails.HSProduct.englishdescription);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.productsDetails.ACID);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.countryOfOrigin.code);&#xD;
//-------------------------------------------------------------------------------------------&#xD;
tw.local.sqlStatements[1] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[1].sql = "SELECT ID DBID FROM BPM.IDC_REQUEST_DETAILS WHERE REQUEST_NUMBER = ?";&#xD;
tw.local.sqlStatements[1].parameters = new tw.object.listOf.SQLParameter();&#xD;
&#xD;
tw.local.sqlStatements[1].parameters[0] = new tw.object.SQLParameter();&#xD;
tw.local.sqlStatements[1].parameters[0].type = "VARCHAR";&#xD;
tw.local.sqlStatements[1].parameters[0].value = tw.local.IDCRequest.appInfo.instanceID;&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>240fd2e8-09a6-470c-8f31-798a1c99248b</guid>
                <versionId>f1e19d59-d626-4deb-8532-07fe22c36756</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.7e5a39e8-986f-4c70-88eb-bfdd7e4bf7dd</processItemId>
            <processId>1.06eaabde-33db-480a-9d65-982fa27c2eac</processId>
            <name>SQL Execute Multiple SubStatements</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.4f75e554-0e0e-47c0-a06f-c94332a52bbf</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.1e446a8b-6a77-4c2a-8f3e-c196734f0b4c</errorHandlerItemId>
            <guid>guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:778</guid>
            <versionId>8c8d73f2-7e6c-4ff5-a149-80625d5b68b0</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="554" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error3</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3edf</errorHandlerItem>
                <errorHandlerItemId>2025.1e446a8b-6a77-4c2a-8f3e-c196734f0b4c</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightTop" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.4f75e554-0e0e-47c0-a06f-c94332a52bbf</subProcessId>
                <attachedProcessRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/1.89ee1e72-5c6b-44ed-9e55-158a6cb613af</attachedProcessRef>
                <guid>2b2e7df4-c3af-4fd7-a722-66fd79af7902</guid>
                <versionId>543469b9-f3a3-4aa8-8187-1f2fe25f5703</versionId>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.70e54f82-cf0d-4271-999b-c7c77d50b89e</parameterMappingId>
                    <processParameterId>2055.c007e1f5-b9cb-4b02-bf74-bc982112aade</processParameterId>
                    <parameterMappingParentId>3012.4f75e554-0e0e-47c0-a06f-c94332a52bbf</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>cbe5712d-ad38-4343-8a48-70e75581e4da</guid>
                    <versionId>04022b28-9cc1-40d0-853c-aef82ddff217</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sqlStatements">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8170e184-aca9-417f-9ad4-f22b3cc8fb3d</parameterMappingId>
                    <processParameterId>2055.c023a0b3-e6e5-47a6-a920-04d6b2f8dc26</processParameterId>
                    <parameterMappingParentId>3012.4f75e554-0e0e-47c0-a06f-c94332a52bbf</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlStatements</value>
                    <classRef>/12.3990d8b4-983d-4250-bd06-3600f527fed0</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>2ae5332f-f4f2-46c3-9f9c-10376fd3a1bb</guid>
                    <versionId>1364cfdc-3581-4199-9f06-af186f3467cb</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="returnType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.07a1866b-cfd1-414a-a234-74fefa4a0620</parameterMappingId>
                    <processParameterId>2055.628ceac6-aa42-426b-97c7-540674f12f38</processParameterId>
                    <parameterMappingParentId>3012.4f75e554-0e0e-47c0-a06f-c94332a52bbf</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"Record"</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>3335c5eb-cebd-4910-b36a-2b96d6c6b80d</guid>
                    <versionId>bd1d84ac-02da-433a-80d8-26eb48361d14</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.42782b00-854b-4c17-ae67-b2d580703d43</parameterMappingId>
                    <processParameterId>2055.24961b69-3b9b-4311-8707-6b6dfffaf207</processParameterId>
                    <parameterMappingParentId>3012.4f75e554-0e0e-47c0-a06f-c94332a52bbf</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>27bbdb27-a9da-4d4c-93f3-fa8ee643df62</guid>
                    <versionId>f1ff3bb9-8b7e-4e65-8d5f-2ae0475f1cfc</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.e8b6e7e2-5ab2-43bc-82c7-0fc1742374f8</processItemId>
            <processId>1.06eaabde-33db-480a-9d65-982fa27c2eac</processId>
            <name>SQL Execute Multiple Statements</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.e7e00bb4-e020-451a-9e1c-b039e76277a9</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.1e446a8b-6a77-4c2a-8f3e-c196734f0b4c</errorHandlerItemId>
            <guid>guid:7ffed11b1cbfb184:129346e9:189b62f5bd4:-1967</guid>
            <versionId>9300a58e-43ac-4270-b01a-54bcb6a6cb83</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="266" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error4</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:3edf</errorHandlerItem>
                <errorHandlerItemId>2025.1e446a8b-6a77-4c2a-8f3e-c196734f0b4c</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topLeft" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.e7e00bb4-e020-451a-9e1c-b039e76277a9</subProcessId>
                <attachedProcessRef>4eb39989-c5fb-47b4-9127-ad1aa4599590/1.89ee1e72-5c6b-44ed-9e55-158a6cb613af</attachedProcessRef>
                <guid>dc3b6e0e-9fb6-4607-806f-8d3f6625e2f2</guid>
                <versionId>048fd439-18a4-4c84-a861-7f124934f3b6</versionId>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.0ed93781-4cf9-4b98-a25c-5df99b64f79f</parameterMappingId>
                    <processParameterId>2055.c007e1f5-b9cb-4b02-bf74-bc982112aade</processParameterId>
                    <parameterMappingParentId>3012.e7e00bb4-e020-451a-9e1c-b039e76277a9</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>c46418f3-6ae0-4500-bdab-b499c767419f</guid>
                    <versionId>3504c4bb-af08-4d0b-8022-bff806ab85ed</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sqlStatements">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.cc7c1eaf-23af-4129-94e3-f7336c00b671</parameterMappingId>
                    <processParameterId>2055.c023a0b3-e6e5-47a6-a920-04d6b2f8dc26</processParameterId>
                    <parameterMappingParentId>3012.e7e00bb4-e020-451a-9e1c-b039e76277a9</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlStatements</value>
                    <classRef>/12.3990d8b4-983d-4250-bd06-3600f527fed0</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>4b04c853-f066-41ef-b449-61d1df8895f8</guid>
                    <versionId>9ccd3714-693f-46f6-9872-ee5712c07465</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="returnType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.cc951237-b8e0-4c01-9b4f-d7c57de4c444</parameterMappingId>
                    <processParameterId>2055.628ceac6-aa42-426b-97c7-540674f12f38</processParameterId>
                    <parameterMappingParentId>3012.e7e00bb4-e020-451a-9e1c-b039e76277a9</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"Record"</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>bd4f9794-ba57-4d5f-902a-2cf1701ef416</guid>
                    <versionId>a6333cb5-ba32-484e-a1b5-8b11ed616d52</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e8bd6a2e-efad-4e17-947a-423042113cb1</parameterMappingId>
                    <processParameterId>2055.24961b69-3b9b-4311-8707-6b6dfffaf207</processParameterId>
                    <parameterMappingParentId>3012.e7e00bb4-e020-451a-9e1c-b039e76277a9</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>93f5b40a-e241-4d20-b106-f69145c9ff98</guid>
                    <versionId>e9b85f77-b12e-406e-b6a1-639fd6e180dd</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3de76605-8063-4595-8e96-64c6ff6b5943</processItemId>
            <processId>1.06eaabde-33db-480a-9d65-982fa27c2eac</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.64a82438-8b23-4f6d-bd95-06ae8f8f31bc</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7ffed11b1cbfb184:129346e9:189b62f5bd4:-3ac1</guid>
            <versionId>acece4b3-312d-43c6-82a7-b254fa815c39</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="730" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.64a82438-8b23-4f6d-bd95-06ae8f8f31bc</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>0e0c33e6-f14c-4158-a870-b7d6782b1dfc</guid>
                <versionId>530690d7-212b-43f0-bc04-e76d4d5a51a0</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.afab2391-7c99-4985-8f1a-ebb8e6aa072a</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Insert IDC Request" id="1.06eaabde-33db-480a-9d65-982fa27c2eac" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="IDCRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.6287bfa2-c4da-4ec3-842c-a164e045a461">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">var autoObject = new tw.object.IDCRequest();
autoObject.IDCRequestState = "Final";
autoObject.commodityDescription = "commo";
autoObject.countryOfOrigin = new tw.object.DBLookup();
//autoObject.countryOfOrigin.id = 1;
//autoObject.countryOfOrigin.code = "EG";
//autoObject.countryOfOrigin.arabicdescription = "مصر";
//autoObject.countryOfOrigin.englishdescription = "ُEgypt";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "12-08-2023";
autoObject.appInfo.status = "status";
autoObject.appInfo.subStatus = "subStatus";
autoObject.appInfo.initiator = "initiator";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "001";
autoObject.appInfo.branch.value = "001";
autoObject.appInfo.requestName = "ICAP";
autoObject.appInfo.requestType = "ICAP";
autoObject.appInfo.stepName = "stepName";
autoObject.appInfo.appRef = "appRef";
autoObject.appInfo.appID = "85634";
autoObject.appInfo.instanceID = "00102230000220";
autoObject.productsDetails = new tw.object.ProductsDetails();
autoObject.productsDetails.destinationPort = "destinationPort";
autoObject.productsDetails.shippingDate = new TWDate();
autoObject.productsDetails.HSProduct = new tw.object.DBLookup();
autoObject.productsDetails.HSProduct.id = 1;
autoObject.productsDetails.HSProduct.code = "0101 – 0106";
autoObject.productsDetails.HSProduct.arabicdescription = "arabic";
autoObject.productsDetails.HSProduct.englishdescription = "Live animals";
autoObject.productsDetails.incoterms = new tw.object.DBLookup();
autoObject.productsDetails.incoterms.id = 2;
autoObject.productsDetails.incoterms.code = "FCA";
autoObject.productsDetails.incoterms.arabicdescription = "arabicdon";
autoObject.productsDetails.incoterms.englishdescription = "Free to Carrier";
autoObject.productsDetails.ACID = "ACID";
autoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();
autoObject.productsDetails.CBECommodityClassification.id = 3;
autoObject.productsDetails.CBECommodityClassification.code = "003";
autoObject.productsDetails.CBECommodityClassification.arabicdescription = "الالات و المعدات";
autoObject.productsDetails.CBECommodityClassification.englishdescription = "english";
autoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();
autoObject.productsDetails.shipmentMethod.id = 4;
autoObject.productsDetails.shipmentMethod.code = "004";
autoObject.productsDetails.shipmentMethod.arabicdescription = "بريد سريع";
autoObject.productsDetails.shipmentMethod.englishdescription = "english";
autoObject.financialDetails = new tw.object.FinancialDetails();
autoObject.financialDetails.isAdvancePaymentsUsed = false;
autoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();
autoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();
autoObject.financialDetails.paymentTerms[0].installmentAmount = 1.1;
autoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();
autoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency =1.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id =1;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = "EGP";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = "جنيه مصرى";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = "Egyptian Pound";
autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated =1.0;
autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = "advance";
autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount =1.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = "invoicer";
autoObject.financialDetails.usedAdvancePayment[0].paidAmount =1.0;
autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = "benefic";
autoObject.financialDetails.usedAdvancePayment[0].documentAmount =1.0;
autoObject.financialDetails.discountAmt =77.0;
autoObject.financialDetails.firstInstallementMaturityDate = new TWDate();
autoObject.financialDetails.facilityAmtInDocCurrency =1.0;
autoObject.financialDetails.amtSight =1.0;
autoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();
autoObject.financialDetails.beneficiaryDetails.account = "account";
autoObject.financialDetails.beneficiaryDetails.bank = "bank";
autoObject.financialDetails.beneficiaryDetails.correspondentRefNum = "corresp";
autoObject.financialDetails.beneficiaryDetails.name = "beneficiary";
autoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();
autoObject.financialDetails.beneficiaryDetails.country.id =1;
autoObject.financialDetails.beneficiaryDetails.country.code = "EG";
autoObject.financialDetails.beneficiaryDetails.country.arabicdescription = "مصر";
autoObject.financialDetails.beneficiaryDetails.country.englishdescription = "Egypt";
autoObject.financialDetails.amtDeferredNoAvalized =1.0;
autoObject.financialDetails.amtPayableByNBE =1.0;
autoObject.financialDetails.executionHub = new tw.object.DBLookup();
autoObject.financialDetails.executionHub.id =1;
autoObject.financialDetails.executionHub.code = "077";
autoObject.financialDetails.executionHub.arabicdescription = "وحدة اعتمادات البرج";
autoObject.financialDetails.executionHub.englishdescription = "english";
autoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfForeignCurrency.id =1;
autoObject.financialDetails.sourceOfForeignCurrency.code = "001";
autoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = "لا يوجد عملة اجنبية";
autoObject.financialDetails.sourceOfForeignCurrency.englishdescription = "No Foreign Currency";
autoObject.financialDetails.facilityAmtWithNoCurrency =1.0;
autoObject.financialDetails.documentCurrency = new tw.object.DBLookup();
autoObject.financialDetails.documentCurrency.id =1;
autoObject.financialDetails.documentCurrency.code = "EGP";
autoObject.financialDetails.documentCurrency.arabicdescription = "جنيه مصرى";
autoObject.financialDetails.documentCurrency.englishdescription = "Egyptian Pound";
autoObject.financialDetails.daysTillMaturity =1;
autoObject.financialDetails.tradeFinanceApprovalNumber = "12345";
autoObject.financialDetails.amountAdvanced =1.0;
autoObject.financialDetails.paymentAccount = "paymentAccount";
autoObject.financialDetails.tradeFOReferenceNumber = "123";
autoObject.financialDetails.documentAmount =1.0;
autoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfFunds.id =1;
autoObject.financialDetails.sourceOfFunds.code = "001";
autoObject.financialDetails.sourceOfFunds.arabicdescription = "موارد العميل الذاتية";
autoObject.financialDetails.sourceOfFunds.englishdescription = "english";
autoObject.financialDetails.chargesAccount = "charges";
autoObject.financialDetails.CashAmtWithNoCurrency =1.0;
autoObject.financialDetails.amtDeferredAvalized =1.0;
autoObject.financialDetails.amtPaidbyOtherBanks =1.0;
autoObject.financialDetails.cashAmtInDocCurrency =1.0;
autoObject.IDCRequestType = new tw.object.DBLookup();
autoObject.IDCRequestType.id =1;
autoObject.IDCRequestType.code = "code";
autoObject.IDCRequestType.arabicdescription = "arabic";
autoObject.IDCRequestType.englishdescription = "english";
autoObject.isIDCWithdrawn = false;
autoObject.IDCRequestStage = "Stage";
autoObject.FCContractNumber = "FC";
autoObject.billOfLading = new tw.object.listOf.Invoice();
autoObject.billOfLading[0] = new tw.object.Invoice();
autoObject.billOfLading[0].date = new TWDate();
autoObject.billOfLading[0].number = "123";&#xD;
autoObject.billOfLading[1] = new tw.object.Invoice();&#xD;
autoObject.billOfLading[1].date = new TWDate();&#xD;
autoObject.billOfLading[1].number = "123";
autoObject.importPurpose = new tw.object.DBLookup();
autoObject.importPurpose.id =1;
autoObject.importPurpose.code = "001";
autoObject.importPurpose.arabicdescription = "استيراد سلعى";
autoObject.importPurpose.englishdescription = "Commodity Import";
autoObject.IDCRequestNature = new tw.object.DBLookup();
autoObject.IDCRequestNature.id =1;
autoObject.IDCRequestNature.code = "1";
autoObject.IDCRequestNature.arabicdescription = "اصدر جديد";
autoObject.IDCRequestNature.englishdescription = "New Request";
autoObject.customerInformation = new tw.object.CustomerInformation();
autoObject.customerInformation.CIFNumber = "02366014";
autoObject.customerInformation.importCardNumber = "import";
autoObject.customerInformation.commercialRegistrationNumber = "commercial";
autoObject.customerInformation.customerName = "customerName";
autoObject.customerInformation.isCustomeSanctionedbyCBE = "";
autoObject.customerInformation.CBENumber = "cbe";
autoObject.customerInformation.customerSector = "rSector";
autoObject.customerInformation.facilityType = new tw.object.DBLookup();&#xD;
autoObject.customerInformation.facilityType.id = 1;&#xD;
autoObject.customerInformation.facilityType.arabicdescription= "لا يتمتع بتسهيلات";
autoObject.customerInformation.commercialRegistrationOffice = "Office";
autoObject.customerInformation.taxCardNumber = "taxCardNumber";
autoObject.customerInformation.customerType = "cType";
autoObject.customerInformation.addressLine1 = "1";
autoObject.customerInformation.addressLine2 = "2";
autoObject.invoices = new tw.object.listOf.Invoice();
autoObject.invoices[0] = new tw.object.Invoice();
autoObject.invoices[0].date = new TWDate();
autoObject.invoices[0].number = "number";&#xD;
autoObject.invoices[1] = new tw.object.Invoice();&#xD;
autoObject.invoices[1].date = new TWDate();&#xD;
autoObject.invoices[1].number = "number2";
autoObject.productCategory = new tw.object.DBLookup();
autoObject.productCategory.id =1;
autoObject.productCategory.code = "001";
autoObject.productCategory.arabicdescription = "arabic";
autoObject.productCategory.englishdescription = "Automotive industry";
autoObject.documentsSource = new tw.object.DBLookup();
autoObject.documentsSource.id =1;
autoObject.documentsSource.code = "001";
autoObject.documentsSource.arabicdescription = "arabic";
autoObject.documentsSource.englishdescription = "Automotive industry";
autoObject.ParentIDCRequestNumber = "Paren";
autoObject.paymentTerms = new tw.object.DBLookup();
autoObject.paymentTerms.id =1;
autoObject.paymentTerms.code = "001";
autoObject.paymentTerms.arabicdescription = "اطلاع";
autoObject.paymentTerms.englishdescription = "Sight";
autoObject.approvals = new tw.object.Approvals();
autoObject.approvals.CAD = false;
autoObject.approvals.treasury = false;
autoObject.approvals.compliance = false;
autoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();
autoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();
autoObject.appLog[0].startTime = new TWDate();
autoObject.appLog[0].endTime = new TWDate();
autoObject.appLog[0].userName = "userName";
autoObject.appLog[0].role = "role";
autoObject.appLog[0].step = "step";
autoObject.appLog[0].action = "action";
autoObject.appLog[0].comment = "comment";
autoObject.appLog[0].terminateReason = "terminateReason";
autoObject.appLog[0].returnReason = "returnReason";
autoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "userName";
autoObject.stepLog.role = "role";
autoObject.stepLog.step = "step";
autoObject.stepLog.action = "action";
autoObject.stepLog.comment = "comment";
autoObject.stepLog.terminateReason = "terminateReason";
autoObject.stepLog.returnReason = "returnReason";
autoObject.DBID =0;
autoObject.requestDate = new TWDate();
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="idcContract" itemSubjectRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1" isCollection="false" id="2055.e4ea1176-3273-4117-87c0-b882c7df46c4" />
                        
                        
                        <ns16:dataOutput name="IDCRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.2fadd4c3-4741-4fce-8d56-2d8b8fbc8ccb" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.5d7794f2-995a-47eb-8a0c-755c73dc5d01" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.6287bfa2-c4da-4ec3-842c-a164e045a461</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.e4ea1176-3273-4117-87c0-b882c7df46c4</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.2fadd4c3-4741-4fce-8d56-2d8b8fbc8ccb</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.5d7794f2-995a-47eb-8a0c-755c73dc5d01</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="b9f68ef0-f3c6-48f7-81d8-add748f37635">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="e7ed2c1d-d1ca-46fd-8747-9ae2bcee0654" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>9d74c8cd-b368-4325-882b-54cc7732f318</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3de76605-8063-4595-8e96-64c6ff6b5943</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>afab2391-7c99-4985-8f1a-ebb8e6aa072a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e8b6e7e2-5ab2-43bc-82c7-0fc1742374f8</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8407a918-9889-424a-888f-1868e8f548b1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>7e5a39e8-986f-4c70-88eb-bfdd7e4bf7dd</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f64a8cd2-a394-4a23-82ee-b14ea1191de0</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a5aba410-d234-4944-8162-08cd3f682bf8</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ff2e1a37-3640-4c8c-84be-96bbbe494039</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>5eeec8b0-ae9f-4c90-8054-3040b952678f</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1e446a8b-6a77-4c2a-8f3e-c196734f0b4c</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="9d74c8cd-b368-4325-882b-54cc7732f318">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.e99b5990-b1b9-474e-80bf-00d16f480ddc</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="3de76605-8063-4595-8e96-64c6ff6b5943">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="730" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:7ffed11b1cbfb184:129346e9:189b62f5bd4:-3ac1</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>c9503c22-2547-446d-8378-1b5e156396e6</ns16:incoming>
                        
                        
                        <ns16:incoming>a4a86a11-2a26-4804-88f5-56c752e193ca</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="9d74c8cd-b368-4325-882b-54cc7732f318" targetRef="afab2391-7c99-4985-8f1a-ebb8e6aa072a" name="To Insert IDC SQL Query" id="2027.e99b5990-b1b9-474e-80bf-00d16f480ddc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Insert IDC SQL Query" id="afab2391-7c99-4985-8f1a-ebb8e6aa072a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="122" y="57" width="95" height="70" />
                            
                            
                            <ns3:postAssignmentScript />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.e99b5990-b1b9-474e-80bf-00d16f480ddc</ns16:incoming>
                        
                        
                        <ns16:outgoing>a1a1b090-a48b-43c7-8e55-d476b767e65b</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
tw.local.sqlStatements[0] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[0].sql = "INSERT INTO BPM.IDC_REQUEST_DETAILS (REQUEST_NUMBER, PARENT_REQUEST_NUMBER, BPM_INSTANCE_NUMBER, REQUEST_INITIATOR, REQUEST_BRANCH_HUB, REQUEST_NATURE_ID, REQUEST_TYPE_ID, REQUEST_DATE, REQUEST_STATE, REQUEST_STAGE, REQUEST_STATUS, REQUEST_SUB_STATUS, FLEX_CUBE_CONTRACT_NUMBER, IS_WITHDRAWEN, IMPORT_PURPOSE_ID, PAYMENT_TERMS_ID, DOCUMENTS_SOURCE_ID, PRODUCT_CATEGORY_ID, COMMODITY_DESCRIPTION, ADVANCE_PAYMENT_OUTSTANDING_AMOUNT, DOCUMENT_AMOUNT, CURRENCY, CHARGES_ACCOUNT, SOURCE_OF_FOREIGN_CURRENCY_ID, SOURCE_OF_FUNDS_ID, PAYMENT_ACCOUNT, CASH_AMOUNT_DOCUMENT_CURRENCY, CASH_AMOUNT_NO_CURRENCY, FACILITY_AMOUNT_DOCUMENT_CURRENCY, FACILITY_AMOUNT_NO_CURRENCY, DISCOUNT, AMOUNT_ADVANCED, AMOUNT_PAID_BY_OTHER_BANKS, AMOUNT_SIGHT, AMOUNT_DEFERRED_NO_AVALIZATION, AMOUNT_DEFERRED_AVALIZATION, AMOUNT_PAYABLE_BY_NBE, FIRST_INSTALLEMENT_MATURITY_DATE, NO_OF_DAYS_TILL_MATURITY, BENEFICIARY_NAME, BENEFICIARY_BANK, BENEFICIARY_IBAN, BENEFICIARY_COUNTRY_CODE, CORRESPONDENT_REF_NUM, TRADE_FO_REFERENCE_NUMBER, TRADE_FU_APPROVAL_NO, EXECUTION_HUB_CODE, EXECUTION_HUB_NAME, SHIPPING_DATE, INCOTERMS, SHIPMENT_METHOD_ID, DESTINATION_PORT, CBE_COMMODITY_CLASSIFICATION_ID, HS_CODE, HS_DESCRIPTION, ACID, COUNTRY_OF_ORIGIN_CODE) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";&#xD;
&#xD;
tw.local.sqlStatements[0].parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.i = 0;&#xD;
function paramInit (type,value) {&#xD;
	tw.local.sqlStatements[0].parameters[tw.local.i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[0].parameters[tw.local.i].type = type;&#xD;
	tw.local.sqlStatements[0].parameters[tw.local.i].value = value;&#xD;
//	tw.local.sqlStatements[0].parameters[tw.local.i].mode = "IN";&#xD;
	tw.local.i= tw.local.i+1;&#xD;
//	return tw.local.parameter;&#xD;
}&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.appInfo.instanceID);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.ParentIDCRequestNumber);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.appInfo.appID);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.appInfo.initiator);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.appInfo.branch.name);&#xD;
paramInit ("INTEGER",tw.local.IDCRequest.IDCRequestNature.id);&#xD;
paramInit ("INTEGER",tw.local.IDCRequest.IDCRequestType.id);&#xD;
paramInit ("DATE",tw.local.IDCRequest.requestDate);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.IDCRequestState);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.IDCRequestStage);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.appInfo.status);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.appInfo.subStatus);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.FCContractNumber);&#xD;
if (tw.local.IDCRequest.isIDCWithdrawn == true) {&#xD;
	paramInit ("INTEGER",1);&#xD;
	&#xD;
}else{&#xD;
	paramInit ("INTEGER",0);&#xD;
	&#xD;
}&#xD;
paramInit ("INTEGER",tw.local.IDCRequest.importPurpose.id);&#xD;
paramInit ("INTEGER",tw.local.IDCRequest.paymentTerms.id);&#xD;
paramInit ("INTEGER",tw.local.IDCRequest.documentsSource.id);&#xD;
paramInit ("INTEGER",tw.local.IDCRequest.productCategory.id);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.commodityDescription);&#xD;
if (tw.local.IDCRequest.IDCRequestType.englishdescription == "Advance Payment") {&#xD;
	paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.amtPayableByNBE);&#xD;
	&#xD;
}else{&#xD;
	paramInit ("DECIMAL",0.0);&#xD;
	&#xD;
}&#xD;
paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.documentAmount);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.documentCurrency.code);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.chargesAccount);&#xD;
paramInit ("INTEGER",tw.local.IDCRequest.financialDetails.sourceOfForeignCurrency.id);&#xD;
paramInit ("INTEGER",tw.local.IDCRequest.financialDetails.sourceOfFunds.id);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.paymentAccount);&#xD;
paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.cashAmtInDocCurrency);&#xD;
paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.CashAmtWithNoCurrency);&#xD;
paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.facilityAmtInDocCurrency);&#xD;
paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.facilityAmtWithNoCurrency);&#xD;
paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.discountAmt);&#xD;
paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.amountAdvanced);&#xD;
paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.amtPaidbyOtherBanks);&#xD;
paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.amtSight);&#xD;
paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.amtDeferredNoAvalized);&#xD;
paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.amtDeferredAvalized);&#xD;
paramInit ("DECIMAL",tw.local.IDCRequest.financialDetails.amtPayableByNBE);&#xD;
paramInit ("DATE",tw.local.IDCRequest.financialDetails.firstInstallementMaturityDate);&#xD;
paramInit ("INTEGER",tw.local.IDCRequest.financialDetails.daysTillMaturity);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.beneficiaryDetails.name);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.beneficiaryDetails.bank);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.beneficiaryDetails.account);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.beneficiaryDetails.country.code);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.beneficiaryDetails.correspondentRefNum);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.tradeFOReferenceNumber);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.tradeFinanceApprovalNumber);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.executionHub.code);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.financialDetails.executionHub.arabicdescription);&#xD;
paramInit ("DATE",tw.local.IDCRequest.productsDetails.shippingDate);&#xD;
if (tw.local.IDCRequest.productsDetails.incoterms.id == 0) {&#xD;
	paramInit ("INTEGER",null);&#xD;
}else{&#xD;
	paramInit ("INTEGER",tw.local.IDCRequest.productsDetails.incoterms.id);&#xD;
}&#xD;
&#xD;
if (tw.local.IDCRequest.productsDetails.shipmentMethod.id == 0) {&#xD;
	paramInit ("INTEGER",null);&#xD;
}else{&#xD;
	paramInit ("INTEGER",tw.local.IDCRequest.productsDetails.shipmentMethod.id);&#xD;
}&#xD;
&#xD;
&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.productsDetails.destinationPort);&#xD;
if (tw.local.IDCRequest.productsDetails.CBECommodityClassification.id == 0) {&#xD;
	paramInit ("INTEGER",null);&#xD;
}else{&#xD;
	paramInit ("INTEGER",tw.local.IDCRequest.productsDetails.CBECommodityClassification.id);&#xD;
	}&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.productsDetails.HSProduct.code);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.productsDetails.HSProduct.englishdescription);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.productsDetails.ACID);&#xD;
paramInit ("VARCHAR",tw.local.IDCRequest.countryOfOrigin.code);&#xD;
//-------------------------------------------------------------------------------------------&#xD;
tw.local.sqlStatements[1] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[1].sql = "SELECT ID DBID FROM BPM.IDC_REQUEST_DETAILS WHERE REQUEST_NUMBER = ?";&#xD;
tw.local.sqlStatements[1].parameters = new tw.object.listOf.SQLParameter();&#xD;
&#xD;
tw.local.sqlStatements[1].parameters[0] = new tw.object.SQLParameter();&#xD;
tw.local.sqlStatements[1].parameters[0].type = "VARCHAR";&#xD;
tw.local.sqlStatements[1].parameters[0].value = tw.local.IDCRequest.appInfo.instanceID;&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="afab2391-7c99-4985-8f1a-ebb8e6aa072a" targetRef="e8b6e7e2-5ab2-43bc-82c7-0fc1742374f8" name="To Service" id="a1a1b090-a48b-43c7-8e55-d476b767e65b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.89ee1e72-5c6b-44ed-9e55-158a6cb613af" name="SQL Execute Multiple Statements" id="e8b6e7e2-5ab2-43bc-82c7-0fc1742374f8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="266" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>a1a1b090-a48b-43c7-8e55-d476b767e65b</ns16:incoming>
                        
                        
                        <ns16:outgoing>90fadd6c-9ce4-4ef0-8fbe-c7671842b276</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.c023a0b3-e6e5-47a6-a920-04d6b2f8dc26</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0">tw.local.sqlStatements</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.c007e1f5-b9cb-4b02-bf74-bc982112aade</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.628ceac6-aa42-426b-97c7-540674f12f38</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Record"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.24961b69-3b9b-4311-8707-6b6dfffaf207</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="e8b6e7e2-5ab2-43bc-82c7-0fc1742374f8" targetRef="8407a918-9889-424a-888f-1868e8f548b1" name="To Insert Subtypes SQL Queries" id="90fadd6c-9ce4-4ef0-8fbe-c7671842b276">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:a93dd992dce2d4cc:-381cc70d:115a5e00b05:-7fdb</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0" isCollection="true" name="sqlStatements" id="2056.2ed2ec9d-3a30-4d5e-88fc-a478f50a01cf" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="true" name="results" id="2056.58801473-2b62-4ec7-8fc2-c418250c60e3" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c" isCollection="true" name="parameter" id="2056.1128deb1-0960-4884-812e-9d92dfcbb40f" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" name="i" id="2056.77d9af18-6d7e-4aec-8ea8-d2c8ef142df9" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Insert Subtypes SQL Queries" id="8407a918-9889-424a-888f-1868e8f548b1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="418" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>90fadd6c-9ce4-4ef0-8fbe-c7671842b276</ns16:incoming>
                        
                        
                        <ns16:outgoing>1cc559b9-e906-463a-8c03-e1f39297e2ef</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.IDCRequest.DBID = tw.local.results[0].DBID;&#xD;
&#xD;
tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var j = 0&#xD;
function paramInit (type,value) {&#xD;
	tw.local.sqlStatements[j].parameters[tw.local.i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[tw.local.i].type = type;&#xD;
	tw.local.sqlStatements[j].parameters[tw.local.i].value = value;&#xD;
	tw.local.i= tw.local.i+1;&#xD;
}&#xD;
//-----------------------------invoice--------------------------------------------------&#xD;
&#xD;
for (j=0; j&lt;tw.local.IDCRequest.invoices.listLength; j++) {&#xD;
	tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
	tw.local.sqlStatements[j].sql = "INSERT INTO BPM.IDC_REQUEST_INVOICES (IDC_REQUEST_ID, INVOICE_NUMBER,INVOICE_DATE) VALUES (?,?,?) ;";&#xD;
	&#xD;
	tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
	tw.local.i = 0;&#xD;
	&#xD;
	paramInit("INTEGER",tw.local.IDCRequest.DBID);&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.invoices[j].number);&#xD;
	paramInit("DATE",tw.local.IDCRequest.invoices[j].date);&#xD;
}&#xD;
&#xD;
////-----------------------------customer---------------------------------------------------&#xD;
//j+=1;&#xD;
	tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
	tw.local.sqlStatements[j].sql = "INSERT INTO BPM.IDC_CUSTOMER_INFORMATION (IDC_REQUEST_ID, CIF, CUSTOMER_NAME, CUSTOMER_SECTOR, CUSTOMER_TYPE, CUSTOMER_NUMBER_AT_CBE, FACILITY_TYPE_ID, COMMERCIAL_REGISTRATION_NUMBER, COMMERCIAL_REGISTRATION_OFFICE, TAX_CARD_NUMBER, IMPORT_CARD_NUMBER) VALUES (?,?,?,?,?,?,?,?,?,?,?) ;";&#xD;
	&#xD;
	tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
	tw.local.i = 0;&#xD;
	&#xD;
	paramInit("INTEGER",tw.local.IDCRequest.DBID);&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.CIFNumber);&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.customerName);&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.customerSector);//can be update later &lt;----------------------------------------------------&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.customerType);&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.CBENumber);&#xD;
	paramInit("INTEGER",tw.local.IDCRequest.customerInformation.facilityType.id);&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.commercialRegistrationNumber);&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.commercialRegistrationOffice);&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.taxCardNumber);&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.customerInformation.importCardNumber);&#xD;
//-----------------------------bill-----------------------------------------------------&#xD;
var n = 0;&#xD;
j++;&#xD;
for (; j&lt;(tw.local.IDCRequest.billOfLading.listLength + tw.local.IDCRequest.invoices.listLength+1); j++) {&#xD;
	tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
	tw.local.sqlStatements[j].sql = "INSERT INTO BPM.IDC_REQUEST_BILLS (IDC_REQUEST_ID, BILL_OF_LADING_REF, BILL_OF_LADING_DATE) VALUES (?,?,?) ;";&#xD;
	&#xD;
	tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
	tw.local.i = 0;&#xD;
	&#xD;
	paramInit("INTEGER",tw.local.IDCRequest.DBID);&#xD;
	paramInit("VARCHAR",tw.local.IDCRequest.billOfLading[n].number);&#xD;
	paramInit("DATE",tw.local.IDCRequest.billOfLading[n].date);&#xD;
	n+=1;&#xD;
}&#xD;
//-----------------------------------------facility-----------------------------------------------------------&#xD;
&#xD;
if (tw.local.idcContract.facilities.listLength &gt; 0) {&#xD;
	for (var m_f_len=0; m_f_len &lt; tw.local.idcContract.facilities.listLength; m_f_len++) {&#xD;
		for (var s_f_len=0; s_f_len &lt; tw.local.idcContract.facilities[m_f_len].facilityLines.listLength; s_f_len++) {&#xD;
			tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
			tw.local.sqlStatements[j].sql = "INSERT INTO BPM.IDC_FACILITY_DETAILS (IDC_REQUEST_ID, FACILITY_ID, CURRENCY, LINE_SERIAL, LINE_CODE, FACILITY_PERCENTAGE_TO_BOOK) VALUES (?,?,?,?,?,?) ;";&#xD;
			&#xD;
			tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
			tw.local.i = 0;&#xD;
			paramInit("INTEGER",tw.local.IDCRequest.DBID);&#xD;
			paramInit("VARCHAR",tw.local.idcContract.facilities[m_f_len].facilityID);&#xD;
			paramInit("VARCHAR",tw.local.idcContract.facilities[m_f_len].facilityCurrency.value);&#xD;
			paramInit("VARCHAR",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].lineSerialNumber);&#xD;
			paramInit("VARCHAR",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].lineCode);&#xD;
			paramInit("DECIMAL",tw.local.idcContract.facilities[m_f_len].facilityLines[s_f_len].facilityPercentageToBook);&#xD;
			j++;&#xD;
		}&#xD;
	}&#xD;
}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="8407a918-9889-424a-888f-1868e8f548b1" targetRef="7e5a39e8-986f-4c70-88eb-bfdd7e4bf7dd" name="To SQL Execute Statement" id="1cc559b9-e906-463a-8c03-e1f39297e2ef">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.89ee1e72-5c6b-44ed-9e55-158a6cb613af" isForCompensation="false" startQuantity="1" completionQuantity="1" name="SQL Execute Multiple SubStatements" id="7e5a39e8-986f-4c70-88eb-bfdd7e4bf7dd">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="554" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>1cc559b9-e906-463a-8c03-e1f39297e2ef</ns16:incoming>
                        
                        
                        <ns16:outgoing>c9503c22-2547-446d-8378-1b5e156396e6</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.c023a0b3-e6e5-47a6-a920-04d6b2f8dc26</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0">tw.local.sqlStatements</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.c007e1f5-b9cb-4b02-bf74-bc982112aade</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.628ceac6-aa42-426b-97c7-540674f12f38</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Record"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.24961b69-3b9b-4311-8707-6b6dfffaf207</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="7e5a39e8-986f-4c70-88eb-bfdd7e4bf7dd" targetRef="3de76605-8063-4595-8e96-64c6ff6b5943" name="To End" id="c9503c22-2547-446d-8378-1b5e156396e6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:a93dd992dce2d4cc:-381cc70d:115a5e00b05:-7fdb</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="8407a918-9889-424a-888f-1868e8f548b1" parallelMultiple="false" name="Error2" id="f64a8cd2-a394-4a23-82ee-b14ea1191de0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="453" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>abc25256-be94-4f29-867f-81c649c93002</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2d532afd-0077-4918-8158-94387ae83097" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="965516d6-3b07-4b8c-87af-189546741935" eventImplId="86b0f24e-c15a-4c03-8b50-c13c441b81b3">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="7e5a39e8-986f-4c70-88eb-bfdd7e4bf7dd" parallelMultiple="false" name="Error3" id="a5aba410-d234-4944-8162-08cd3f682bf8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="589" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>3978a550-8f28-420b-8ae8-f999909d5aca</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="b8e62b2d-5aa6-4dba-8207-2fc099671783" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="2e5f4524-2562-4dff-8123-3234062dd264" eventImplId="60822cef-938d-49ea-8826-0916d59bd87c">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="a5aba410-d234-4944-8162-08cd3f682bf8" targetRef="1e446a8b-6a77-4c2a-8f3e-c196734f0b4c" name="To End Event" id="3978a550-8f28-420b-8ae8-f999909d5aca">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightTop</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.c7ff887e-6208-4bf0-8a5e-31bf5cda1e89">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="afab2391-7c99-4985-8f1a-ebb8e6aa072a" parallelMultiple="false" name="Error" id="ff2e1a37-3640-4c8c-84be-96bbbe494039">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="157" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>a4abec46-1e7c-46b7-8157-377781e0bd3d</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="e2aab190-2ec4-48e4-802f-2d1872265439" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="b7822844-da2c-4b93-84e5-9772d0aacdbc" eventImplId="2f98a257-9500-46d4-8b8f-a7a28f5e68e3">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.4ca37604-a434-4b56-8604-141d293722d6" />
                    
                    
                    <ns16:sequenceFlow sourceRef="1e446a8b-6a77-4c2a-8f3e-c196734f0b4c" targetRef="3de76605-8063-4595-8e96-64c6ff6b5943" name="To End" id="a4a86a11-2a26-4804-88f5-56c752e193ca">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="e8b6e7e2-5ab2-43bc-82c7-0fc1742374f8" parallelMultiple="false" name="Error4" id="5eeec8b0-ae9f-4c90-8054-3040b952678f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="301" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>20fd333c-d6e6-44ad-8b39-ff3a11bc2558</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="ddbe9f69-2a1c-489d-8a00-56db4e079ffc" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="50e7391e-a996-46bb-832a-2df4a6eb2218" eventImplId="d24982ef-beef-4d94-8e91-97a4b84a6970">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="5eeec8b0-ae9f-4c90-8054-3040b952678f" targetRef="1e446a8b-6a77-4c2a-8f3e-c196734f0b4c" name="To Script Task" id="20fd333c-d6e6-44ad-8b39-ff3a11bc2558">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="ff2e1a37-3640-4c8c-84be-96bbbe494039" targetRef="1e446a8b-6a77-4c2a-8f3e-c196734f0b4c" name="To Script Task" id="a4abec46-1e7c-46b7-8157-377781e0bd3d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="f64a8cd2-a394-4a23-82ee-b14ea1191de0" targetRef="1e446a8b-6a77-4c2a-8f3e-c196734f0b4c" name="To Script Task" id="abc25256-be94-4f29-867f-81c649c93002">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Catch Errors" id="1e446a8b-6a77-4c2a-8f3e-c196734f0b4c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="446" y="210" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>3978a550-8f28-420b-8ae8-f999909d5aca</ns16:incoming>
                        
                        
                        <ns16:incoming>abc25256-be94-4f29-867f-81c649c93002</ns16:incoming>
                        
                        
                        <ns16:incoming>20fd333c-d6e6-44ad-8b39-ff3a11bc2558</ns16:incoming>
                        
                        
                        <ns16:incoming>a4abec46-1e7c-46b7-8157-377781e0bd3d</ns16:incoming>
                        
                        
                        <ns16:outgoing>a4a86a11-2a26-4804-88f5-56c752e193ca</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error = new tw.object.AjaxError();&#xD;
if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
}&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG+"";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Insert Subtypes SQL Queries">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.90fadd6c-9ce4-4ef0-8fbe-c7671842b276</processLinkId>
            <processId>1.06eaabde-33db-480a-9d65-982fa27c2eac</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.e8b6e7e2-5ab2-43bc-82c7-0fc1742374f8</fromProcessItemId>
            <endStateId>guid:a93dd992dce2d4cc:-381cc70d:115a5e00b05:-7fdb</endStateId>
            <toProcessItemId>2025.8407a918-9889-424a-888f-1868e8f548b1</toProcessItemId>
            <guid>a783a64e-bcc1-4e15-8c62-5bab2353b261</guid>
            <versionId>13c59b90-dcfa-4680-9a0a-1ed26288867c</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.e8b6e7e2-5ab2-43bc-82c7-0fc1742374f8</fromProcessItemId>
            <toProcessItemId>2025.8407a918-9889-424a-888f-1868e8f548b1</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.a4a86a11-2a26-4804-88f5-56c752e193ca</processLinkId>
            <processId>1.06eaabde-33db-480a-9d65-982fa27c2eac</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.1e446a8b-6a77-4c2a-8f3e-c196734f0b4c</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.3de76605-8063-4595-8e96-64c6ff6b5943</toProcessItemId>
            <guid>97f23c67-20c2-4551-a91c-c8062987f54b</guid>
            <versionId>269ddb7c-6146-44c2-a8f7-238c4c7a6396</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.1e446a8b-6a77-4c2a-8f3e-c196734f0b4c</fromProcessItemId>
            <toProcessItemId>2025.3de76605-8063-4595-8e96-64c6ff6b5943</toProcessItemId>
        </link>
        <link name="To SQL Execute Statement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.1cc559b9-e906-463a-8c03-e1f39297e2ef</processLinkId>
            <processId>1.06eaabde-33db-480a-9d65-982fa27c2eac</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8407a918-9889-424a-888f-1868e8f548b1</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.7e5a39e8-986f-4c70-88eb-bfdd7e4bf7dd</toProcessItemId>
            <guid>4901e30d-eb7e-4037-8220-bfce9c376a4d</guid>
            <versionId>5886e23f-778e-41aa-ac45-f717ed7857e7</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.8407a918-9889-424a-888f-1868e8f548b1</fromProcessItemId>
            <toProcessItemId>2025.7e5a39e8-986f-4c70-88eb-bfdd7e4bf7dd</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.c9503c22-2547-446d-8378-1b5e156396e6</processLinkId>
            <processId>1.06eaabde-33db-480a-9d65-982fa27c2eac</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.7e5a39e8-986f-4c70-88eb-bfdd7e4bf7dd</fromProcessItemId>
            <endStateId>guid:a93dd992dce2d4cc:-381cc70d:115a5e00b05:-7fdb</endStateId>
            <toProcessItemId>2025.3de76605-8063-4595-8e96-64c6ff6b5943</toProcessItemId>
            <guid>c71e47fa-99b6-4b25-935e-354b1b30ea0c</guid>
            <versionId>7510e7ac-137e-4e93-8991-90a8d57301f1</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.7e5a39e8-986f-4c70-88eb-bfdd7e4bf7dd</fromProcessItemId>
            <toProcessItemId>2025.3de76605-8063-4595-8e96-64c6ff6b5943</toProcessItemId>
        </link>
        <link name="To Service">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.a1a1b090-a48b-43c7-8e55-d476b767e65b</processLinkId>
            <processId>1.06eaabde-33db-480a-9d65-982fa27c2eac</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.afab2391-7c99-4985-8f1a-ebb8e6aa072a</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.e8b6e7e2-5ab2-43bc-82c7-0fc1742374f8</toProcessItemId>
            <guid>b8a1f9c6-ca47-48c7-9f07-3b4db3576657</guid>
            <versionId>a11e5405-f373-449e-93d5-cc802c9f600c</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.afab2391-7c99-4985-8f1a-ebb8e6aa072a</fromProcessItemId>
            <toProcessItemId>2025.e8b6e7e2-5ab2-43bc-82c7-0fc1742374f8</toProcessItemId>
        </link>
    </process>
</teamworks>

